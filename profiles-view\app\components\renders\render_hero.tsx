'use client';

import { useState, useEffect } from 'react';
import { StickyScrollRevealDemo } from '@/components/ui/sticky-scroll-reveal-demo';
import DecryptedText from '@/components/ui/DecryptedText';

interface HeroContent {
  title: string;
  description: string;
  contentType: 'color' | 'image';
  colorGradient?: string;
  imageUrl?: string;
  contentText?: string;
  textEffect?: 'none' | 'decrypted';
}

interface RenderHeroProps {
  address: string;
  componentData: {
    address: string;
    chain: string;
    componentType: string;
    order: string;
    hidden: string;
    heroContent?: HeroContent[];
    backgroundColor?: string;
    fontColor?: string | null;
  };
  showPositionLabel?: boolean;
}

export default function RenderHero({
  address,
  componentData,
  showPositionLabel = false
}: RenderHeroProps) {
  const [heroContent, setHeroContent] = useState<HeroContent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadHeroContent = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch hero content from API
        const response = await fetch(`/api/hero/${address}`);

        if (!response.ok) {
          throw new Error('Failed to load hero content');
        }

        const data = await response.json();

        if (data && data.heroContent && Array.isArray(data.heroContent)) {
          // Ensure all loaded content has textEffect field for backward compatibility
          const contentWithTextEffect = data.heroContent.map((item: HeroContent) => ({
            ...item,
            textEffect: item.textEffect || 'decrypted'
          }));
          setHeroContent(contentWithTextEffect);
        } else {
          setError('No hero content found');
        }
      } catch (error) {
        console.error('Error loading hero content:', error);
        setError('Failed to load hero content');
      } finally {
        setIsLoading(false);
      }
    };

    if (address) {
      loadHeroContent();
    }
  }, [address]);

  if (isLoading) {
    return (
      <div className="relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8">
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </div>
    );
  }

  if (error || heroContent.length === 0) {
    return (
      <div className="relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8">
        <div className="text-center text-neutral-400">
          <p>{error || 'No hero content available'}</p>
        </div>
      </div>
    );
  }

  // Transform hero content for StickyScrollRevealDemo
  const transformedContent = heroContent.map((item) => {
    let content = null;

    if (item.contentType === 'image' && item.imageUrl) {
      content = (
        <div className="flex h-full w-full items-center justify-center">
          <img
            src={item.imageUrl}
            className="w-full h-full object-contain"
            alt={item.title}
          />
        </div>
      );
    } else if (item.colorGradient) {
      content = (
        <div
          className="flex h-full w-full items-center justify-center text-white"
          style={{ background: item.colorGradient }}
        >
          {item.contentText || item.title}
        </div>
      );
    }

    // Handle text effects for title and description
    const titleElement = item.textEffect === 'decrypted' ? (
      <DecryptedText
        text={item.title}
        animateOn="view"
        className="font-bold"
      />
    ) : item.title;

    const descriptionElement = item.textEffect === 'decrypted' ? (
      <DecryptedText
        text={item.description}
        animateOn="view"
      />
    ) : item.description;

    return {
      title: titleElement,
      description: descriptionElement,
      content: content,
      customBackground: item.colorGradient || null
    };
  });

  return (
    <div
      className="relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-0"
      style={{ backgroundColor: componentData.backgroundColor || 'transparent' }}
    >
      <StickyScrollRevealDemo
        content={transformedContent}
        fontColor={componentData.fontColor || '#ffffff'}
        backgroundColor={componentData.backgroundColor || 'transparent'}
      />

      {showPositionLabel && (
        <div className="absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
          Hero
        </div>
      )}
    </div>
  );
}
