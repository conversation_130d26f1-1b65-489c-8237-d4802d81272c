'use client';

import { useState, useEffect } from 'react';
import { StickyScrollRevealDemo } from '@/components/ui/sticky-scroll-reveal-demo';
import DecryptedText from '@/components/ui/DecryptedText';

interface HeroContent {
  title: string;
  description: string;
  contentType: 'color' | 'image';
  colorGradient?: string;
  imageUrl?: string;
  contentText?: string;
  textEffect?: 'none' | 'decrypted';
}

interface RenderHeroProps {
  address: string;
  componentData: {
    address: string;
    chain: string;
    componentType: string;
    order: string;
    hidden: string;
    heroContent?: HeroContent[];
    backgroundColor?: string;
    fontColor?: string | null;
  };
  showPositionLabel?: boolean;
}

export default function RenderHero({
  address,
  componentData,
  showPositionLabel = false
}: RenderHeroProps) {
  const [heroContent, setHeroContent] = useState<HeroContent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadHeroContent = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch hero content from API
        const response = await fetch(`/api/hero/${address}`);

        if (!response.ok) {
          throw new Error('Failed to load hero content');
        }

        const data = await response.json();

        if (data && data.heroContent && Array.isArray(data.heroContent)) {
          // Ensure all loaded content has textEffect field for backward compatibility
          const contentWithTextEffect = data.heroContent.map((item: HeroContent) => ({
            ...item,
            textEffect: item.textEffect || 'decrypted'
          }));
          setHeroContent(contentWithTextEffect);
        } else {
          setError('No hero content found');
        }
      } catch (error) {
        console.error('Error loading hero content:', error);
        setError('Failed to load hero content');
      } finally {
        setIsLoading(false);
      }
    };

    if (address) {
      loadHeroContent();
    }
  }, [address]);

  if (isLoading) {
    return (
      <div className="relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8">
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </div>
    );
  }

  if (error || heroContent.length === 0) {
    return (
      <div className="relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8">
        <div className="text-center text-neutral-400">
          <p>{error || 'No hero content available'}</p>
        </div>
      </div>
    );
  }

  // Format content for the StickyScrollRevealDemo component
  const formatContentForDemo = () => {
    return heroContent.map((item, index) => {

      // For image content, we'll create a special wrapper with background color
      // For color content, we'll use the gradient as before
      let bgClass = '';
      let customBackground = null;

      if (item.contentType === 'color') {
        if (item.colorGradient?.includes('cyan') && item.colorGradient?.includes('emerald')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--cyan-500),var(--emerald-500))]';
        } else if (item.colorGradient?.includes('orange') && item.colorGradient?.includes('yellow')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--orange-500),var(--yellow-500))]';
        } else if (item.colorGradient?.includes('pink') && item.colorGradient?.includes('purple')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--pink-500),var(--purple-500))]';
        } else if (item.colorGradient?.includes('blue') && item.colorGradient?.includes('indigo')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--blue-500),var(--indigo-500))]';
        } else {
          // No gradient specified
          bgClass = '';
        }
      } else if (item.contentType === 'image') {
        // For image content, we'll override the background in the StickyScroll component
        customBackground = componentData.backgroundColor || 'transparent';
      }

      return {
        title: item.title,
        // Conditionally use DecryptedText based on textEffect setting
        description: (item.textEffect === 'decrypted' || item.textEffect === undefined) ? (
          <DecryptedText
            text={item.description}
            animateOn="view"
            speed={50}
            maxIterations={15}
            revealDirection="start"
            sequential={true}
            className=""
            style={{ color: componentData.fontColor || '#ffffff' }}
          />
        ) : (
          <span style={{ color: componentData.fontColor || '#ffffff' }}>
            {item.description}
          </span>
        ),
        content: (
          <div
            key={`hero-content-${index}`}
            className={`flex h-full w-full items-center justify-center text-white ${item.contentType === 'color' ? bgClass : ''}`}
            style={item.contentType === 'image' ? { backgroundColor: componentData.backgroundColor || 'transparent' } : {}}
          >
            {item.contentType === 'image' && item.imageUrl ? (
              <img
                src={item.imageUrl}
                className="h-full w-full object-contain"
                alt={item.title}
              />
            ) : (
              item.contentText || item.title
            )}
          </div>
        ),
        // Add custom background for image content to override the StickyScroll gradient
        customBackground: item.contentType === 'image' ? customBackground : undefined,
        customScrollContainerClass: "h-[20rem] sm:h-[26rem] md:h-[28rem]" // Add custom class for scroll container
      };
    });
  };


  if (isLoading) {
    return (
      <div className="relative bg-transparent rounded-xl border border-neutral-800 overflow-hidden w-full">
        <div className="w-full h-[20rem] md:h-[30rem] bg-transparent flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error || heroContent.length === 0) {
    return (
      <div className="relative bg-transparent rounded-xl border border-neutral-800 overflow-hidden w-full">
        <div className="w-full h-[20rem] md:h-[30rem] bg-transparent flex items-center justify-center">
          <p className="text-neutral-400 text-center max-w-md px-4">
            {error || 'No hero content available. Add some content in the Create page.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-0"
    >
      <StickyScrollRevealDemo content={formatContentForDemo()} fontColor={componentData.fontColor || undefined} backgroundColor={componentData.backgroundColor} />

      {showPositionLabel && (
        <div className="absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
          Hero
        </div>
      )}
    </div>
  );
}
