"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_walletconnect_utils_node_modules_noble_curves_esm_secp256k1_js"],{

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n/** connects noble-curves to noble-hashes */\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FsbGV0Y29ubmVjdC91dGlscy9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vX3Nob3J0d191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMEM7QUFDcUI7QUFDUDtBQUN4RDtBQUNPO0FBQ1A7QUFDQTtBQUNBLGdDQUFnQyx3REFBSSxZQUFZLGdFQUFXO0FBQzNELG1CQUFtQjtBQUNuQjtBQUNBO0FBQ087QUFDUCw2QkFBNkIscUVBQVcsR0FBRywrQkFBK0I7QUFDMUUsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhbGxldGNvbm5lY3RcXHV0aWxzXFxub2RlX21vZHVsZXNcXEBub2JsZVxcY3VydmVzXFxlc21cXF9zaG9ydHdfdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVdGlsaXRpZXMgZm9yIHNob3J0IHdlaWVyc3RyYXNzIGN1cnZlcywgY29tYmluZWQgd2l0aCBub2JsZS1oYXNoZXMuXG4gKiBAbW9kdWxlXG4gKi9cbi8qISBub2JsZS1jdXJ2ZXMgLSBNSVQgTGljZW5zZSAoYykgMjAyMiBQYXVsIE1pbGxlciAocGF1bG1pbGxyLmNvbSkgKi9cbmltcG9ydCB7IGhtYWMgfSBmcm9tICdAbm9ibGUvaGFzaGVzL2htYWMnO1xuaW1wb3J0IHsgY29uY2F0Qnl0ZXMsIHJhbmRvbUJ5dGVzIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy91dGlscyc7XG5pbXBvcnQgeyB3ZWllcnN0cmFzcyB9IGZyb20gJy4vYWJzdHJhY3Qvd2VpZXJzdHJhc3MuanMnO1xuLyoqIGNvbm5lY3RzIG5vYmxlLWN1cnZlcyB0byBub2JsZS1oYXNoZXMgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRIYXNoKGhhc2gpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBoYXNoLFxuICAgICAgICBobWFjOiAoa2V5LCAuLi5tc2dzKSA9PiBobWFjKGhhc2gsIGtleSwgY29uY2F0Qnl0ZXMoLi4ubXNncykpLFxuICAgICAgICByYW5kb21CeXRlcyxcbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUN1cnZlKGN1cnZlRGVmLCBkZWZIYXNoKSB7XG4gICAgY29uc3QgY3JlYXRlID0gKGhhc2gpID0+IHdlaWVyc3RyYXNzKHsgLi4uY3VydmVEZWYsIC4uLmdldEhhc2goaGFzaCkgfSk7XG4gICAgcmV0dXJuIHsgLi4uY3JlYXRlKGRlZkhhc2gpLCBjcmVhdGUgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPV9zaG9ydHdfdXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pippenger: () => (/* binding */ pippenger),\n/* harmony export */   precomputeMSMUnsafe: () => (/* binding */ precomputeMSMUnsafe),\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, bits) {\n    validateW(W, bits);\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap(); // This allows use make points immutable (nothing changes inside)\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nfunction wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = calcWOpts(W, bits);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                if (n === _0n)\n                    break; // No need to go over empty scalar\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                if (wbits === 0)\n                    continue;\n                let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n                if (wbits < 0)\n                    curr = curr.negate();\n                // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n                acc = acc.add(curr);\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nfunction pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    if (points.length !== scalars.length)\n        throw new Error('arrays of points and scalars must have equal length');\n    const zero = c.ZERO;\n    const wbits = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitLen)(BigInt(points.length));\n    const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n    const MASK = (1 << windowSize) - 1;\n    const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < scalars.length; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nfunction precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = BigInt((1 << windowSize) - 1);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    anum(value);\n    anum(length);\n    if (value < 0 || value >= 1 << (8 * length))\n        throw new Error('invalid I2OSP input: ' + value);\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction anum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (lenInBytes > 65535 || ell > 255)\n        throw new Error('expand_message_xmd: invalid lenInBytes');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    anum(count);\n    const DST = typeof _DST === 'string' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(_DST) : _DST;\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const COEFF = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xNum, xDen, yNum, yDen] = COEFF.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        x = field.div(xNum, xDen); // xNum / xDen\n        y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n        return { x: x, y: y };\n    };\n}\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nfunction createHasher(Point, mapToCurve, def) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    return {\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...def, DST: def.DST, ...options });\n            const u0 = Point.fromAffine(mapToCurve(u[0]));\n            const u1 = Point.fromAffine(mapToCurve(u[1]));\n            const P = u0.add(u1).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...def, DST: def.encodeDST, ...options });\n            const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Same as encodeToCurve, but without hash\n        mapToCurve(scalars) {\n            if (!Array.isArray(scalars))\n                throw new Error('mapToCurve: expected array of bigints');\n            for (const i of scalars)\n                if (typeof i !== 'bigint')\n                    throw new Error('mapToCurve: expected array of bigints');\n            const P = Point.fromAffine(mapToCurve(scalars)).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpLegendre: () => (/* binding */ FpLegendre),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nfunction pow(num, power, modulo) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (modulo <= _0n)\n        throw new Error('invalid modulus');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nfunction invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n        // Crash instead of infinity loop, we cannot reasonable count until P.\n        if (Z > 1000)\n            throw new Error('Cannot find square root: likely non-prime P');\n    }\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nfunction FpSqrt(P) {\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nfunction FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nfunction FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nfunction FpLegendre(order) {\n    const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n    return (f, x) => f.pow(x, legendreConst);\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(f) {\n    const legendre = FpLegendre(f.ORDER);\n    return (x) => {\n        const p = legendre(f, x);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aInRange: () => (/* binding */ aInRange),\n/* harmony export */   abool: () => (/* binding */ abool),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   memoized: () => (/* binding */ memoized),\n/* harmony export */   notImplemented: () => (/* binding */ notImplemented),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nfunction abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nfunction inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nfunction aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nfunction bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nconst notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nfunction memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   DERErr: () => (/* binding */ DERErr),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curve.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/curve.js\");\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Short Weierstrass curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endomorphism, can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endomorphism, expected beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = _utils_js__WEBPACK_IMPORTED_MODULE_0__;\nclass DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded((len.length / 2) | 128) : '';\n            const t = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return b2n(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes(data);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.Field)(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return _utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(key))\n                key = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(num, N); // disabled by default, enabled for BLS\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    const toAffineMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        if (!Fp.eql(left, right))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.pippenger)(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.wNAF)(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.invert)(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(tail);\n                if (!_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    const numToNByteStr = (num) => _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('r', this.r, _1n, CURVE_ORDER); // r in [1..N]\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('s', this.s, _1n, CURVE_ORDER); // s in [1..N]\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength)(CURVE.n);\n            return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField)(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = _utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(item);\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\"\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('num < 2^' + CURVE.nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = _utils_js__WEBPACK_IMPORTED_MODULE_0__.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || _utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField)(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha256 */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/_shortw_utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 short weierstrass curve and ECDSA signatures over it.\n *\n * @example\n * import { secp256k1 } from '@noble/curves/secp256k1';\n *\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n */\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: BigInt(0), // equation params: a, b\n    b: BigInt(7),\n    Fp: Fpk1, // Field's prime: 2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n\n    n: secp256k1N, // Curve order, total count of valid points in the field\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1), // Cofactor\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.aInRange)('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n */\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\n/** secp256k1 hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nconst hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\n/** secp256k1 encode-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nconst encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: () => (/* binding */ Chi),\n/* harmony export */   HashMD: () => (/* binding */ HashMD),\n/* harmony export */   Maj: () => (/* binding */ Maj),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        const { view, buffer, blockLen } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/hmac.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA256: () => (/* binding */ SHA256),\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\n\n\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = SHA256_IV[0] | 0;\n        this.B = SHA256_IV[1] | 0;\n        this.C = SHA256_IV[2] | 0;\n        this.D = SHA256_IV[3] | 0;\n        this.E = SHA256_IV[4] | 0;\n        this.F = SHA256_IV[5] | 0;\n        this.G = SHA256_IV[6] | 0;\n        this.H = SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/** SHA2-256 hash function */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA256());\n/** SHA2-224 hash function */\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA224());\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@walletconnect/utils/node_modules/@noble/hashes/esm/sha256.js\n"));

/***/ })

}]);