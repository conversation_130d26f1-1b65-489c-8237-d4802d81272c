"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.garnet = void 0;
const chainConfig_js_1 = require("../../op-stack/chainConfig.js");
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const sourceId = 17000;
exports.garnet = (0, defineChain_js_1.defineChain)({
    ...chainConfig_js_1.chainConfig,
    name: 'Garnet Testnet',
    testnet: true,
    id: 17069,
    sourceId,
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.garnetchain.com'],
            webSocket: ['wss://rpc.garnetchain.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://explorer.garnetchain.com',
        },
    },
    contracts: {
        ...chainConfig_js_1.chainConfig.contracts,
        multicall3: {
            address: '******************************************',
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 1274684,
            },
        },
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 1274684,
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 1274684,
            },
        },
    },
});
//# sourceMappingURL=garnet.js.map