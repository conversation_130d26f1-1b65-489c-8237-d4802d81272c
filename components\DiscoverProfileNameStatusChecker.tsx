'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAppKitAccount } from '@reown/appkit/react';

interface ProfileStatusCheckerProps {
  profileAddress: string;
  profileName?: string;
  children: React.ReactNode;
}

// This component is specifically for the name part of profile cards
// It doesn't show the status text overlay, but still prevents navigation
export default function DiscoverProfileNameStatusChecker({
  profileAddress,
  profileName,
  children
}: ProfileStatusCheckerProps) {
  const router = useRouter();
  const { address } = useAppKitAccount();
  const [status, setStatus] = useState<string | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const [isApproved, setIsApproved] = useState(false);
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  useEffect(() => {
    const checkProfileStatus = async () => {
      try {
        setIsChecking(true);

        // Use the profile name if available, otherwise use the address
        const identifier = profileName || profileAddress;
        console.log('[DiscoverProfileNameStatusChecker] Checking profile:', identifier);

        // Fetch profile status
        const response = await fetch(`/api/profile/check-status?identifier=${identifier}`);
        const data = await response.json();
        console.log('[DiscoverProfileNameStatusChecker] Profile status data:', data);

        // Check if this is the user's own profile
        const isOwn = address && data.address &&
          address.toLowerCase() === data.address.toLowerCase();
        console.log('[DiscoverProfileNameStatusChecker] Is own profile:', isOwn);

        setStatus(data.status);
        setIsApproved(data.status === 'approved');
        setIsOwnProfile(isOwn);
      } catch (error) {
        console.error('Error checking profile status:', error);
      } finally {
        setIsChecking(false);
      }
    };

    if (profileAddress) {
      checkProfileStatus();
    }
  }, [profileAddress, profileName, address]);

  const handleClick = (e: React.MouseEvent) => {
    // If profile is not approved and it's not the user's own profile, prevent navigation
    if (!isApproved && !isOwnProfile && status !== 'not-found') {
      console.log('[DiscoverProfileNameStatusChecker] Preventing navigation for status:', status);
      e.preventDefault();
      e.stopPropagation();
      // Use window.location for a hard redirect instead of router.push
      // Use the name parameter instead of address
      const nameParam = profileName ? `&name=${profileName}` : '';
      window.location.href = `/profile-error?status=${status}${nameParam}`;
    } else {
      console.log('[DiscoverProfileNameStatusChecker] Allowing navigation for status:', status);
    }
  };

  // Always render children normally, but attach click handler if needed
  if (isChecking || isApproved || status === 'not-found') {
    return <>{children}</>;
  } else {
    // For non-approved profiles, add the click handler without any visual overlay
    // We don't make an exception for the owner's profile in the Discover page
    return (
      <div onClick={handleClick} className="cursor-pointer">
        {children}
      </div>
    );
  }
}
