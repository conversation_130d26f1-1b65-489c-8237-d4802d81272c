import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, profileReferrals } from '@/db/schema';
import { eq } from 'drizzle-orm';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    console.log(`[referral API] Fetching referral data for address: ${address}`);

    // Get profile to find referral code
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      console.log(`[referral API] No profile found for address: ${address}`);
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    const referralCode = profile[0].referralCode;

    if (!referralCode) {
      console.log(`[referral API] No referral code for address: ${address}`);
      return Response.json({
        referralCode: null,
        referralCount: 0
      });
    }

    // Count how many people used this referral code
    const referralCount = await db
      .select()
      .from(profileReferrals)
      .where(eq(profileReferrals.referrerAddress, address));

    console.log(`[referral API] Found referral data for address: ${address}`);
    return Response.json({
      referralCode,
      referralCount: referralCount.length
    });

  } catch (error: any) {
    console.error('[referral API] Error fetching referral data:', error);
    
    return Response.json(
      { error: 'Failed to fetch referral data' },
      { status: 500 }
    );
  }
}
