import ClientPage from './ClientPage';

export default async function Page({
  params,
}: {
  params?: Promise<{ name: string }>
}) {
  // Await the params if they are provided as a Promise
  const resolvedParams = params ? await params : { name: '' };

  // Ensure name is never undefined
  const name = resolvedParams.name || '';
  // Page component received name parameter

  return <ClientPage name={name} />;
}
