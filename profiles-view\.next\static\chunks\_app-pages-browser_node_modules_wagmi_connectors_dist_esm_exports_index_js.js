"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_wagmi_connectors_dist_esm_exports_index_js"],{

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* binding */ coinbaseWallet)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\ncoinbaseWallet.type = 'coinbaseWallet';\nfunction coinbaseWallet(parameters = {}) {\n    if (parameters.version === '3' || parameters.headlessMode)\n        return version3(parameters);\n    return version4(parameters);\n}\nfunction version4(parameters) {\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                    params: 'instantOnboarding' in rest && rest.instantOnboarding\n                        ? [{ onboarding: 'instant' }]\n                        : [],\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close?.();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = (await provider.request({\n                method: 'eth_chainId',\n            }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                const preference = (() => {\n                    if (typeof parameters.preference === 'string')\n                        return { options: parameters.preference };\n                    return {\n                        ...parameters.preference,\n                        options: parameters.preference?.options ?? 'all',\n                    };\n                })();\n                const { createCoinbaseWalletSDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_coinbase_wallet-sdk_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @coinbase/wallet-sdk */ \"(app-pages-browser)/./node_modules/@coinbase/wallet-sdk/dist/index.js\"));\n                const sdk = createCoinbaseWalletSDK({\n                    ...parameters,\n                    appChainIds: config.chains.map((x) => x.id),\n                    preference,\n                });\n                walletProvider = sdk.getProvider();\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\nfunction version3(parameters) {\n    const reloadOnDisconnect = false;\n    let sdk;\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = await provider.request({\n                method: 'eth_chainId',\n            });\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const CoinbaseWalletSDK = await (async () => {\n                    const { default: SDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cbw-sdk_dist_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cbw-sdk */ \"(app-pages-browser)/./node_modules/cbw-sdk/dist/index.js\", 19));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                sdk = new CoinbaseWalletSDK({ ...parameters, reloadOnDisconnect });\n                // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.\n                const walletExtensionChainId = sdk.walletExtension?.getChainId();\n                const chain = config.chains.find((chain) => parameters.chainId\n                    ? chain.id === parameters.chainId\n                    : chain.id === walletExtensionChainId) || config.chains[0];\n                const chainId = parameters.chainId || chain?.id;\n                const jsonRpcUrl = parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0];\n                walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId);\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\n//# sourceMappingURL=coinbaseWallet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/exports/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* reexport safe */ _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_2__.coinbaseWallet),\n/* harmony export */   injected: () => (/* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_0__.injected),\n/* harmony export */   metaMask: () => (/* reexport safe */ _metaMask_js__WEBPACK_IMPORTED_MODULE_3__.metaMask),\n/* harmony export */   mock: () => (/* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.mock),\n/* harmony export */   safe: () => (/* reexport safe */ _safe_js__WEBPACK_IMPORTED_MODULE_4__.safe),\n/* harmony export */   version: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_6__.version),\n/* harmony export */   walletConnect: () => (/* reexport safe */ _walletConnect_js__WEBPACK_IMPORTED_MODULE_5__.walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\");\n/* harmony import */ var _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../coinbaseWallet.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\");\n/* harmony import */ var _metaMask_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../metaMask.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\");\n/* harmony import */ var _safe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../safe.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/safe.js\");\n/* harmony import */ var _walletConnect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../walletConnect.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../version.js */ \"(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/version.js\");\n// biome-ignore lint/performance/noBarrelFile: entrypoint module\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS9leHBvcnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDOEM7QUFDUztBQUNiO0FBQ1I7QUFDbUI7QUFDYjtBQUN4QyIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvbm5lY3RvcnNcXGRpc3RcXGVzbVxcZXhwb3J0c1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gYmlvbWUtaWdub3JlIGxpbnQvcGVyZm9ybWFuY2Uvbm9CYXJyZWxGaWxlOiBlbnRyeXBvaW50IG1vZHVsZVxuZXhwb3J0IHsgaW5qZWN0ZWQsIG1vY2ssIH0gZnJvbSAnQHdhZ21pL2NvcmUnO1xuZXhwb3J0IHsgY29pbmJhc2VXYWxsZXQsIH0gZnJvbSAnLi4vY29pbmJhc2VXYWxsZXQuanMnO1xuZXhwb3J0IHsgbWV0YU1hc2sgfSBmcm9tICcuLi9tZXRhTWFzay5qcyc7XG5leHBvcnQgeyBzYWZlIH0gZnJvbSAnLi4vc2FmZS5qcyc7XG5leHBvcnQgeyB3YWxsZXRDb25uZWN0LCB9IGZyb20gJy4uL3dhbGxldENvbm5lY3QuanMnO1xuZXhwb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4uL3ZlcnNpb24uanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: () => (/* binding */ metaMask)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_metamask_sdk_dist_browser_es_metamask-sdk_js\").then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(app-pages-browser)/./node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/safe.js":
/*!*********************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/safe.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n\n\nsafe.type = 'safe';\nfunction safe(parameters = {}) {\n    const { shimDisconnect = false } = parameters;\n    let provider_;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'safe',\n        name: 'Safe',\n        type: safe.type,\n        async connect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await this.getAccounts();\n            const chainId = await this.getChainId();\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n            // Remove disconnected shim if it exists\n            if (shimDisconnect)\n                await config.storage?.removeItem('safe.disconnected');\n            return { accounts, chainId };\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect)\n                await config.storage?.setItem('safe.disconnected', true);\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return (await provider.request({ method: 'eth_accounts' })).map(viem__WEBPACK_IMPORTED_MODULE_2__.getAddress);\n        },\n        async getProvider() {\n            // Only allowed in iframe context\n            const isIframe = typeof window !== 'undefined' && window?.parent !== window;\n            if (!isIframe)\n                return;\n            if (!provider_) {\n                const { default: SDK } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_safe-global_safe-apps-sdk_dist_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @safe-global/safe-apps-sdk */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\"));\n                const sdk = new SDK(parameters);\n                // `getInfo` hangs when not used in Safe App iFrame\n                // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********\n                const safe = await (0,viem__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => sdk.safe.getInfo(), {\n                    timeout: parameters.unstable_getInfoTimeout ?? 10,\n                });\n                if (!safe)\n                    throw new Error('Could not load Safe information');\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const SafeAppProvider = await (async () => {\n                    const Provider = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_safe-global_safe-apps-provider_dist_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! @safe-global/safe-apps-provider */ \"(app-pages-browser)/./node_modules/@safe-global/safe-apps-provider/dist/index.js\", 19));\n                    if (typeof Provider.SafeAppProvider !== 'function' &&\n                        typeof Provider.default.SafeAppProvider === 'function')\n                        return Provider.default.SafeAppProvider;\n                    return Provider.SafeAppProvider;\n                })();\n                provider_ = new SafeAppProvider(safe, sdk);\n            }\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return Number(provider.chainId);\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem('safe.disconnected'));\n                if (isDisconnected)\n                    return false;\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        onAccountsChanged() {\n            // Not relevant for Safe because changing account requires app reload.\n        },\n        onChainChanged() {\n            // Not relevant for Safe because Safe smart contract wallets only exist on single chain.\n        },\n        onDisconnect() {\n            config.emitter.emit('disconnect');\n        },\n    }));\n}\n//# sourceMappingURL=safe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/safe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/version.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/version.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '5.8.3';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29ubmVjdG9yc1xcZGlzdFxcZXNtXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzUuOC4zJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/version.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/walletConnect.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnect: () => (/* binding */ walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\nwalletConnect.type = 'walletConnect';\nfunction walletConnect(parameters) {\n    const isNewChainsStale = parameters.isNewChainsStale ?? true;\n    let provider_;\n    let providerPromise;\n    const NAMESPACE = 'eip155';\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let sessionDelete;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'walletConnect',\n        name: 'WalletConnect',\n        type: walletConnect.type,\n        async setup() {\n            const provider = await this.getProvider().catch(() => null);\n            if (!provider)\n                return;\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            if (!sessionDelete) {\n                sessionDelete = this.onSessionDelete.bind(this);\n                provider.on('session_delete', sessionDelete);\n            }\n        },\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                if (!displayUri) {\n                    displayUri = this.onDisplayUri;\n                    provider.on('display_uri', displayUri);\n                }\n                let targetChainId = chainId;\n                if (!targetChainId) {\n                    const state = (await config.storage?.getItem('state')) ?? {};\n                    const isChainSupported = config.chains.some((x) => x.id === state.chainId);\n                    if (isChainSupported)\n                        targetChainId = state.chainId;\n                    else\n                        targetChainId = config.chains[0]?.id;\n                }\n                if (!targetChainId)\n                    throw new Error('No chains found on connector.');\n                const isChainsStale = await this.isChainsStale();\n                // If there is an active session with stale chains, disconnect current session.\n                if (provider.session && isChainsStale)\n                    await provider.disconnect();\n                // If there isn't an active session or chains are stale, connect.\n                if (!provider.session || isChainsStale) {\n                    const optionalChains = config.chains\n                        .filter((chain) => chain.id !== targetChainId)\n                        .map((optionalChain) => optionalChain.id);\n                    await provider.connect({\n                        optionalChains: [targetChainId, ...optionalChains],\n                        ...('pairingTopic' in rest\n                            ? { pairingTopic: rest.pairingTopic }\n                            : {}),\n                    });\n                    this.setRequestedChainsIds(config.chains.map((x) => x.id));\n                }\n                // If session exists and chains are authorized, enable provider for required chain\n                const accounts = (await provider.enable()).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                const currentChainId = await this.getChainId();\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                if (!sessionDelete) {\n                    sessionDelete = this.onSessionDelete.bind(this);\n                    provider.on('session_delete', sessionDelete);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user rejected|connection request reset)/i.test(error?.message)) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            try {\n                await provider?.disconnect();\n            }\n            catch (error) {\n                if (!/No matching key/i.test(error.message))\n                    throw error;\n            }\n            finally {\n                if (chainChanged) {\n                    provider?.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider?.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider?.on('connect', connect);\n                }\n                if (accountsChanged) {\n                    provider?.removeListener('accountsChanged', accountsChanged);\n                    accountsChanged = undefined;\n                }\n                if (sessionDelete) {\n                    provider?.removeListener('session_delete', sessionDelete);\n                    sessionDelete = undefined;\n                }\n                this.setRequestedChainsIds([]);\n            }\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return provider.accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getProvider({ chainId } = {}) {\n            async function initProvider() {\n                const optionalChains = config.chains.map((x) => x.id);\n                if (!optionalChains.length)\n                    return;\n                const { EthereumProvider } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_walletconnect_ethereum-provider_dist_index_es_js\").then(__webpack_require__.bind(__webpack_require__, /*! @walletconnect/ethereum-provider */ \"(app-pages-browser)/./node_modules/@walletconnect/ethereum-provider/dist/index.es.js\"));\n                return await EthereumProvider.init({\n                    ...parameters,\n                    disableProviderPing: true,\n                    optionalChains,\n                    projectId: parameters.projectId,\n                    rpcMap: Object.fromEntries(config.chains.map((chain) => {\n                        const [url] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                            chain,\n                            transports: config.transports,\n                        });\n                        return [chain.id, url];\n                    })),\n                    showQrModal: parameters.showQrModal ?? true,\n                });\n            }\n            if (!provider_) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider_ = await providerPromise;\n                provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY);\n            }\n            if (chainId)\n                await this.switchChain?.({ chainId });\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            return provider.chainId;\n        },\n        async isAuthorized() {\n            try {\n                const [accounts, provider] = await Promise.all([\n                    this.getAccounts(),\n                    this.getProvider(),\n                ]);\n                // If an account does not exist on the session, then the connector is unauthorized.\n                if (!accounts.length)\n                    return false;\n                // If the chains are stale on the session, then the connector is unauthorized.\n                const isChainsStale = await this.isChainsStale();\n                if (isChainsStale && provider.session) {\n                    await provider.disconnect().catch(() => { });\n                    return false;\n                }\n                return true;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ChainNotConfiguredError());\n            try {\n                await Promise.all([\n                    new Promise((resolve) => {\n                        const listener = ({ chainId: currentChainId, }) => {\n                            if (currentChainId === chainId) {\n                                config.emitter.off('change', listener);\n                                resolve();\n                            }\n                        };\n                        config.emitter.on('change', listener);\n                    }),\n                    provider.request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId) }],\n                    }),\n                ]);\n                const requestedChains = await this.getRequestedChainsIds();\n                this.setRequestedChainsIds([...requestedChains, chainId]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (/(user rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                try {\n                    let blockExplorerUrls;\n                    if (addEthereumChainParameter?.blockExplorerUrls)\n                        blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                    else\n                        blockExplorerUrls = chain.blockExplorers?.default.url\n                            ? [chain.blockExplorers?.default.url]\n                            : [];\n                    let rpcUrls;\n                    if (addEthereumChainParameter?.rpcUrls?.length)\n                        rpcUrls = addEthereumChainParameter.rpcUrls;\n                    else\n                        rpcUrls = [...chain.rpcUrls.default.http];\n                    const addEthereumChain = {\n                        blockExplorerUrls,\n                        chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId),\n                        chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                        iconUrls: addEthereumChainParameter?.iconUrls,\n                        nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,\n                        rpcUrls,\n                    };\n                    await provider.request({\n                        method: 'wallet_addEthereumChain',\n                        params: [addEthereumChain],\n                    });\n                    const requestedChains = await this.getRequestedChainsIds();\n                    this.setRequestedChainsIds([...requestedChains, chainId]);\n                    return chain;\n                }\n                catch (error) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const chainId = Number(connectInfo.chainId);\n            const accounts = await this.getAccounts();\n            config.emitter.emit('connect', { accounts, chainId });\n        },\n        async onDisconnect(_error) {\n            this.setRequestedChainsIds([]);\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (sessionDelete) {\n                provider.removeListener('session_delete', sessionDelete);\n                sessionDelete = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n        onSessionDelete() {\n            this.onDisconnect();\n        },\n        getNamespaceChainsIds() {\n            if (!provider_)\n                return [];\n            const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map((account) => Number.parseInt(account.split(':')[1] || ''));\n            return chainIds ?? [];\n        },\n        async getRequestedChainsIds() {\n            return ((await config.storage?.getItem(this.requestedChainsStorageKey)) ?? []);\n        },\n        /**\n         * Checks if the target chains match the chains that were\n         * initially requested by the connector for the WalletConnect session.\n         * If there is a mismatch, this means that the chains on the connector\n         * are considered stale, and need to be revalidated at a later point (via\n         * connection).\n         *\n         * There may be a scenario where a dapp adds a chain to the\n         * connector later on, however, this chain will not have been approved or rejected\n         * by the wallet. In this case, the chain is considered stale.\n         */\n        async isChainsStale() {\n            if (!isNewChainsStale)\n                return false;\n            const connectorChains = config.chains.map((x) => x.id);\n            const namespaceChains = this.getNamespaceChainsIds();\n            if (namespaceChains.length &&\n                !namespaceChains.some((id) => connectorChains.includes(id)))\n                return false;\n            const requestedChains = await this.getRequestedChainsIds();\n            return !connectorChains.every((id) => requestedChains.includes(id));\n        },\n        async setRequestedChainsIds(chains) {\n            await config.storage?.setItem(this.requestedChainsStorageKey, chains);\n        },\n        get requestedChainsStorageKey() {\n            return `${this.id}.requestedChains`;\n        },\n    }));\n}\n//# sourceMappingURL=walletConnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/mock.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mock: () => (/* binding */ mock)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem/utils */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/compat.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../errors/config.js */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\nmock.type = 'mock';\nfunction mock(parameters) {\n    const transactionCache = new Map();\n    const features = parameters.features ??\n        { defaultConnected: false };\n    let connected = features.defaultConnected;\n    let connectedChainId;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'mock',\n        name: 'Mock Connector',\n        type: mock.type,\n        async setup() {\n            connectedChainId = config.chains[0].id;\n        },\n        async connect({ chainId } = {}) {\n            if (features.connectError) {\n                if (typeof features.connectError === 'boolean')\n                    throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to connect.'));\n                throw features.connectError;\n            }\n            const provider = await this.getProvider();\n            const accounts = await provider.request({\n                method: 'eth_requestAccounts',\n            });\n            let currentChainId = await this.getChainId();\n            if (chainId && currentChainId !== chainId) {\n                const chain = await this.switchChain({ chainId });\n                currentChainId = chain.id;\n            }\n            connected = true;\n            return {\n                accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                chainId: currentChainId,\n            };\n        },\n        async disconnect() {\n            connected = false;\n        },\n        async getAccounts() {\n            if (!connected)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ConnectorNotConnectedError();\n            const provider = await this.getProvider();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(hexChainId, 'number');\n        },\n        async isAuthorized() {\n            if (!features.reconnect)\n                return false;\n            if (!connected)\n                return false;\n            const accounts = await this.getAccounts();\n            return !!accounts.length;\n        },\n        async switchChain({ chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_1__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            await provider.request({\n                method: 'wallet_switchEthereumChain',\n                params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(chainId) }],\n            });\n            return chain;\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            connected = false;\n        },\n        async getProvider({ chainId } = {}) {\n            const chain = config.chains.find((x) => x.id === chainId) ?? config.chains[0];\n            const url = chain.rpcUrls.default.http[0];\n            const request = async ({ method, params }) => {\n                // eth methods\n                if (method === 'eth_chainId')\n                    return (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(connectedChainId);\n                if (method === 'eth_requestAccounts')\n                    return parameters.accounts;\n                if (method === 'eth_signTypedData_v4')\n                    if (features.signTypedDataError) {\n                        if (typeof features.signTypedDataError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign typed data.'));\n                        throw features.signTypedDataError;\n                    }\n                // wallet methods\n                if (method === 'wallet_switchEthereumChain') {\n                    if (features.switchChainError) {\n                        if (typeof features.switchChainError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.switchChainError;\n                    }\n                    connectedChainId = (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(params[0].chainId, 'number');\n                    this.onChainChanged(connectedChainId.toString());\n                    return;\n                }\n                if (method === 'wallet_watchAsset') {\n                    if (features.watchAssetError) {\n                        if (typeof features.watchAssetError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.watchAssetError;\n                    }\n                    return connected;\n                }\n                if (method === 'wallet_getCapabilities')\n                    return {\n                        '0x2105': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                            sessionKeys: {\n                                supported: true,\n                            },\n                        },\n                        '0x14A34': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                        },\n                    };\n                if (method === 'wallet_sendCalls') {\n                    const hashes = [];\n                    const calls = params[0].calls;\n                    for (const call of calls) {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_sendTransaction',\n                                params: [call],\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        hashes.push(result);\n                    }\n                    const id = (0,viem__WEBPACK_IMPORTED_MODULE_8__.keccak256)((0,viem__WEBPACK_IMPORTED_MODULE_5__.stringToHex)(JSON.stringify(calls)));\n                    transactionCache.set(id, hashes);\n                    return { id };\n                }\n                if (method === 'wallet_getCallsStatus') {\n                    const hashes = transactionCache.get(params[0]);\n                    if (!hashes)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    const receipts = await Promise.all(hashes.map(async (hash) => {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_getTransactionReceipt',\n                                params: [hash],\n                                id: 0,\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        if (!result)\n                            return null;\n                        return {\n                            blockHash: result.blockHash,\n                            blockNumber: result.blockNumber,\n                            gasUsed: result.gasUsed,\n                            logs: result.logs,\n                            status: result.status,\n                            transactionHash: result.transactionHash,\n                        };\n                    }));\n                    const receipts_ = receipts.filter((x) => x !== null);\n                    if (receipts_.length === 0)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    return {\n                        atomic: false,\n                        chainId: '0x1',\n                        id: params[0],\n                        status: 200,\n                        receipts: receipts_,\n                        version: '2.0.0',\n                    };\n                }\n                if (method === 'wallet_showCallsStatus')\n                    return;\n                // other methods\n                if (method === 'personal_sign') {\n                    if (features.signMessageError) {\n                        if (typeof features.signMessageError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign message.'));\n                        throw features.signMessageError;\n                    }\n                    // Change `personal_sign` to `eth_sign` and swap params\n                    method = 'eth_sign';\n                    params = [params[1], params[0]];\n                }\n                const body = { method, params };\n                const { error, result } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, { body });\n                if (error)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({ body, error, url });\n                return result;\n            };\n            return (0,viem__WEBPACK_IMPORTED_MODULE_9__.custom)({ request })({ retryCount: 0 });\n        },\n    }));\n}\n//# sourceMappingURL=mock.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: () => (/* binding */ extractRpcUrls)\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy9leHRyYWN0UnBjVXJscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBO0FBQ0EsNERBQTRELE9BQU87QUFDbkU7QUFDQSw2QkFBNkIsT0FBTztBQUNwQztBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFx1dGlsc1xcZXh0cmFjdFJwY1VybHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGV4dHJhY3RScGNVcmxzKHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGNoYWluIH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGZhbGxiYWNrVXJsID0gY2hhaW4ucnBjVXJscy5kZWZhdWx0Lmh0dHBbMF07XG4gICAgaWYgKCFwYXJhbWV0ZXJzLnRyYW5zcG9ydHMpXG4gICAgICAgIHJldHVybiBbZmFsbGJhY2tVcmxdO1xuICAgIGNvbnN0IHRyYW5zcG9ydCA9IHBhcmFtZXRlcnMudHJhbnNwb3J0cz8uW2NoYWluLmlkXT8uKHsgY2hhaW4gfSk7XG4gICAgY29uc3QgdHJhbnNwb3J0cyA9IHRyYW5zcG9ydD8udmFsdWU/LnRyYW5zcG9ydHMgfHwgW3RyYW5zcG9ydF07XG4gICAgcmV0dXJuIHRyYW5zcG9ydHMubWFwKCh7IHZhbHVlIH0pID0+IHZhbHVlPy51cmwgfHwgZmFsbGJhY2tVcmwpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXh0cmFjdFJwY1VybHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/compat.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/compat.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSocket: () => (/* binding */ getSocket),\n/* harmony export */   rpc: () => (/* binding */ rpc)\n/* harmony export */ });\n/* harmony import */ var _http_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./http.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/http.js\");\n/* harmony import */ var _webSocket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./webSocket.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/webSocket.js\");\n// TODO(v3): This file is here for backwards compatibility, and to prevent breaking changes.\n// These APIs will be removed in v3.\n\n\nfunction webSocket(socketClient, { body, onError, onResponse }) {\n    socketClient.request({\n        body,\n        onError,\n        onResponse,\n    });\n    return socketClient;\n}\nasync function webSocketAsync(socketClient, { body, timeout = 10_000 }) {\n    return socketClient.requestAsync({\n        body,\n        timeout,\n    });\n}\n/**\n * @deprecated use `getSocketClient` instead.\n *\n * ```diff\n * -import { getSocket } from 'viem/utils'\n * +import { getSocketClient } from 'viem/utils'\n *\n * -const socket = await getSocket(url)\n * +const socketClient = await getSocketClient(url)\n * +const socket = socketClient.socket\n * ```\n */\nasync function getSocket(url) {\n    const client = await (0,_webSocket_js__WEBPACK_IMPORTED_MODULE_0__.getWebSocketRpcClient)(url);\n    return Object.assign(client.socket, {\n        requests: client.requests,\n        subscriptions: client.subscriptions,\n    });\n}\nconst rpc = {\n    /**\n     * @deprecated use `getHttpRpcClient` instead.\n     *\n     * ```diff\n     * -import { rpc } from 'viem/utils'\n     * +import { getHttpRpcClient } from 'viem/utils'\n     *\n     * -rpc.http(url, params)\n     * +const httpClient = getHttpRpcClient(url)\n     * +httpClient.request(params)\n     * ```\n     */\n    http(url, params) {\n        return (0,_http_js__WEBPACK_IMPORTED_MODULE_1__.getHttpRpcClient)(url).request(params);\n    },\n    /**\n     * @deprecated use `getWebSocketRpcClient` instead.\n     *\n     * ```diff\n     * -import { rpc } from 'viem/utils'\n     * +import { getWebSocketRpcClient } from 'viem/utils'\n     *\n     * -rpc.webSocket(url, params)\n     * +const webSocketClient = getWebSocketRpcClient(url)\n     * +webSocketClient.request(params)\n     * ```\n     */\n    webSocket,\n    /**\n     * @deprecated use `getWebSocketRpcClient` instead.\n     *\n     * ```diff\n     * -import { rpc } from 'viem/utils'\n     * +import { getWebSocketRpcClient } from 'viem/utils'\n     *\n     * -const response = await rpc.webSocketAsync(url, params)\n     * +const webSocketClient = getWebSocketRpcClient(url)\n     * +const response = await webSocketClient.requestAsync(params)\n     * ```\n     */\n    webSocketAsync,\n};\n/* c8 ignore end */\n//# sourceMappingURL=compat.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/compat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/socket.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/socket.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSocketRpcClient: () => (/* binding */ getSocketRpcClient),\n/* harmony export */   socketClientCache: () => (/* binding */ socketClientCache)\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../promise/createBatchScheduler.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js\");\n/* harmony import */ var _promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../promise/withTimeout.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./id.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/id.js\");\n\n\n\n\nconst socketClientCache = /*#__PURE__*/ new Map();\nasync function getSocketRpcClient(parameters) {\n    const { getSocket, keepAlive = true, key = 'socket', reconnect = true, url, } = parameters;\n    const { interval: keepAliveInterval = 30_000 } = typeof keepAlive === 'object' ? keepAlive : {};\n    const { attempts = 5, delay = 2_000 } = typeof reconnect === 'object' ? reconnect : {};\n    let socketClient = socketClientCache.get(`${key}:${url}`);\n    // If the socket already exists, return it.\n    if (socketClient)\n        return socketClient;\n    let reconnectCount = 0;\n    const { schedule } = (0,_promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_0__.createBatchScheduler)({\n        id: `${key}:${url}`,\n        fn: async () => {\n            // Set up a cache for incoming \"synchronous\" requests.\n            const requests = new Map();\n            // Set up a cache for subscriptions (eth_subscribe).\n            const subscriptions = new Map();\n            let error;\n            let socket;\n            let keepAliveTimer;\n            // Set up socket implementation.\n            async function setup() {\n                const result = await getSocket({\n                    onClose() {\n                        // Notify all requests and subscriptions of the closure error.\n                        for (const request of requests.values())\n                            request.onError?.(new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url }));\n                        for (const subscription of subscriptions.values())\n                            subscription.onError?.(new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url }));\n                        // Attempt to reconnect.\n                        if (reconnect && reconnectCount < attempts)\n                            setTimeout(async () => {\n                                reconnectCount++;\n                                await setup().catch(console.error);\n                            }, delay);\n                        // Otherwise, clear all requests and subscriptions.\n                        else {\n                            requests.clear();\n                            subscriptions.clear();\n                        }\n                    },\n                    onError(error_) {\n                        error = error_;\n                        // Notify all requests and subscriptions of the error.\n                        for (const request of requests.values())\n                            request.onError?.(error);\n                        for (const subscription of subscriptions.values())\n                            subscription.onError?.(error);\n                        // Make sure socket is definitely closed.\n                        socketClient?.close();\n                        // Attempt to reconnect.\n                        if (reconnect && reconnectCount < attempts)\n                            setTimeout(async () => {\n                                reconnectCount++;\n                                await setup().catch(console.error);\n                            }, delay);\n                        // Otherwise, clear all requests and subscriptions.\n                        else {\n                            requests.clear();\n                            subscriptions.clear();\n                        }\n                    },\n                    onOpen() {\n                        error = undefined;\n                        reconnectCount = 0;\n                    },\n                    onResponse(data) {\n                        const isSubscription = data.method === 'eth_subscription';\n                        const id = isSubscription ? data.params.subscription : data.id;\n                        const cache = isSubscription ? subscriptions : requests;\n                        const callback = cache.get(id);\n                        if (callback)\n                            callback.onResponse(data);\n                        if (!isSubscription)\n                            cache.delete(id);\n                    },\n                });\n                socket = result;\n                if (keepAlive) {\n                    if (keepAliveTimer)\n                        clearInterval(keepAliveTimer);\n                    keepAliveTimer = setInterval(() => socket.ping?.(), keepAliveInterval);\n                }\n                return result;\n            }\n            await setup();\n            error = undefined;\n            // Create a new socket instance.\n            socketClient = {\n                close() {\n                    keepAliveTimer && clearInterval(keepAliveTimer);\n                    socket.close();\n                    socketClientCache.delete(`${key}:${url}`);\n                },\n                get socket() {\n                    return socket;\n                },\n                request({ body, onError, onResponse }) {\n                    if (error && onError)\n                        onError(error);\n                    const id = body.id ?? _id_js__WEBPACK_IMPORTED_MODULE_2__.idCache.take();\n                    const callback = (response) => {\n                        if (typeof response.id === 'number' && id !== response.id)\n                            return;\n                        // If we are subscribing to a topic, we want to set up a listener for incoming\n                        // messages.\n                        if (body.method === 'eth_subscribe' &&\n                            typeof response.result === 'string')\n                            subscriptions.set(response.result, {\n                                onResponse: callback,\n                                onError,\n                            });\n                        // If we are unsubscribing from a topic, we want to remove the listener.\n                        if (body.method === 'eth_unsubscribe')\n                            subscriptions.delete(body.params?.[0]);\n                        onResponse(response);\n                    };\n                    requests.set(id, { onResponse: callback, onError });\n                    try {\n                        socket.request({\n                            body: {\n                                jsonrpc: '2.0',\n                                id,\n                                ...body,\n                            },\n                        });\n                    }\n                    catch (error) {\n                        onError?.(error);\n                    }\n                },\n                requestAsync({ body, timeout = 10_000 }) {\n                    return (0,_promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => new Promise((onResponse, onError) => this.request({\n                        body,\n                        onError,\n                        onResponse,\n                    })), {\n                        errorInstance: new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.TimeoutError({ body, url }),\n                        timeout,\n                    });\n                },\n                requests,\n                subscriptions,\n                url,\n            };\n            socketClientCache.set(`${key}:${url}`, socketClient);\n            return [socketClient];\n        },\n    });\n    const [_, [socketClient_]] = await schedule();\n    return socketClient_;\n}\n//# sourceMappingURL=socket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/socket.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/webSocket.js":
/*!*******************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/webSocket.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getWebSocketRpcClient: () => (/* binding */ getWebSocketRpcClient)\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./socket.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/socket.js\");\n\n\nasync function getWebSocketRpcClient(url, options = {}) {\n    const { keepAlive, reconnect } = options;\n    return (0,_socket_js__WEBPACK_IMPORTED_MODULE_0__.getSocketRpcClient)({\n        async getSocket({ onClose, onError, onOpen, onResponse }) {\n            const WebSocket = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_isows__esm_native_js\").then(__webpack_require__.bind(__webpack_require__, /*! isows */ \"(app-pages-browser)/./node_modules/isows/_esm/native.js\")).then((module) => module.WebSocket);\n            const socket = new WebSocket(url);\n            function onClose_() {\n                socket.removeEventListener('close', onClose_);\n                socket.removeEventListener('message', onMessage);\n                socket.removeEventListener('error', onError);\n                socket.removeEventListener('open', onOpen);\n                onClose();\n            }\n            function onMessage({ data }) {\n                try {\n                    const _data = JSON.parse(data);\n                    onResponse(_data);\n                }\n                catch (error) {\n                    onError(error);\n                }\n            }\n            // Setup event listeners for RPC & subscription responses.\n            socket.addEventListener('close', onClose_);\n            socket.addEventListener('message', onMessage);\n            socket.addEventListener('error', onError);\n            socket.addEventListener('open', onOpen);\n            // Wait for the socket to open.\n            if (socket.readyState === WebSocket.CONNECTING) {\n                await new Promise((resolve, reject) => {\n                    if (!socket)\n                        return;\n                    socket.onopen = resolve;\n                    socket.onerror = reject;\n                });\n            }\n            const { close: close_ } = socket;\n            return Object.assign(socket, {\n                close() {\n                    close_.bind(socket)();\n                    onClose_();\n                },\n                ping() {\n                    try {\n                        if (socket.readyState === socket.CLOSED ||\n                            socket.readyState === socket.CLOSING)\n                            throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.WebSocketRequestError({\n                                url: socket.url,\n                                cause: new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url: socket.url }),\n                            });\n                        const body = {\n                            jsonrpc: '2.0',\n                            method: 'net_version',\n                            params: [],\n                        };\n                        socket.send(JSON.stringify(body));\n                    }\n                    catch (error) {\n                        onError(error);\n                    }\n                },\n                request({ body }) {\n                    if (socket.readyState === socket.CLOSED ||\n                        socket.readyState === socket.CLOSING)\n                        throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.WebSocketRequestError({\n                            body,\n                            url: socket.url,\n                            cause: new _errors_request_js__WEBPACK_IMPORTED_MODULE_1__.SocketClosedError({ url: socket.url }),\n                        });\n                    return socket.send(JSON.stringify(body));\n                },\n            });\n        },\n        keepAlive,\n        reconnect,\n        url,\n    });\n}\n//# sourceMappingURL=webSocket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/rpc/webSocket.js\n"));

/***/ })

}]);