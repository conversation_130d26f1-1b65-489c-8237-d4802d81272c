"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fraxtal = void 0;
const chainConfig_js_1 = require("../../op-stack/chainConfig.js");
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const sourceId = 1;
exports.fraxtal = (0, defineChain_js_1.defineChain)({
    ...chainConfig_js_1.chainConfig,
    id: 252,
    name: 'Fraxtal',
    nativeCurrency: { name: 'Frax Ether', symbol: 'frxETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.frax.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'fraxscan',
            url: 'https://fraxscan.com',
            apiUrl: 'https://api.fraxscan.com/api',
        },
    },
    contracts: {
        ...chainConfig_js_1.chainConfig.contracts,
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 19135323,
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 19135323,
            },
        },
    },
    sourceId,
});
//# sourceMappingURL=fraxtal.js.map