"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wagmi";
exports.ids = ["vendor-chunks/@wagmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* binding */ coinbaseWallet)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\ncoinbaseWallet.type = 'coinbaseWallet';\nfunction coinbaseWallet(parameters = {}) {\n    if (parameters.version === '3' || parameters.headlessMode)\n        return version3(parameters);\n    return version4(parameters);\n}\nfunction version4(parameters) {\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                    params: 'instantOnboarding' in rest && rest.instantOnboarding\n                        ? [{ onboarding: 'instant' }]\n                        : [],\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close?.();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = (await provider.request({\n                method: 'eth_chainId',\n            }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                const preference = (() => {\n                    if (typeof parameters.preference === 'string')\n                        return { options: parameters.preference };\n                    return {\n                        ...parameters.preference,\n                        options: parameters.preference?.options ?? 'all',\n                    };\n                })();\n                const { createCoinbaseWalletSDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@noble\"), __webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/@coinbase\")]).then(__webpack_require__.bind(__webpack_require__, /*! @coinbase/wallet-sdk */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/index.js\"));\n                const sdk = createCoinbaseWalletSDK({\n                    ...parameters,\n                    appChainIds: config.chains.map((x) => x.id),\n                    preference,\n                });\n                walletProvider = sdk.getProvider();\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\nfunction version3(parameters) {\n    const reloadOnDisconnect = false;\n    let sdk;\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = await provider.request({\n                method: 'eth_chainId',\n            });\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const CoinbaseWalletSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@metamask\"), __webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/cbw-sdk\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/eth-block-tracker\"), __webpack_require__.e(\"vendor-chunks/readable-stream\"), __webpack_require__.e(\"vendor-chunks/eth-json-rpc-filters\"), __webpack_require__.e(\"vendor-chunks/sha.js\"), __webpack_require__.e(\"vendor-chunks/json-rpc-engine\"), __webpack_require__.e(\"vendor-chunks/keccak\"), __webpack_require__.e(\"vendor-chunks/eth-rpc-errors\"), __webpack_require__.e(\"vendor-chunks/async-mutex\"), __webpack_require__.e(\"vendor-chunks/inherits\"), __webpack_require__.e(\"vendor-chunks/tslib\"), __webpack_require__.e(\"vendor-chunks/xtend\"), __webpack_require__.e(\"vendor-chunks/util-deprecate\"), __webpack_require__.e(\"vendor-chunks/string_decoder\"), __webpack_require__.e(\"vendor-chunks/pify\"), __webpack_require__.e(\"vendor-chunks/json-rpc-random-id\"), __webpack_require__.e(\"vendor-chunks/fast-safe-stringify\"), __webpack_require__.e(\"vendor-chunks/eth-query\"), __webpack_require__.e(\"_d272\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! cbw-sdk */ \"(ssr)/./node_modules/cbw-sdk/dist/index.js\", 19));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                sdk = new CoinbaseWalletSDK({ ...parameters, reloadOnDisconnect });\n                // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.\n                const walletExtensionChainId = sdk.walletExtension?.getChainId();\n                const chain = config.chains.find((chain) => parameters.chainId\n                    ? chain.id === parameters.chainId\n                    : chain.id === walletExtensionChainId) || config.chains[0];\n                const chainId = parameters.chainId || chain?.id;\n                const jsonRpcUrl = parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0];\n                walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId);\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\n//# sourceMappingURL=coinbaseWallet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/exports/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* reexport safe */ _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_2__.coinbaseWallet),\n/* harmony export */   injected: () => (/* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_0__.injected),\n/* harmony export */   metaMask: () => (/* reexport safe */ _metaMask_js__WEBPACK_IMPORTED_MODULE_3__.metaMask),\n/* harmony export */   mock: () => (/* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.mock),\n/* harmony export */   safe: () => (/* reexport safe */ _safe_js__WEBPACK_IMPORTED_MODULE_4__.safe),\n/* harmony export */   version: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_6__.version),\n/* harmony export */   walletConnect: () => (/* reexport safe */ _walletConnect_js__WEBPACK_IMPORTED_MODULE_5__.walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\");\n/* harmony import */ var _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../coinbaseWallet.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\");\n/* harmony import */ var _metaMask_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../metaMask.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\");\n/* harmony import */ var _safe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../safe.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/safe.js\");\n/* harmony import */ var _walletConnect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../walletConnect.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/version.js\");\n// biome-ignore lint/performance/noBarrelFile: entrypoint module\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2Nvbm5lY3RvcnMvZGlzdC9lc20vZXhwb3J0cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQzhDO0FBQ1M7QUFDYjtBQUNSO0FBQ21CO0FBQ2I7QUFDeEMiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb25uZWN0b3JzXFxkaXN0XFxlc21cXGV4cG9ydHNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGJpb21lLWlnbm9yZSBsaW50L3BlcmZvcm1hbmNlL25vQmFycmVsRmlsZTogZW50cnlwb2ludCBtb2R1bGVcbmV4cG9ydCB7IGluamVjdGVkLCBtb2NrLCB9IGZyb20gJ0B3YWdtaS9jb3JlJztcbmV4cG9ydCB7IGNvaW5iYXNlV2FsbGV0LCB9IGZyb20gJy4uL2NvaW5iYXNlV2FsbGV0LmpzJztcbmV4cG9ydCB7IG1ldGFNYXNrIH0gZnJvbSAnLi4vbWV0YU1hc2suanMnO1xuZXhwb3J0IHsgc2FmZSB9IGZyb20gJy4uL3NhZmUuanMnO1xuZXhwb3J0IHsgd2FsbGV0Q29ubmVjdCwgfSBmcm9tICcuLi93YWxsZXRDb25uZWN0LmpzJztcbmV4cG9ydCB7IHZlcnNpb24gfSBmcm9tICcuLi92ZXJzaW9uLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: () => (/* binding */ metaMask)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@metamask\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/engine.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-parser\"), __webpack_require__.e(\"vendor-chunks/engine.io-parser\"), __webpack_require__.e(\"vendor-chunks/@socket.io\"), __webpack_require__.e(\"vendor-chunks/xmlhttprequest-ssl\"), __webpack_require__.e(\"vendor-chunks/eventemitter2\"), __webpack_require__.e(\"vendor-chunks/cross-fetch\"), __webpack_require__.e(\"_d272-_3dc1-_d04b-_bf50\")]).then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(ssr)/./node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2Nvbm5lY3RvcnMvZGlzdC9lc20vbWV0YU1hc2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0c7QUFDK0M7QUFDOUo7QUFDTyxpQ0FBaUM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsNERBQWU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULHdCQUF3QiwwQkFBMEIsSUFBSTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSxnREFBVTtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFNBQVM7QUFDcEUsMkNBQTJDLDBEQUF3QjtBQUNuRTtBQUNBLGlDQUFpQztBQUNqQyxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsMERBQXdCO0FBQzNELDhCQUE4QiwwREFBd0I7QUFDdEQsbUNBQW1DLDZEQUEyQjtBQUM5RCw4QkFBOEIsNkRBQTJCO0FBQ3pEO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsdUNBQXVDLGdEQUFVO0FBQ2pELFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsdUJBQXVCO0FBQ2xFO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsZUFBZSxRQUFRLG95QkFBdUI7QUFDMUU7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxtQ0FBbUMsaURBQVcsY0FBYywyREFBYztBQUMxRTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSw4QkFBOEIsOERBQXFCO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QywrQ0FBUyxPQUFPLGlEQUFXLDZCQUE2QixTQUFTO0FBQ3hHO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCw0QkFBNEIsb0NBQW9DO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixrREFBZ0IsS0FBSyxnRUFBdUI7QUFDdEU7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLFNBQVMsaURBQVcsV0FBVztBQUM5RCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQywwREFBd0I7QUFDM0QsOEJBQThCLDBEQUF3QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsNENBQTRDO0FBQzVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckMsNkNBQTZDLGlEQUFXO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckMsaUNBQWlDO0FBQ2pDO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQywwREFBd0I7QUFDbkUsc0NBQXNDLDBEQUF3QjtBQUM5RCxrQ0FBa0Msa0RBQWdCO0FBQ2xEO0FBQ0E7QUFDQSwwQkFBMEIsa0RBQWdCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLCtDQUFTO0FBQy9CLGtDQUFrQyxpREFBVztBQUM3QztBQUNBLDhDQUE4Qyx1QkFBdUI7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0Esb0RBQW9ELFNBQVM7QUFDN0QsaUJBQWlCO0FBQ2pCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLFNBQVM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsZ0RBQVU7QUFDNUQsaUJBQWlCO0FBQ2pCLFNBQVM7QUFDVDtBQUNBO0FBQ0EsNENBQTRDLFNBQVM7QUFDckQsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsbUJBQW1CO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw2Q0FBNkMsZ0NBQWdDO0FBQzdFLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvbm5lY3RvcnNcXGRpc3RcXGVzbVxcbWV0YU1hc2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IsIFByb3ZpZGVyTm90Rm91bmRFcnJvciwgY3JlYXRlQ29ubmVjdG9yLCBleHRyYWN0UnBjVXJscywgfSBmcm9tICdAd2FnbWkvY29yZSc7XG5pbXBvcnQgeyBSZXNvdXJjZVVuYXZhaWxhYmxlUnBjRXJyb3IsIFN3aXRjaENoYWluRXJyb3IsIFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvciwgZ2V0QWRkcmVzcywgaGV4VG9OdW1iZXIsIG51bWJlclRvSGV4LCB3aXRoUmV0cnksIHdpdGhUaW1lb3V0LCB9IGZyb20gJ3ZpZW0nO1xubWV0YU1hc2sudHlwZSA9ICdtZXRhTWFzayc7XG5leHBvcnQgZnVuY3Rpb24gbWV0YU1hc2socGFyYW1ldGVycyA9IHt9KSB7XG4gICAgbGV0IHNkaztcbiAgICBsZXQgcHJvdmlkZXI7XG4gICAgbGV0IHByb3ZpZGVyUHJvbWlzZTtcbiAgICBsZXQgYWNjb3VudHNDaGFuZ2VkO1xuICAgIGxldCBjaGFpbkNoYW5nZWQ7XG4gICAgbGV0IGNvbm5lY3Q7XG4gICAgbGV0IGRpc3BsYXlVcmk7XG4gICAgbGV0IGRpc2Nvbm5lY3Q7XG4gICAgcmV0dXJuIGNyZWF0ZUNvbm5lY3RvcigoY29uZmlnKSA9PiAoe1xuICAgICAgICBpZDogJ21ldGFNYXNrU0RLJyxcbiAgICAgICAgbmFtZTogJ01ldGFNYXNrJyxcbiAgICAgICAgcmRuczogWydpby5tZXRhbWFzaycsICdpby5tZXRhbWFzay5tb2JpbGUnXSxcbiAgICAgICAgdHlwZTogbWV0YU1hc2sudHlwZSxcbiAgICAgICAgYXN5bmMgc2V0dXAoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGlmIChwcm92aWRlcj8ub24pIHtcbiAgICAgICAgICAgICAgICBpZiAoIWNvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdCA9IHRoaXMub25Db25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFdlIHNob3VsZG4ndCBuZWVkIHRvIGxpc3RlbiBmb3IgYCdhY2NvdW50c0NoYW5nZWQnYCBoZXJlIHNpbmNlIHRoZSBgJ2Nvbm5lY3QnYCBldmVudCBzaG91bGQgc3VmZmljZSAoYW5kIHdhbGxldCBzaG91bGRuJ3QgYmUgY29ubmVjdGVkIHlldCkuXG4gICAgICAgICAgICAgICAgLy8gU29tZSB3YWxsZXRzLCBsaWtlIE1ldGFNYXNrLCBkbyBub3QgaW1wbGVtZW50IHRoZSBgJ2Nvbm5lY3QnYCBldmVudCBhbmQgb3ZlcmxvYWQgYCdhY2NvdW50c0NoYW5nZWQnYCBpbnN0ZWFkLlxuICAgICAgICAgICAgICAgIGlmICghYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgICAgIGFjY291bnRzQ2hhbmdlZCA9IHRoaXMub25BY2NvdW50c0NoYW5nZWQuYmluZCh0aGlzKTtcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2FjY291bnRzQ2hhbmdlZCcsIGFjY291bnRzQ2hhbmdlZCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBjb25uZWN0KHsgY2hhaW5JZCwgaXNSZWNvbm5lY3RpbmcgfSA9IHt9KSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGlmICghZGlzcGxheVVyaSkge1xuICAgICAgICAgICAgICAgIGRpc3BsYXlVcmkgPSB0aGlzLm9uRGlzcGxheVVyaTtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzcGxheV91cmknLCBkaXNwbGF5VXJpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBhY2NvdW50cyA9IFtdO1xuICAgICAgICAgICAgaWYgKGlzUmVjb25uZWN0aW5nKVxuICAgICAgICAgICAgICAgIGFjY291bnRzID0gYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpLmNhdGNoKCgpID0+IFtdKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgbGV0IHNpZ25SZXNwb25zZTtcbiAgICAgICAgICAgICAgICBsZXQgY29ubmVjdFdpdGhSZXNwb25zZTtcbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzPy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdEFuZFNpZ24gfHwgcGFyYW1ldGVycy5jb25uZWN0V2l0aCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdEFuZFNpZ24pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnblJlc3BvbnNlID0gYXdhaXQgc2RrLmNvbm5lY3RBbmRTaWduKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbXNnOiBwYXJhbWV0ZXJzLmNvbm5lY3RBbmRTaWduLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAocGFyYW1ldGVycy5jb25uZWN0V2l0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25uZWN0V2l0aFJlc3BvbnNlID0gYXdhaXQgc2RrLmNvbm5lY3RXaXRoKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiBwYXJhbWV0ZXJzLmNvbm5lY3RXaXRoLm1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBwYXJhbWV0ZXJzLmNvbm5lY3RXaXRoLnBhcmFtcyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY291bnRzID0gYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVxdWVzdGVkQWNjb3VudHMgPSAoYXdhaXQgc2RrLmNvbm5lY3QoKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NvdW50cyA9IHJlcXVlc3RlZEFjY291bnRzLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gU3dpdGNoIHRvIGNoYWluIGlmIHByb3ZpZGVkXG4gICAgICAgICAgICAgICAgbGV0IGN1cnJlbnRDaGFpbklkID0gKGF3YWl0IHRoaXMuZ2V0Q2hhaW5JZCgpKTtcbiAgICAgICAgICAgICAgICBpZiAoY2hhaW5JZCAmJiBjdXJyZW50Q2hhaW5JZCAhPT0gY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGFpbiA9IGF3YWl0IHRoaXMuc3dpdGNoQ2hhaW4oeyBjaGFpbklkIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgaWQ6IGN1cnJlbnRDaGFpbklkIH07XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhaW5JZCA9IGNoYWluPy5pZCA/PyBjdXJyZW50Q2hhaW5JZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGRpc3BsYXlVcmkpIHtcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc3BsYXlfdXJpJywgZGlzcGxheVVyaSk7XG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXlVcmkgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChzaWduUmVzcG9uc2UpXG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLmVtaXQoJ2Nvbm5lY3RBbmRTaWduJywge1xuICAgICAgICAgICAgICAgICAgICAgICAgYWNjb3VudHMsXG4gICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBjdXJyZW50Q2hhaW5JZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25SZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoY29ubmVjdFdpdGhSZXNwb25zZSlcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIuZW1pdCgnY29ubmVjdFdpdGgnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NvdW50cyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluSWQ6IGN1cnJlbnRDaGFpbklkLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29ubmVjdFdpdGhSZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gTWFuYWdlIEVJUC0xMTkzIGV2ZW50IGxpc3RlbmVyc1xuICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZWlwcy5ldGhlcmV1bS5vcmcvRUlQUy9laXAtMTE5MyNldmVudHNcbiAgICAgICAgICAgICAgICBpZiAoY29ubmVjdCkge1xuICAgICAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY29ubmVjdCcsIGNvbm5lY3QpO1xuICAgICAgICAgICAgICAgICAgICBjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBhY2NvdW50c0NoYW5nZWQgPSB0aGlzLm9uQWNjb3VudHNDaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHRoaXMub25EaXNjb25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB7IGFjY291bnRzLCBjaGFpbklkOiBjdXJyZW50Q2hhaW5JZCB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZXJyO1xuICAgICAgICAgICAgICAgIGlmIChlcnJvci5jb2RlID09PSBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IuY29kZSlcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvcihlcnJvcik7XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFJlc291cmNlVW5hdmFpbGFibGVScGNFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUmVzb3VyY2VVbmF2YWlsYWJsZVJwY0Vycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZGlzY29ubmVjdCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgLy8gTWFuYWdlIEVJUC0xMTkzIGV2ZW50IGxpc3RlbmVyc1xuICAgICAgICAgICAgaWYgKGNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGNoYWluQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChkaXNjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc2Nvbm5lY3QnLCBkaXNjb25uZWN0KTtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgY29ubmVjdCA9IHRoaXMub25Db25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2Nvbm5lY3QnLCBjb25uZWN0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGF3YWl0IHNkay50ZXJtaW5hdGUoKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0QWNjb3VudHMoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGNvbnN0IGFjY291bnRzID0gKGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgIG1ldGhvZDogJ2V0aF9hY2NvdW50cycsXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICByZXR1cm4gYWNjb3VudHMubWFwKCh4KSA9PiBnZXRBZGRyZXNzKHgpKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0Q2hhaW5JZCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IHByb3ZpZGVyLmdldENoYWluSWQoKSB8fFxuICAgICAgICAgICAgICAgIChhd2FpdCBwcm92aWRlcj8ucmVxdWVzdCh7IG1ldGhvZDogJ2V0aF9jaGFpbklkJyB9KSk7XG4gICAgICAgICAgICByZXR1cm4gTnVtYmVyKGNoYWluSWQpO1xuICAgICAgICB9LFxuICAgICAgICBhc3luYyBnZXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIGluaXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgICAgICAvLyBVbndyYXBwaW5nIGltcG9ydCBmb3IgVml0ZSBjb21wYXRpYmlsaXR5LlxuICAgICAgICAgICAgICAgIC8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3ZpdGVqcy92aXRlL2lzc3Vlcy85NzAzXG4gICAgICAgICAgICAgICAgY29uc3QgTWV0YU1hc2tTREsgPSBhd2FpdCAoYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGRlZmF1bHQ6IFNESyB9ID0gYXdhaXQgaW1wb3J0KCdAbWV0YW1hc2svc2RrJyk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgU0RLICE9PSAnZnVuY3Rpb24nICYmIHR5cGVvZiBTREsuZGVmYXVsdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBTREsuZGVmYXVsdDtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFNESztcbiAgICAgICAgICAgICAgICB9KSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlYWRvbmx5UlBDTWFwID0ge307XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBjaGFpbiBvZiBjb25maWcuY2hhaW5zKVxuICAgICAgICAgICAgICAgICAgICByZWFkb25seVJQQ01hcFtudW1iZXJUb0hleChjaGFpbi5pZCldID0gZXh0cmFjdFJwY1VybHMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgY2hhaW4sXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc3BvcnRzOiBjb25maWcudHJhbnNwb3J0cyxcbiAgICAgICAgICAgICAgICAgICAgfSk/LlswXTtcbiAgICAgICAgICAgICAgICBzZGsgPSBuZXcgTWV0YU1hc2tTREsoe1xuICAgICAgICAgICAgICAgICAgICBfc291cmNlOiAnd2FnbWknLFxuICAgICAgICAgICAgICAgICAgICBmb3JjZURlbGV0ZVByb3ZpZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgZm9yY2VJbmplY3RQcm92aWRlcjogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGluamVjdFByb3ZpZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgLy8gV29ya2Fyb3VuZCBjYXN0IHNpbmNlIE1ldGFNYXNrIFNESyBkb2VzIG5vdCBzdXBwb3J0IGAnZXhhY3RPcHRpb25hbFByb3BlcnR5VHlwZXMnYFxuICAgICAgICAgICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICAgICAgICAgICAgICByZWFkb25seVJQQ01hcCxcbiAgICAgICAgICAgICAgICAgICAgZGFwcE1ldGFkYXRhOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRlc3QgaWYgbmFtZSBhbmQgdXJsIGFyZSBzZXQgQU5EIG5vdCBlbXB0eVxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogcGFyYW1ldGVycy5kYXBwTWV0YWRhdGE/Lm5hbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHBhcmFtZXRlcnMuZGFwcE1ldGFkYXRhPy5uYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnd2FnbWknLFxuICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiBwYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YT8udXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBwYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YT8udXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnaHR0cHM6Ly93YWdtaS5zaCcsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHVzZURlZXBsaW5rOiBwYXJhbWV0ZXJzLnVzZURlZXBsaW5rID8/IHRydWUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2RrLmluaXQoKTtcbiAgICAgICAgICAgICAgICAvLyBPbiBpbml0aWFsIGxvYWQsIHNvbWV0aW1lcyBgc2RrLmdldFByb3ZpZGVyYCBkb2VzIG5vdCByZXR1cm4gcHJvdmlkZXIuXG4gICAgICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3dldm0vd2FnbWkvaXNzdWVzLzQzNjdcbiAgICAgICAgICAgICAgICAvLyBVc2UgcmVzdWx0IG9mIGBpbml0YCBjYWxsIGlmIGF2YWlsYWJsZS5cbiAgICAgICAgICAgICAgICBjb25zdCBwcm92aWRlciA9ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXN1bHQ/LmFjdGl2ZVByb3ZpZGVyKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdC5hY3RpdmVQcm92aWRlcjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNkay5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgICAgIH0pKCk7XG4gICAgICAgICAgICAgICAgaWYgKCFwcm92aWRlcilcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFByb3ZpZGVyTm90Rm91bmRFcnJvcigpO1xuICAgICAgICAgICAgICAgIHJldHVybiBwcm92aWRlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghcHJvdmlkZXIpIHtcbiAgICAgICAgICAgICAgICBpZiAoIXByb3ZpZGVyUHJvbWlzZSlcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXJQcm9taXNlID0gaW5pdFByb3ZpZGVyKCk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIgPSBhd2FpdCBwcm92aWRlclByb21pc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcHJvdmlkZXI7XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGlzQXV0aG9yaXplZCgpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8gTWV0YU1hc2sgbW9iaWxlIHByb3ZpZGVyIHNvbWV0aW1lcyBmYWlscyB0byBpbW1lZGlhdGVseSByZXNvbHZlXG4gICAgICAgICAgICAgICAgLy8gSlNPTi1SUEMgcmVxdWVzdHMgb24gcGFnZSBsb2FkXG4gICAgICAgICAgICAgICAgY29uc3QgdGltZW91dCA9IDIwMDtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHdpdGhSZXRyeSgoKSA9PiB3aXRoVGltZW91dCgoKSA9PiB0aGlzLmdldEFjY291bnRzKCksIHsgdGltZW91dCB9KSwge1xuICAgICAgICAgICAgICAgICAgICBkZWxheTogdGltZW91dCArIDEsXG4gICAgICAgICAgICAgICAgICAgIHJldHJ5Q291bnQ6IDMsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuICEhYWNjb3VudHMubGVuZ3RoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgc3dpdGNoQ2hhaW4oeyBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLCBjaGFpbklkIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKHgpID0+IHguaWQgPT09IGNoYWluSWQpO1xuICAgICAgICAgICAgaWYgKCFjaGFpbilcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgU3dpdGNoQ2hhaW5FcnJvcihuZXcgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IoKSk7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfc3dpdGNoRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtczogW3sgY2hhaW5JZDogbnVtYmVyVG9IZXgoY2hhaW5JZCkgfV0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gRHVyaW5nIGAnd2FsbGV0X3N3aXRjaEV0aGVyZXVtQ2hhaW4nYCwgTWV0YU1hc2sgbWFrZXMgYSBgJ25ldF92ZXJzaW9uJ2AgUlBDIGNhbGwgdG8gdGhlIHRhcmdldCBjaGFpbi5cbiAgICAgICAgICAgICAgICAvLyBJZiB0aGlzIHJlcXVlc3QgZmFpbHMsIE1ldGFNYXNrIGRvZXMgbm90IGVtaXQgdGhlIGAnY2hhaW5DaGFuZ2VkJ2AgZXZlbnQsIGJ1dCB3aWxsIHN0aWxsIHN3aXRjaCB0aGUgY2hhaW4uXG4gICAgICAgICAgICAgICAgLy8gVG8gY291bnRlciB0aGlzIGJlaGF2aW9yLCB3ZSByZXF1ZXN0IGFuZCBlbWl0IHRoZSBjdXJyZW50IGNoYWluIElEIHRvIGNvbmZpcm0gdGhlIGNoYWluIHN3aXRjaCBlaXRoZXIgdmlhXG4gICAgICAgICAgICAgICAgLy8gdGhpcyBjYWxsYmFjayBvciBhbiBleHRlcm5hbGx5IGVtaXR0ZWQgYCdjaGFpbkNoYW5nZWQnYCBldmVudC5cbiAgICAgICAgICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vTWV0YU1hc2svbWV0YW1hc2stZXh0ZW5zaW9uL2lzc3Vlcy8yNDI0N1xuICAgICAgICAgICAgICAgIGF3YWl0IHdhaXRGb3JDaGFpbklkVG9TeW5jKCk7XG4gICAgICAgICAgICAgICAgYXdhaXQgc2VuZEFuZFdhaXRGb3JDaGFuZ2VFdmVudChjaGFpbklkKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBlcnI7XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgVXNlclJlamVjdGVkUmVxdWVzdEVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBJbmRpY2F0ZXMgY2hhaW4gaXMgbm90IGFkZGVkIHRvIHByb3ZpZGVyXG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQ5MDIgfHxcbiAgICAgICAgICAgICAgICAgICAgLy8gVW53cmFwcGluZyBmb3IgTWV0YU1hc2sgTW9iaWxlXG4gICAgICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9NZXRhTWFzay9tZXRhbWFzay1tb2JpbGUvaXNzdWVzLzI5NDQjaXNzdWVjb21tZW50LTk3Njk4ODcxOVxuICAgICAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgPy5kYXRhPy5vcmlnaW5hbEVycm9yPy5jb2RlID09PSA0OTAyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJsb2NrRXhwbG9yZXJVcmxzOiAoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgZGVmYXVsdDogYmxvY2tFeHBsb3JlciwgLi4uYmxvY2tFeHBsb3JlcnMgfSA9IGNoYWluLmJsb2NrRXhwbG9yZXJzID8/IHt9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5ibG9ja0V4cGxvcmVyVXJscylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIuYmxvY2tFeHBsb3JlclVybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGJsb2NrRXhwbG9yZXIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyLnVybCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLk9iamVjdC52YWx1ZXMoYmxvY2tFeHBsb3JlcnMpLm1hcCgoeCkgPT4geC51cmwpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKCksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBudW1iZXJUb0hleChjaGFpbklkKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluTmFtZTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8uY2hhaW5OYW1lID8/IGNoYWluLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uVXJsczogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8uaWNvblVybHMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYXRpdmVDdXJyZW5jeTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ubmF0aXZlQ3VycmVuY3kgPz9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbi5uYXRpdmVDdXJyZW5jeSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJwY1VybHM6ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXI/LnJwY1VybHM/Lmxlbmd0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIucnBjVXJscztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gW2NoYWluLnJwY1VybHMuZGVmYXVsdD8uaHR0cFswXSA/PyAnJ107XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IHdhaXRGb3JDaGFpbklkVG9TeW5jKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBzZW5kQW5kV2FpdEZvckNoYW5nZUV2ZW50KGNoYWluSWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNoYWluO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZXJyO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFN3aXRjaENoYWluRXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIHdhaXRGb3JDaGFpbklkVG9TeW5jKCkge1xuICAgICAgICAgICAgICAgIC8vIE9uIG1vYmlsZSwgdGhlcmUgaXMgYSByYWNlIGNvbmRpdGlvbiBiZXR3ZWVuIHRoZSByZXN1bHQgb2YgYCd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbidgIGFuZCBgJ2V0aF9jaGFpbklkJ2AuXG4gICAgICAgICAgICAgICAgLy8gVG8gYXZvaWQgdGhpcywgd2Ugd2FpdCBmb3IgYCdldGhfY2hhaW5JZCdgIHRvIHJldHVybiB0aGUgZXhwZWN0ZWQgY2hhaW4gSUQgd2l0aCBhIHJldHJ5IGxvb3AuXG4gICAgICAgICAgICAgICAgYXdhaXQgd2l0aFJldHJ5KGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBoZXhUb051bWJlcihcbiAgICAgICAgICAgICAgICAgICAgLy8gYCdldGhfY2hhaW5JZCdgIGlzIGNhY2hlZCBieSB0aGUgTWV0YU1hc2sgU0RLIHNpZGUgdG8gYXZvaWQgdW5uZWNlc3NhcnkgZGVlcGxpbmtzXG4gICAgICAgICAgICAgICAgICAgIChhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHsgbWV0aG9kOiAnZXRoX2NoYWluSWQnIH0pKSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIGB2YWx1ZWAgZG9lc24ndCBtYXRjaCBleHBlY3RlZCBgY2hhaW5JZGAsIHRocm93IHRvIHRyaWdnZXIgcmV0cnlcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbHVlICE9PSBjaGFpbklkKVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIHJlamVjdGVkIHN3aXRjaCBhZnRlciBhZGRpbmcgbmV0d29yay4nKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICAgICAgZGVsYXk6IDUwLFxuICAgICAgICAgICAgICAgICAgICByZXRyeUNvdW50OiAyMCwgLy8gYW5kcm9pZCBkZXZpY2UgZW5jcnlwdGlvbiBpcyBzbG93ZXJcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIHNlbmRBbmRXYWl0Rm9yQ2hhbmdlRXZlbnQoY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxpc3RlbmVyID0gKChkYXRhKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoJ2NoYWluSWQnIGluIGRhdGEgJiYgZGF0YS5jaGFpbklkID09PSBjaGFpbklkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIub2ZmKCdjaGFuZ2UnLCBsaXN0ZW5lcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIub24oJ2NoYW5nZScsIGxpc3RlbmVyKTtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIuZW1pdCgnY2hhbmdlJywgeyBjaGFpbklkIH0pO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBvbkFjY291bnRzQ2hhbmdlZChhY2NvdW50cykge1xuICAgICAgICAgICAgLy8gRGlzY29ubmVjdCBpZiB0aGVyZSBhcmUgbm8gYWNjb3VudHNcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICAvLyAuLi4gYW5kIHVzaW5nIGJyb3dzZXIgZXh0ZW5zaW9uXG4gICAgICAgICAgICAgICAgaWYgKHNkay5pc0V4dGVuc2lvbkFjdGl2ZSgpKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLm9uRGlzY29ubmVjdCgpO1xuICAgICAgICAgICAgICAgIC8vIEZJWE1FKHVwc3RyZWFtKTogTW9iaWxlIGFwcCBzb21ldGltZXMgZW1pdHMgaW52YWxpZCBgYWNjb3VudHNDaGFuZ2VkYCBldmVudCB3aXRoIGVtcHR5IGFjY291bnRzIGFycmF5XG4gICAgICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBDb25uZWN0IGlmIGVtaXR0ZXIgaXMgbGlzdGVuaW5nIGZvciBjb25uZWN0IGV2ZW50IChlLmcuIGlzIGRpc2Nvbm5lY3RlZCBhbmQgY29ubmVjdHMgdGhyb3VnaCB3YWxsZXQgaW50ZXJmYWNlKVxuICAgICAgICAgICAgZWxzZSBpZiAoY29uZmlnLmVtaXR0ZXIubGlzdGVuZXJDb3VudCgnY29ubmVjdCcpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IChhd2FpdCB0aGlzLmdldENoYWluSWQoKSkudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICB0aGlzLm9uQ29ubmVjdCh7IGNoYWluSWQgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBSZWd1bGFyIGNoYW5nZSBldmVudFxuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2NoYW5nZScsIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjb3VudHM6IGFjY291bnRzLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uQ2hhaW5DaGFuZ2VkKGNoYWluKSB7XG4gICAgICAgICAgICBjb25zdCBjaGFpbklkID0gTnVtYmVyKGNoYWluKTtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2NoYW5nZScsIHsgY2hhaW5JZCB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgb25Db25uZWN0KGNvbm5lY3RJbmZvKSB7XG4gICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHRoaXMuZ2V0QWNjb3VudHMoKTtcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IE51bWJlcihjb25uZWN0SW5mby5jaGFpbklkKTtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Nvbm5lY3QnLCB7IGFjY291bnRzLCBjaGFpbklkIH0pO1xuICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICBpZiAoY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgYWNjb3VudHNDaGFuZ2VkID0gdGhpcy5vbkFjY291bnRzQ2hhbmdlZC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2NoYWluQ2hhbmdlZCcsIGNoYWluQ2hhbmdlZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdGhpcy5vbkRpc2Nvbm5lY3QuYmluZCh0aGlzKTtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzY29ubmVjdCcsIGRpc2Nvbm5lY3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBvbkRpc2Nvbm5lY3QoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgLy8gSWYgTWV0YU1hc2sgZW1pdHMgYSBgY29kZTogMTAxM2AgZXJyb3IsIHdhaXQgZm9yIHJlY29ubmVjdGlvbiBiZWZvcmUgZGlzY29ubmVjdGluZ1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL01ldGFNYXNrL3Byb3ZpZGVycy9wdWxsLzEyMFxuICAgICAgICAgICAgaWYgKGVycm9yICYmIGVycm9yLmNvZGUgPT09IDEwMTMpIHtcbiAgICAgICAgICAgICAgICBpZiAocHJvdmlkZXIgJiYgISEoYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpKS5sZW5ndGgpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Rpc2Nvbm5lY3QnKTtcbiAgICAgICAgICAgIC8vIE1hbmFnZSBFSVAtMTE5MyBldmVudCBsaXN0ZW5lcnNcbiAgICAgICAgICAgIGlmIChjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGlzY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIGNvbm5lY3QgPSB0aGlzLm9uQ29ubmVjdC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG9uRGlzcGxheVVyaSh1cmkpIHtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ21lc3NhZ2UnLCB7IHR5cGU6ICdkaXNwbGF5X3VyaScsIGRhdGE6IHVyaSB9KTtcbiAgICAgICAgfSxcbiAgICB9KSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXRhTWFzay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/safe.js":
/*!*********************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/safe.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n\n\nsafe.type = 'safe';\nfunction safe(parameters = {}) {\n    const { shimDisconnect = false } = parameters;\n    let provider_;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'safe',\n        name: 'Safe',\n        type: safe.type,\n        async connect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await this.getAccounts();\n            const chainId = await this.getChainId();\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n            // Remove disconnected shim if it exists\n            if (shimDisconnect)\n                await config.storage?.removeItem('safe.disconnected');\n            return { accounts, chainId };\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect)\n                await config.storage?.setItem('safe.disconnected', true);\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return (await provider.request({ method: 'eth_accounts' })).map(viem__WEBPACK_IMPORTED_MODULE_2__.getAddress);\n        },\n        async getProvider() {\n            // Only allowed in iframe context\n            const isIframe = typeof window !== 'undefined' && window?.parent !== window;\n            if (!isIframe)\n                return;\n            if (!provider_) {\n                const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/viem\"), __webpack_require__.e(\"vendor-chunks/@safe-global\")]).then(__webpack_require__.bind(__webpack_require__, /*! @safe-global/safe-apps-sdk */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\"));\n                const sdk = new SDK(parameters);\n                // `getInfo` hangs when not used in Safe App iFrame\n                // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********\n                const safe = await (0,viem__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => sdk.safe.getInfo(), {\n                    timeout: parameters.unstable_getInfoTimeout ?? 10,\n                });\n                if (!safe)\n                    throw new Error('Could not load Safe information');\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const SafeAppProvider = await (async () => {\n                    const Provider = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/viem\"), __webpack_require__.e(\"vendor-chunks/@noble\"), __webpack_require__.e(\"vendor-chunks/ox\"), __webpack_require__.e(\"vendor-chunks/abitype\"), __webpack_require__.e(\"vendor-chunks/@safe-global\"), __webpack_require__.e(\"vendor-chunks/isows\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @safe-global/safe-apps-provider */ \"(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/index.js\", 19));\n                    if (typeof Provider.SafeAppProvider !== 'function' &&\n                        typeof Provider.default.SafeAppProvider === 'function')\n                        return Provider.default.SafeAppProvider;\n                    return Provider.SafeAppProvider;\n                })();\n                provider_ = new SafeAppProvider(safe, sdk);\n            }\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return Number(provider.chainId);\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem('safe.disconnected'));\n                if (isDisconnected)\n                    return false;\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        onAccountsChanged() {\n            // Not relevant for Safe because changing account requires app reload.\n        },\n        onChainChanged() {\n            // Not relevant for Safe because Safe smart contract wallets only exist on single chain.\n        },\n        onDisconnect() {\n            config.emitter.emit('disconnect');\n        },\n    }));\n}\n//# sourceMappingURL=safe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/safe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/version.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/version.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '5.8.3';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2Nvbm5lY3RvcnMvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvbm5lY3RvcnNcXGRpc3RcXGVzbVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICc1LjguMyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/walletConnect.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnect: () => (/* binding */ walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\nwalletConnect.type = 'walletConnect';\nfunction walletConnect(parameters) {\n    const isNewChainsStale = parameters.isNewChainsStale ?? true;\n    let provider_;\n    let providerPromise;\n    const NAMESPACE = 'eip155';\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let sessionDelete;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'walletConnect',\n        name: 'WalletConnect',\n        type: walletConnect.type,\n        async setup() {\n            const provider = await this.getProvider().catch(() => null);\n            if (!provider)\n                return;\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            if (!sessionDelete) {\n                sessionDelete = this.onSessionDelete.bind(this);\n                provider.on('session_delete', sessionDelete);\n            }\n        },\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                if (!displayUri) {\n                    displayUri = this.onDisplayUri;\n                    provider.on('display_uri', displayUri);\n                }\n                let targetChainId = chainId;\n                if (!targetChainId) {\n                    const state = (await config.storage?.getItem('state')) ?? {};\n                    const isChainSupported = config.chains.some((x) => x.id === state.chainId);\n                    if (isChainSupported)\n                        targetChainId = state.chainId;\n                    else\n                        targetChainId = config.chains[0]?.id;\n                }\n                if (!targetChainId)\n                    throw new Error('No chains found on connector.');\n                const isChainsStale = await this.isChainsStale();\n                // If there is an active session with stale chains, disconnect current session.\n                if (provider.session && isChainsStale)\n                    await provider.disconnect();\n                // If there isn't an active session or chains are stale, connect.\n                if (!provider.session || isChainsStale) {\n                    const optionalChains = config.chains\n                        .filter((chain) => chain.id !== targetChainId)\n                        .map((optionalChain) => optionalChain.id);\n                    await provider.connect({\n                        optionalChains: [targetChainId, ...optionalChains],\n                        ...('pairingTopic' in rest\n                            ? { pairingTopic: rest.pairingTopic }\n                            : {}),\n                    });\n                    this.setRequestedChainsIds(config.chains.map((x) => x.id));\n                }\n                // If session exists and chains are authorized, enable provider for required chain\n                const accounts = (await provider.enable()).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                const currentChainId = await this.getChainId();\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                if (!sessionDelete) {\n                    sessionDelete = this.onSessionDelete.bind(this);\n                    provider.on('session_delete', sessionDelete);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user rejected|connection request reset)/i.test(error?.message)) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            try {\n                await provider?.disconnect();\n            }\n            catch (error) {\n                if (!/No matching key/i.test(error.message))\n                    throw error;\n            }\n            finally {\n                if (chainChanged) {\n                    provider?.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider?.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider?.on('connect', connect);\n                }\n                if (accountsChanged) {\n                    provider?.removeListener('accountsChanged', accountsChanged);\n                    accountsChanged = undefined;\n                }\n                if (sessionDelete) {\n                    provider?.removeListener('session_delete', sessionDelete);\n                    sessionDelete = undefined;\n                }\n                this.setRequestedChainsIds([]);\n            }\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return provider.accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getProvider({ chainId } = {}) {\n            async function initProvider() {\n                const optionalChains = config.chains.map((x) => x.id);\n                if (!optionalChains.length)\n                    return;\n                const { EthereumProvider } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@walletconnect\").then(__webpack_require__.bind(__webpack_require__, /*! @walletconnect/ethereum-provider */ \"(ssr)/./node_modules/@walletconnect/ethereum-provider/dist/index.es.js\"));\n                return await EthereumProvider.init({\n                    ...parameters,\n                    disableProviderPing: true,\n                    optionalChains,\n                    projectId: parameters.projectId,\n                    rpcMap: Object.fromEntries(config.chains.map((chain) => {\n                        const [url] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                            chain,\n                            transports: config.transports,\n                        });\n                        return [chain.id, url];\n                    })),\n                    showQrModal: parameters.showQrModal ?? true,\n                });\n            }\n            if (!provider_) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider_ = await providerPromise;\n                provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY);\n            }\n            if (chainId)\n                await this.switchChain?.({ chainId });\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            return provider.chainId;\n        },\n        async isAuthorized() {\n            try {\n                const [accounts, provider] = await Promise.all([\n                    this.getAccounts(),\n                    this.getProvider(),\n                ]);\n                // If an account does not exist on the session, then the connector is unauthorized.\n                if (!accounts.length)\n                    return false;\n                // If the chains are stale on the session, then the connector is unauthorized.\n                const isChainsStale = await this.isChainsStale();\n                if (isChainsStale && provider.session) {\n                    await provider.disconnect().catch(() => { });\n                    return false;\n                }\n                return true;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ChainNotConfiguredError());\n            try {\n                await Promise.all([\n                    new Promise((resolve) => {\n                        const listener = ({ chainId: currentChainId, }) => {\n                            if (currentChainId === chainId) {\n                                config.emitter.off('change', listener);\n                                resolve();\n                            }\n                        };\n                        config.emitter.on('change', listener);\n                    }),\n                    provider.request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId) }],\n                    }),\n                ]);\n                const requestedChains = await this.getRequestedChainsIds();\n                this.setRequestedChainsIds([...requestedChains, chainId]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (/(user rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                try {\n                    let blockExplorerUrls;\n                    if (addEthereumChainParameter?.blockExplorerUrls)\n                        blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                    else\n                        blockExplorerUrls = chain.blockExplorers?.default.url\n                            ? [chain.blockExplorers?.default.url]\n                            : [];\n                    let rpcUrls;\n                    if (addEthereumChainParameter?.rpcUrls?.length)\n                        rpcUrls = addEthereumChainParameter.rpcUrls;\n                    else\n                        rpcUrls = [...chain.rpcUrls.default.http];\n                    const addEthereumChain = {\n                        blockExplorerUrls,\n                        chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId),\n                        chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                        iconUrls: addEthereumChainParameter?.iconUrls,\n                        nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,\n                        rpcUrls,\n                    };\n                    await provider.request({\n                        method: 'wallet_addEthereumChain',\n                        params: [addEthereumChain],\n                    });\n                    const requestedChains = await this.getRequestedChainsIds();\n                    this.setRequestedChainsIds([...requestedChains, chainId]);\n                    return chain;\n                }\n                catch (error) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const chainId = Number(connectInfo.chainId);\n            const accounts = await this.getAccounts();\n            config.emitter.emit('connect', { accounts, chainId });\n        },\n        async onDisconnect(_error) {\n            this.setRequestedChainsIds([]);\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (sessionDelete) {\n                provider.removeListener('session_delete', sessionDelete);\n                sessionDelete = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n        onSessionDelete() {\n            this.onDisconnect();\n        },\n        getNamespaceChainsIds() {\n            if (!provider_)\n                return [];\n            const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map((account) => Number.parseInt(account.split(':')[1] || ''));\n            return chainIds ?? [];\n        },\n        async getRequestedChainsIds() {\n            return ((await config.storage?.getItem(this.requestedChainsStorageKey)) ?? []);\n        },\n        /**\n         * Checks if the target chains match the chains that were\n         * initially requested by the connector for the WalletConnect session.\n         * If there is a mismatch, this means that the chains on the connector\n         * are considered stale, and need to be revalidated at a later point (via\n         * connection).\n         *\n         * There may be a scenario where a dapp adds a chain to the\n         * connector later on, however, this chain will not have been approved or rejected\n         * by the wallet. In this case, the chain is considered stale.\n         */\n        async isChainsStale() {\n            if (!isNewChainsStale)\n                return false;\n            const connectorChains = config.chains.map((x) => x.id);\n            const namespaceChains = this.getNamespaceChainsIds();\n            if (namespaceChains.length &&\n                !namespaceChains.some((id) => connectorChains.includes(id)))\n                return false;\n            const requestedChains = await this.getRequestedChainsIds();\n            return !connectorChains.every((id) => requestedChains.includes(id));\n        },\n        async setRequestedChainsIds(chains) {\n            await config.storage?.setItem(this.requestedChainsStorageKey, chains);\n        },\n        get requestedChainsStorageKey() {\n            return `${this.id}.requestedChains`;\n        },\n    }));\n}\n//# sourceMappingURL=walletConnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/connect.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connect: () => (/* binding */ connect)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n/** https://wagmi.sh/core/api/actions/connect */\nasync function connect(config, parameters) {\n    // \"Register\" connector if not already created\n    let connector;\n    if (typeof parameters.connector === 'function') {\n        connector = config._internal.connectors.setup(parameters.connector);\n    }\n    else\n        connector = parameters.connector;\n    // Check if connector is already connected\n    if (connector.uid === config.state.current)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAlreadyConnectedError();\n    try {\n        config.setState((x) => ({ ...x, status: 'connecting' }));\n        connector.emitter.emit('message', { type: 'connecting' });\n        const { connector: _, ...rest } = parameters;\n        const data = await connector.connect(rest);\n        const accounts = data.accounts;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        await config.storage?.setItem('recentConnectorId', connector.id);\n        config.setState((x) => ({\n            ...x,\n            connections: new Map(x.connections).set(connector.uid, {\n                accounts,\n                chainId: data.chainId,\n                connector: connector,\n            }),\n            current: connector.uid,\n            status: 'connected',\n        }));\n        return { accounts, chainId: data.chainId };\n    }\n    catch (error) {\n        config.setState((x) => ({\n            ...x,\n            // Keep existing connector connected in case of error\n            status: x.current ? 'connected' : 'disconnected',\n        }));\n        throw error;\n    }\n}\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/disconnect.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnect: () => (/* binding */ disconnect)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/disconnect */\nasync function disconnect(config, parameters = {}) {\n    let connector;\n    if (parameters.connector)\n        connector = parameters.connector;\n    else {\n        const { connections, current } = config.state;\n        const connection = connections.get(current);\n        connector = connection?.connector;\n    }\n    const connections = config.state.connections;\n    if (connector) {\n        await connector.disconnect();\n        connector.emitter.off('change', config._internal.events.change);\n        connector.emitter.off('disconnect', config._internal.events.disconnect);\n        connector.emitter.on('connect', config._internal.events.connect);\n        connections.delete(connector.uid);\n    }\n    config.setState((x) => {\n        // if no connections exist, move to disconnected state\n        if (connections.size === 0)\n            return {\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            };\n        // switch over to another connection\n        const nextConnection = connections.values().next().value;\n        return {\n            ...x,\n            connections: new Map(connections),\n            current: nextConnection.connector.uid,\n        };\n    });\n    // Set recent connector if exists\n    {\n        const current = config.state.current;\n        if (!current)\n            return;\n        const connector = config.state.connections.get(current)?.connector;\n        if (!connector)\n            return;\n        await config.storage?.setItem('recentConnectorId', connector.id);\n    }\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9kaXNjb25uZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPLGlEQUFpRDtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix1QkFBdUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcZGlzY29ubmVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL2Rpc2Nvbm5lY3QgKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkaXNjb25uZWN0KGNvbmZpZywgcGFyYW1ldGVycyA9IHt9KSB7XG4gICAgbGV0IGNvbm5lY3RvcjtcbiAgICBpZiAocGFyYW1ldGVycy5jb25uZWN0b3IpXG4gICAgICAgIGNvbm5lY3RvciA9IHBhcmFtZXRlcnMuY29ubmVjdG9yO1xuICAgIGVsc2Uge1xuICAgICAgICBjb25zdCB7IGNvbm5lY3Rpb25zLCBjdXJyZW50IH0gPSBjb25maWcuc3RhdGU7XG4gICAgICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBjb25uZWN0aW9ucy5nZXQoY3VycmVudCk7XG4gICAgICAgIGNvbm5lY3RvciA9IGNvbm5lY3Rpb24/LmNvbm5lY3RvcjtcbiAgICB9XG4gICAgY29uc3QgY29ubmVjdGlvbnMgPSBjb25maWcuc3RhdGUuY29ubmVjdGlvbnM7XG4gICAgaWYgKGNvbm5lY3Rvcikge1xuICAgICAgICBhd2FpdCBjb25uZWN0b3IuZGlzY29ubmVjdCgpO1xuICAgICAgICBjb25uZWN0b3IuZW1pdHRlci5vZmYoJ2NoYW5nZScsIGNvbmZpZy5faW50ZXJuYWwuZXZlbnRzLmNoYW5nZSk7XG4gICAgICAgIGNvbm5lY3Rvci5lbWl0dGVyLm9mZignZGlzY29ubmVjdCcsIGNvbmZpZy5faW50ZXJuYWwuZXZlbnRzLmRpc2Nvbm5lY3QpO1xuICAgICAgICBjb25uZWN0b3IuZW1pdHRlci5vbignY29ubmVjdCcsIGNvbmZpZy5faW50ZXJuYWwuZXZlbnRzLmNvbm5lY3QpO1xuICAgICAgICBjb25uZWN0aW9ucy5kZWxldGUoY29ubmVjdG9yLnVpZCk7XG4gICAgfVxuICAgIGNvbmZpZy5zZXRTdGF0ZSgoeCkgPT4ge1xuICAgICAgICAvLyBpZiBubyBjb25uZWN0aW9ucyBleGlzdCwgbW92ZSB0byBkaXNjb25uZWN0ZWQgc3RhdGVcbiAgICAgICAgaWYgKGNvbm5lY3Rpb25zLnNpemUgPT09IDApXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIC4uLngsXG4gICAgICAgICAgICAgICAgY29ubmVjdGlvbnM6IG5ldyBNYXAoKSxcbiAgICAgICAgICAgICAgICBjdXJyZW50OiBudWxsLFxuICAgICAgICAgICAgICAgIHN0YXR1czogJ2Rpc2Nvbm5lY3RlZCcsXG4gICAgICAgICAgICB9O1xuICAgICAgICAvLyBzd2l0Y2ggb3ZlciB0byBhbm90aGVyIGNvbm5lY3Rpb25cbiAgICAgICAgY29uc3QgbmV4dENvbm5lY3Rpb24gPSBjb25uZWN0aW9ucy52YWx1ZXMoKS5uZXh0KCkudmFsdWU7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi54LFxuICAgICAgICAgICAgY29ubmVjdGlvbnM6IG5ldyBNYXAoY29ubmVjdGlvbnMpLFxuICAgICAgICAgICAgY3VycmVudDogbmV4dENvbm5lY3Rpb24uY29ubmVjdG9yLnVpZCxcbiAgICAgICAgfTtcbiAgICB9KTtcbiAgICAvLyBTZXQgcmVjZW50IGNvbm5lY3RvciBpZiBleGlzdHNcbiAgICB7XG4gICAgICAgIGNvbnN0IGN1cnJlbnQgPSBjb25maWcuc3RhdGUuY3VycmVudDtcbiAgICAgICAgaWYgKCFjdXJyZW50KVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBjb25uZWN0b3IgPSBjb25maWcuc3RhdGUuY29ubmVjdGlvbnMuZ2V0KGN1cnJlbnQpPy5jb25uZWN0b3I7XG4gICAgICAgIGlmICghY29ubmVjdG9yKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBhd2FpdCBjb25maWcuc3RvcmFnZT8uc2V0SXRlbSgncmVjZW50Q29ubmVjdG9ySWQnLCBjb25uZWN0b3IuaWQpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRpc2Nvbm5lY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/estimateGas.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/estimateGas.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   estimateGas: () => (/* binding */ estimateGas)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/estimateGas.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/estimateGas */\nasync function estimateGas(config, parameters) {\n    const { chainId, connector, ...rest } = parameters;\n    let account;\n    if (parameters.account)\n        account = parameters.account;\n    else {\n        const connectorClient = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: parameters.account,\n            chainId,\n            connector,\n        });\n        account = connectorClient.account;\n    }\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.estimateGas, 'estimateGas');\n    return action({ ...rest, account });\n}\n//# sourceMappingURL=estimateGas.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9lc3RpbWF0ZUdhcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdFO0FBQ2Q7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSw4QkFBOEI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsMEVBQWtCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0MsbUJBQW1CLDhEQUFTLFNBQVMscURBQWdCO0FBQ3JELG9CQUFvQixrQkFBa0I7QUFDdEM7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcZXN0aW1hdGVHYXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZXN0aW1hdGVHYXMgYXMgdmllbV9lc3RpbWF0ZUdhcywgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmltcG9ydCB7IGdldENvbm5lY3RvckNsaWVudCwgfSBmcm9tICcuL2dldENvbm5lY3RvckNsaWVudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL2VzdGltYXRlR2FzICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXN0aW1hdGVHYXMoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBjaGFpbklkLCBjb25uZWN0b3IsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgbGV0IGFjY291bnQ7XG4gICAgaWYgKHBhcmFtZXRlcnMuYWNjb3VudClcbiAgICAgICAgYWNjb3VudCA9IHBhcmFtZXRlcnMuYWNjb3VudDtcbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgY29ubmVjdG9yQ2xpZW50ID0gYXdhaXQgZ2V0Q29ubmVjdG9yQ2xpZW50KGNvbmZpZywge1xuICAgICAgICAgICAgYWNjb3VudDogcGFyYW1ldGVycy5hY2NvdW50LFxuICAgICAgICAgICAgY2hhaW5JZCxcbiAgICAgICAgICAgIGNvbm5lY3RvcixcbiAgICAgICAgfSk7XG4gICAgICAgIGFjY291bnQgPSBjb25uZWN0b3JDbGllbnQuYWNjb3VudDtcbiAgICB9XG4gICAgY29uc3QgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9lc3RpbWF0ZUdhcywgJ2VzdGltYXRlR2FzJyk7XG4gICAgcmV0dXJuIGFjdGlvbih7IC4uLnJlc3QsIGFjY291bnQgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lc3RpbWF0ZUdhcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/estimateGas.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getAccount.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccount: () => (/* binding */ getAccount)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getAccount */\nfunction getAccount(config) {\n    const uid = config.state.current;\n    const connection = config.state.connections.get(uid);\n    const addresses = connection?.accounts;\n    const address = addresses?.[0];\n    const chain = config.chains.find((chain) => chain.id === connection?.chainId);\n    const status = config.state.status;\n    switch (status) {\n        case 'connected':\n            return {\n                address: address,\n                addresses: addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: true,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'reconnecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: !!address,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: true,\n                status,\n            };\n        case 'connecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: false,\n                isConnecting: true,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'disconnected':\n            return {\n                address: undefined,\n                addresses: undefined,\n                chain: undefined,\n                chainId: undefined,\n                connector: undefined,\n                isConnected: false,\n                isConnecting: false,\n                isDisconnected: true,\n                isReconnecting: false,\n                status,\n            };\n    }\n}\n//# sourceMappingURL=getAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getBalance.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBalance: () => (/* binding */ getBalance)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/data/trim.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/unit/formatUnits.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getBalance.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/getUnit.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js\");\n/* harmony import */ var _readContracts_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./readContracts.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js\");\n\n\n\n\n\n/** https://wagmi.sh/core/api/actions/getBalance */\nasync function getBalance(config, parameters) {\n    const { address, blockNumber, blockTag, chainId, token: tokenAddress, unit = 'ether', } = parameters;\n    if (tokenAddress) {\n        try {\n            return await getTokenBalance(config, {\n                balanceAddress: address,\n                chainId,\n                symbolType: 'string',\n                tokenAddress,\n            });\n        }\n        catch (error) {\n            // In the chance that there is an error upon decoding the contract result,\n            // it could be likely that the contract data is represented as bytes32 instead\n            // of a string.\n            if (error.name ===\n                'ContractFunctionExecutionError') {\n                const balance = await getTokenBalance(config, {\n                    balanceAddress: address,\n                    chainId,\n                    symbolType: 'bytes32',\n                    tokenAddress,\n                });\n                const symbol = (0,viem__WEBPACK_IMPORTED_MODULE_0__.hexToString)((0,viem__WEBPACK_IMPORTED_MODULE_1__.trim)(balance.symbol, { dir: 'right' }));\n                return { ...balance, symbol };\n            }\n            throw error;\n        }\n    }\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.getBalance, 'getBalance');\n    const value = await action(blockNumber ? { address, blockNumber } : { address, blockTag });\n    const chain = config.chains.find((x) => x.id === chainId) ?? client.chain;\n    return {\n        decimals: chain.nativeCurrency.decimals,\n        formatted: (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value, (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit)),\n        symbol: chain.nativeCurrency.symbol,\n        value,\n    };\n}\nasync function getTokenBalance(config, parameters) {\n    const { balanceAddress, chainId, symbolType, tokenAddress, unit } = parameters;\n    const contract = {\n        abi: [\n            {\n                type: 'function',\n                name: 'balanceOf',\n                stateMutability: 'view',\n                inputs: [{ type: 'address' }],\n                outputs: [{ type: 'uint256' }],\n            },\n            {\n                type: 'function',\n                name: 'decimals',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: 'uint8' }],\n            },\n            {\n                type: 'function',\n                name: 'symbol',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: symbolType }],\n            },\n        ],\n        address: tokenAddress,\n    };\n    const [value, decimals, symbol] = await (0,_readContracts_js__WEBPACK_IMPORTED_MODULE_6__.readContracts)(config, {\n        allowFailure: false,\n        contracts: [\n            {\n                ...contract,\n                functionName: 'balanceOf',\n                args: [balanceAddress],\n                chainId,\n            },\n            { ...contract, functionName: 'decimals', chainId },\n            { ...contract, functionName: 'symbol', chainId },\n        ],\n    });\n    const formatted = (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value ?? '0', (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit ?? decimals));\n    return { decimals, formatted, symbol, value };\n}\n//# sourceMappingURL=getBalance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnections.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnections: () => (/* binding */ getConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousConnections = [];\n/** https://wagmi.sh/core/api/actions/getConnections */\nfunction getConnections(config) {\n    const connections = [...config.state.connections.values()];\n    if (config.state.status === 'reconnecting')\n        return previousConnections;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousConnections, connections))\n        return previousConnections;\n    previousConnections = connections;\n    return connections;\n}\n//# sourceMappingURL=getConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDb25uZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFTO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXGdldENvbm5lY3Rpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZXBFcXVhbCB9IGZyb20gJy4uL3V0aWxzL2RlZXBFcXVhbC5qcyc7XG5sZXQgcHJldmlvdXNDb25uZWN0aW9ucyA9IFtdO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9nZXRDb25uZWN0aW9ucyAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldENvbm5lY3Rpb25zKGNvbmZpZykge1xuICAgIGNvbnN0IGNvbm5lY3Rpb25zID0gWy4uLmNvbmZpZy5zdGF0ZS5jb25uZWN0aW9ucy52YWx1ZXMoKV07XG4gICAgaWYgKGNvbmZpZy5zdGF0ZS5zdGF0dXMgPT09ICdyZWNvbm5lY3RpbmcnKVxuICAgICAgICByZXR1cm4gcHJldmlvdXNDb25uZWN0aW9ucztcbiAgICBpZiAoZGVlcEVxdWFsKHByZXZpb3VzQ29ubmVjdGlvbnMsIGNvbm5lY3Rpb25zKSlcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzQ29ubmVjdGlvbnM7XG4gICAgcHJldmlvdXNDb25uZWN0aW9ucyA9IGNvbm5lY3Rpb25zO1xuICAgIHJldHVybiBjb25uZWN0aW9ucztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldENvbm5lY3Rpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectorClient: () => (/* binding */ getConnectorClient)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nasync function getConnectorClient(config, parameters = {}) {\n    // Get connection\n    let connection;\n    if (parameters.connector) {\n        const { connector } = parameters;\n        if (config.state.status === 'reconnecting' &&\n            !connector.getAccounts &&\n            !connector.getChainId)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorUnavailableReconnectingError({ connector });\n        const [accounts, chainId] = await Promise.all([\n            connector.getAccounts().catch((e) => {\n                if (parameters.account === null)\n                    return [];\n                throw e;\n            }),\n            connector.getChainId(),\n        ]);\n        connection = {\n            accounts: accounts,\n            chainId,\n            connector,\n        };\n    }\n    else\n        connection = config.state.connections.get(config.state.current);\n    if (!connection)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorNotConnectedError();\n    const chainId = parameters.chainId ?? connection.chainId;\n    // Check connector using same chainId as connection\n    const connectorChainId = await connection.connector.getChainId();\n    if (connectorChainId !== connection.chainId)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorChainMismatchError({\n            connectionChainId: connection.chainId,\n            connectorChainId,\n        });\n    const connector = connection.connector;\n    if (connector.getClient)\n        return connector.getClient({ chainId });\n    // Default using `custom` transport\n    const account = (0,viem_utils__WEBPACK_IMPORTED_MODULE_1__.parseAccount)(parameters.account ?? connection.accounts[0]);\n    if (account)\n        account.address = (0,viem_utils__WEBPACK_IMPORTED_MODULE_2__.getAddress)(account.address); // TODO: Checksum address as part of `parseAccount`?\n    // If account was provided, check that it exists on the connector\n    if (parameters.account &&\n        !connection.accounts.some((x) => x.toLowerCase() === account.address.toLowerCase()))\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAccountNotFoundError({\n            address: account.address,\n            connector,\n        });\n    const chain = config.chains.find((chain) => chain.id === chainId);\n    const provider = (await connection.connector.getProvider({ chainId }));\n    return (0,viem__WEBPACK_IMPORTED_MODULE_3__.createClient)({\n        account,\n        chain,\n        name: 'Connector Client',\n        transport: (opts) => (0,viem__WEBPACK_IMPORTED_MODULE_4__.custom)(provider)({ ...opts, retryCount: 0 }),\n    });\n}\n//# sourceMappingURL=getConnectorClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/multicall.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   multicall: () => (/* binding */ multicall)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/multicall.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\nasync function multicall(config, parameters) {\n    const { allowFailure = true, chainId, contracts, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.multicall, 'multicall');\n    return action({\n        allowFailure,\n        contracts,\n        ...rest,\n    });\n}\n//# sourceMappingURL=multicall.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9tdWx0aWNhbGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ1Q7QUFDM0M7QUFDUCxZQUFZLG1EQUFtRDtBQUMvRCxzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxtREFBYztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxhY3Rpb25zXFxtdWx0aWNhbGwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbXVsdGljYWxsIGFzIHZpZW1fbXVsdGljYWxsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbXVsdGljYWxsKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgYWxsb3dGYWlsdXJlID0gdHJ1ZSwgY2hhaW5JZCwgY29udHJhY3RzLCAuLi5yZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fbXVsdGljYWxsLCAnbXVsdGljYWxsJyk7XG4gICAgcmV0dXJuIGFjdGlvbih7XG4gICAgICAgIGFsbG93RmFpbHVyZSxcbiAgICAgICAgY29udHJhY3RzLFxuICAgICAgICAuLi5yZXN0LFxuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bXVsdGljYWxsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/prepareTransactionRequest.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/prepareTransactionRequest.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareTransactionRequest: () => (/* binding */ prepareTransactionRequest)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/prepareTransactionRequest.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/prepareTransactionRequest */\nasync function prepareTransactionRequest(config, parameters) {\n    const { account: account_, chainId, ...rest } = parameters;\n    const account = account_ ?? (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config).address;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.prepareTransactionRequest, 'prepareTransactionRequest');\n    return action({\n        ...rest,\n        ...(account ? { account } : {}),\n    });\n}\n//# sourceMappingURL=prepareTransactionRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9wcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMkY7QUFDekM7QUFDTDtBQUM3QztBQUNPO0FBQ1AsWUFBWSxzQ0FBc0M7QUFDbEQsZ0NBQWdDLDBEQUFVO0FBQzFDLHNDQUFzQyxTQUFTO0FBQy9DLG1CQUFtQiw4REFBUyxTQUFTLG1FQUE4QjtBQUNuRTtBQUNBO0FBQ0Esd0JBQXdCLFVBQVUsSUFBSTtBQUN0QyxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xccHJlcGFyZVRyYW5zYWN0aW9uUmVxdWVzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBwcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0IGFzIHZpZW1fcHJlcGFyZVRyYW5zYWN0aW9uUmVxdWVzdCB9IGZyb20gJ3ZpZW0vYWN0aW9ucyc7XG5pbXBvcnQgeyBnZXRBY3Rpb24gfSBmcm9tICcuLi91dGlscy9nZXRBY3Rpb24uanMnO1xuaW1wb3J0IHsgZ2V0QWNjb3VudCB9IGZyb20gJy4vZ2V0QWNjb3VudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3ByZXBhcmVUcmFuc2FjdGlvblJlcXVlc3QgKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBwcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0KGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgYWNjb3VudDogYWNjb3VudF8sIGNoYWluSWQsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgYWNjb3VudCA9IGFjY291bnRfID8/IGdldEFjY291bnQoY29uZmlnKS5hZGRyZXNzO1xuICAgIGNvbnN0IGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fcHJlcGFyZVRyYW5zYWN0aW9uUmVxdWVzdCwgJ3ByZXBhcmVUcmFuc2FjdGlvblJlcXVlc3QnKTtcbiAgICByZXR1cm4gYWN0aW9uKHtcbiAgICAgICAgLi4ucmVzdCxcbiAgICAgICAgLi4uKGFjY291bnQgPyB7IGFjY291bnQgfSA6IHt9KSxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByZXBhcmVUcmFuc2FjdGlvblJlcXVlc3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/prepareTransactionRequest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContract.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContract: () => (/* binding */ readContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/readContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/readContract */\nfunction readContract(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.readContract, 'readContract');\n    return action(rest);\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9yZWFkQ29udHJhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtFO0FBQ2hCO0FBQ2xEO0FBQ087QUFDUCxZQUFZLG1CQUFtQjtBQUMvQixzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxzREFBaUI7QUFDdEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxhY3Rpb25zXFxyZWFkQ29udHJhY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVhZENvbnRyYWN0IGFzIHZpZW1fcmVhZENvbnRyYWN0LCB9IGZyb20gJ3ZpZW0vYWN0aW9ucyc7XG5pbXBvcnQgeyBnZXRBY3Rpb24gfSBmcm9tICcuLi91dGlscy9nZXRBY3Rpb24uanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9yZWFkQ29udHJhY3QgKi9cbmV4cG9ydCBmdW5jdGlvbiByZWFkQ29udHJhY3QoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBjaGFpbklkLCAuLi5yZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fcmVhZENvbnRyYWN0LCAncmVhZENvbnRyYWN0Jyk7XG4gICAgcmV0dXJuIGFjdGlvbihyZXN0KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlYWRDb250cmFjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContracts.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContracts: () => (/* binding */ readContracts)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/contract.js\");\n/* harmony import */ var _multicall_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./multicall.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js\");\n/* harmony import */ var _readContract_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./readContract.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\");\n\n\n\nasync function readContracts(config, parameters) {\n    const { allowFailure = true, blockNumber, blockTag, ...rest } = parameters;\n    const contracts = parameters.contracts;\n    try {\n        const contractsByChainId = {};\n        for (const [index, contract] of contracts.entries()) {\n            const chainId = contract.chainId ?? config.state.chainId;\n            if (!contractsByChainId[chainId])\n                contractsByChainId[chainId] = [];\n            contractsByChainId[chainId]?.push({ contract, index });\n        }\n        const promises = () => Object.entries(contractsByChainId).map(([chainId, contracts]) => (0,_multicall_js__WEBPACK_IMPORTED_MODULE_0__.multicall)(config, {\n            ...rest,\n            allowFailure,\n            blockNumber,\n            blockTag,\n            chainId: Number.parseInt(chainId),\n            contracts: contracts.map(({ contract }) => contract),\n        }));\n        const multicallResults = (await Promise.all(promises())).flat();\n        // Reorder the contract results back to the order they were\n        // provided in.\n        const resultIndexes = Object.values(contractsByChainId).flatMap((contracts) => contracts.map(({ index }) => index));\n        return multicallResults.reduce((results, result, index) => {\n            if (results)\n                results[resultIndexes[index]] = result;\n            return results;\n        }, []);\n    }\n    catch (error) {\n        if (error instanceof viem__WEBPACK_IMPORTED_MODULE_1__.ContractFunctionExecutionError)\n            throw error;\n        const promises = () => contracts.map((contract) => (0,_readContract_js__WEBPACK_IMPORTED_MODULE_2__.readContract)(config, { ...contract, blockNumber, blockTag }));\n        if (allowFailure)\n            return (await Promise.allSettled(promises())).map((result) => {\n                if (result.status === 'fulfilled')\n                    return { result: result.value, status: 'success' };\n                return { error: result.reason, result: undefined, status: 'failure' };\n            });\n        return (await Promise.all(promises()));\n    }\n}\n//# sourceMappingURL=readContracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/reconnect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reconnect: () => (/* binding */ reconnect)\n/* harmony export */ });\nlet isReconnecting = false;\n/** https://wagmi.sh/core/api/actions/reconnect */\nasync function reconnect(config, parameters = {}) {\n    // If already reconnecting, do nothing\n    if (isReconnecting)\n        return [];\n    isReconnecting = true;\n    config.setState((x) => ({\n        ...x,\n        status: x.current ? 'reconnecting' : 'connecting',\n    }));\n    const connectors = [];\n    if (parameters.connectors?.length) {\n        for (const connector_ of parameters.connectors) {\n            let connector;\n            // \"Register\" connector if not already created\n            if (typeof connector_ === 'function')\n                connector = config._internal.connectors.setup(connector_);\n            else\n                connector = connector_;\n            connectors.push(connector);\n        }\n    }\n    else\n        connectors.push(...config.connectors);\n    // Try recently-used connectors first\n    let recentConnectorId;\n    try {\n        recentConnectorId = await config.storage?.getItem('recentConnectorId');\n    }\n    catch { }\n    const scores = {};\n    for (const [, connection] of config.state.connections) {\n        scores[connection.connector.id] = 1;\n    }\n    if (recentConnectorId)\n        scores[recentConnectorId] = 0;\n    const sorted = Object.keys(scores).length > 0\n        ? // .toSorted()\n            [...connectors].sort((a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10))\n        : connectors;\n    // Iterate through each connector and try to connect\n    let connected = false;\n    const connections = [];\n    const providers = [];\n    for (const connector of sorted) {\n        const provider = await connector.getProvider().catch(() => undefined);\n        if (!provider)\n            continue;\n        // If we already have an instance of this connector's provider,\n        // then we have already checked it (ie. injected connectors can\n        // share the same `window.ethereum` instance, so we don't want to\n        // connect to it again).\n        if (providers.some((x) => x === provider))\n            continue;\n        const isAuthorized = await connector.isAuthorized();\n        if (!isAuthorized)\n            continue;\n        const data = await connector\n            .connect({ isReconnecting: true })\n            .catch(() => null);\n        if (!data)\n            continue;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        config.setState((x) => {\n            const connections = new Map(connected ? x.connections : new Map()).set(connector.uid, { accounts: data.accounts, chainId: data.chainId, connector });\n            return {\n                ...x,\n                current: connected ? x.current : connector.uid,\n                connections,\n            };\n        });\n        connections.push({\n            accounts: data.accounts,\n            chainId: data.chainId,\n            connector,\n        });\n        providers.push(provider);\n        connected = true;\n    }\n    // Prevent overwriting connected status from race condition\n    if (config.state.status === 'reconnecting' ||\n        config.state.status === 'connecting') {\n        // If connecting didn't succeed, set to disconnected\n        if (!connected)\n            config.setState((x) => ({\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            }));\n        else\n            config.setState((x) => ({ ...x, status: 'connected' }));\n    }\n    isReconnecting = false;\n    return connections;\n}\n//# sourceMappingURL=reconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/sendTransaction.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/sendTransaction.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendTransaction: () => (/* binding */ sendTransaction)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/sendTransaction.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/sendTransaction */\nasync function sendTransaction(config, parameters) {\n    const { account, chainId, connector, ...rest } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.sendTransaction, 'sendTransaction');\n    const hash = await action({\n        ...rest,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n        gas: rest.gas ?? undefined,\n    });\n    return hash;\n}\n//# sourceMappingURL=sendTransaction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/sendTransaction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/signMessage.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/signMessage.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signMessage: () => (/* binding */ signMessage)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/signMessage.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/signMessage */\nasync function signMessage(config, parameters) {\n    const { account, connector, ...rest } = parameters;\n    let client;\n    if (typeof account === 'object' && account.type === 'local')\n        client = config.getClient();\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, { account, connector });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.signMessage, 'signMessage');\n    return action({\n        ...rest,\n        ...(account ? { account } : {}),\n    });\n}\n//# sourceMappingURL=signMessage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zaWduTWVzc2FnZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdFO0FBQ2Q7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSw4QkFBOEI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsMEVBQWtCLFdBQVcsb0JBQW9CO0FBQ3hFLG1CQUFtQiw4REFBUyxTQUFTLHFEQUFnQjtBQUNyRDtBQUNBO0FBQ0Esd0JBQXdCLFVBQVUsSUFBSTtBQUN0QyxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcc2lnbk1lc3NhZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2lnbk1lc3NhZ2UgYXMgdmllbV9zaWduTWVzc2FnZSwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmltcG9ydCB7IGdldENvbm5lY3RvckNsaWVudCwgfSBmcm9tICcuL2dldENvbm5lY3RvckNsaWVudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3NpZ25NZXNzYWdlICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2lnbk1lc3NhZ2UoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhY2NvdW50LCBjb25uZWN0b3IsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgbGV0IGNsaWVudDtcbiAgICBpZiAodHlwZW9mIGFjY291bnQgPT09ICdvYmplY3QnICYmIGFjY291bnQudHlwZSA9PT0gJ2xvY2FsJylcbiAgICAgICAgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCgpO1xuICAgIGVsc2VcbiAgICAgICAgY2xpZW50ID0gYXdhaXQgZ2V0Q29ubmVjdG9yQ2xpZW50KGNvbmZpZywgeyBhY2NvdW50LCBjb25uZWN0b3IgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9zaWduTWVzc2FnZSwgJ3NpZ25NZXNzYWdlJyk7XG4gICAgcmV0dXJuIGFjdGlvbih7XG4gICAgICAgIC4uLnJlc3QsXG4gICAgICAgIC4uLihhY2NvdW50ID8geyBhY2NvdW50IH0gOiB7fSksXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zaWduTWVzc2FnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/signMessage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/switchChain.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/switchChain.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   switchChain: () => (/* binding */ switchChain)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n\n\n/** https://wagmi.sh/core/api/actions/switchChain */\nasync function switchChain(config, parameters) {\n    const { addEthereumChainParameter, chainId } = parameters;\n    const connection = config.state.connections.get(parameters.connector?.uid ?? config.state.current);\n    if (connection) {\n        const connector = connection.connector;\n        if (!connector.switchChain)\n            throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainNotSupportedError({ connector });\n        const chain = await connector.switchChain({\n            addEthereumChainParameter,\n            chainId,\n        });\n        return chain;\n    }\n    const chain = config.chains.find((x) => x.id === chainId);\n    if (!chain)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_1__.ChainNotConfiguredError();\n    config.setState((x) => ({ ...x, chainId }));\n    return chain;\n}\n//# sourceMappingURL=switchChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zd2l0Y2hDaGFpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0Q7QUFDUTtBQUN2RTtBQUNPO0FBQ1AsWUFBWSxxQ0FBcUM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOEVBQTRCLEdBQUcsV0FBVztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isc0VBQXVCO0FBQ3pDLDhCQUE4QixlQUFlO0FBQzdDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcc3dpdGNoQ2hhaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IsIH0gZnJvbSAnLi4vZXJyb3JzL2NvbmZpZy5qcyc7XG5pbXBvcnQgeyBTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yLCB9IGZyb20gJy4uL2Vycm9ycy9jb25uZWN0b3IuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9zd2l0Y2hDaGFpbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN3aXRjaENoYWluKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlciwgY2hhaW5JZCB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBjb25uZWN0aW9uID0gY29uZmlnLnN0YXRlLmNvbm5lY3Rpb25zLmdldChwYXJhbWV0ZXJzLmNvbm5lY3Rvcj8udWlkID8/IGNvbmZpZy5zdGF0ZS5jdXJyZW50KTtcbiAgICBpZiAoY29ubmVjdGlvbikge1xuICAgICAgICBjb25zdCBjb25uZWN0b3IgPSBjb25uZWN0aW9uLmNvbm5lY3RvcjtcbiAgICAgICAgaWYgKCFjb25uZWN0b3Iuc3dpdGNoQ2hhaW4pXG4gICAgICAgICAgICB0aHJvdyBuZXcgU3dpdGNoQ2hhaW5Ob3RTdXBwb3J0ZWRFcnJvcih7IGNvbm5lY3RvciB9KTtcbiAgICAgICAgY29uc3QgY2hhaW4gPSBhd2FpdCBjb25uZWN0b3Iuc3dpdGNoQ2hhaW4oe1xuICAgICAgICAgICAgYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcixcbiAgICAgICAgICAgIGNoYWluSWQsXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gY2hhaW47XG4gICAgfVxuICAgIGNvbnN0IGNoYWluID0gY29uZmlnLmNoYWlucy5maW5kKCh4KSA9PiB4LmlkID09PSBjaGFpbklkKTtcbiAgICBpZiAoIWNoYWluKVxuICAgICAgICB0aHJvdyBuZXcgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IoKTtcbiAgICBjb25maWcuc2V0U3RhdGUoKHgpID0+ICh7IC4uLngsIGNoYWluSWQgfSkpO1xuICAgIHJldHVybiBjaGFpbjtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN3aXRjaENoYWluLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/switchChain.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceipt: () => (/* binding */ waitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/waitForTransactionReceipt.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getTransaction.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n\nasync function waitForTransactionReceipt(config, parameters) {\n    const { chainId, timeout = 0, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.waitForTransactionReceipt, 'waitForTransactionReceipt');\n    const receipt = await action({ ...rest, timeout });\n    if (receipt.status === 'reverted') {\n        const action_getTransaction = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.getTransaction, 'getTransaction');\n        const txn = await action_getTransaction({ hash: receipt.transactionHash });\n        const action_call = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.call, 'call');\n        const code = await action_call({\n            ...txn,\n            data: txn.input,\n            gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n            maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n            maxPriorityFeePerGas: txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n        });\n        const reason = code?.data\n            ? (0,viem__WEBPACK_IMPORTED_MODULE_4__.hexToString)(`0x${code.data.substring(138)}`)\n            : 'unknown reason';\n        throw new Error(reason);\n    }\n    return {\n        ...receipt,\n        chainId: client.chain.id,\n    };\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchAccount: () => (/* binding */ watchAccount)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nfunction watchAccount(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config), onChange, {\n        equalityFn(a, b) {\n            const { connector: aConnector, ...aRest } = a;\n            const { connector: bConnector, ...bRest } = b;\n            return ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual)(aRest, bRest) &&\n                // check connector separately\n                aConnector?.id === bConnector?.id &&\n                aConnector?.uid === bConnector?.uid);\n        },\n    });\n}\n//# sourceMappingURL=watchAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaEFjY291bnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ0w7QUFDN0M7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QixrQ0FBa0MsMERBQVU7QUFDNUM7QUFDQSxvQkFBb0Isa0NBQWtDO0FBQ3RELG9CQUFvQixrQ0FBa0M7QUFDdEQsb0JBQW9CLDhEQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcd2F0Y2hBY2NvdW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZXBFcXVhbCB9IGZyb20gJy4uL3V0aWxzL2RlZXBFcXVhbC5qcyc7XG5pbXBvcnQgeyBnZXRBY2NvdW50IH0gZnJvbSAnLi9nZXRBY2NvdW50LmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd2F0Y2hBY2NvdW50ICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hBY2NvdW50KGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgb25DaGFuZ2UgfSA9IHBhcmFtZXRlcnM7XG4gICAgcmV0dXJuIGNvbmZpZy5zdWJzY3JpYmUoKCkgPT4gZ2V0QWNjb3VudChjb25maWcpLCBvbkNoYW5nZSwge1xuICAgICAgICBlcXVhbGl0eUZuKGEsIGIpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgY29ubmVjdG9yOiBhQ29ubmVjdG9yLCAuLi5hUmVzdCB9ID0gYTtcbiAgICAgICAgICAgIGNvbnN0IHsgY29ubmVjdG9yOiBiQ29ubmVjdG9yLCAuLi5iUmVzdCB9ID0gYjtcbiAgICAgICAgICAgIHJldHVybiAoZGVlcEVxdWFsKGFSZXN0LCBiUmVzdCkgJiZcbiAgICAgICAgICAgICAgICAvLyBjaGVjayBjb25uZWN0b3Igc2VwYXJhdGVseVxuICAgICAgICAgICAgICAgIGFDb25uZWN0b3I/LmlkID09PSBiQ29ubmVjdG9yPy5pZCAmJlxuICAgICAgICAgICAgICAgIGFDb25uZWN0b3I/LnVpZCA9PT0gYkNvbm5lY3Rvcj8udWlkKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhdGNoQWNjb3VudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnectors: () => (/* binding */ watchConnectors)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchConnectors */\nfunction watchConnectors(config, parameters) {\n    const { onChange } = parameters;\n    return config._internal.connectors.subscribe((connectors, prevConnectors) => {\n        onChange(Object.values(connectors), prevConnectors);\n    });\n}\n//# sourceMappingURL=watchConnectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENvbm5lY3RvcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxhY3Rpb25zXFx3YXRjaENvbm5lY3RvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy93YXRjaENvbm5lY3RvcnMgKi9cbmV4cG9ydCBmdW5jdGlvbiB3YXRjaENvbm5lY3RvcnMoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBvbkNoYW5nZSB9ID0gcGFyYW1ldGVycztcbiAgICByZXR1cm4gY29uZmlnLl9pbnRlcm5hbC5jb25uZWN0b3JzLnN1YnNjcmliZSgoY29ubmVjdG9ycywgcHJldkNvbm5lY3RvcnMpID0+IHtcbiAgICAgICAgb25DaGFuZ2UoT2JqZWN0LnZhbHVlcyhjb25uZWN0b3JzKSwgcHJldkNvbm5lY3RvcnMpO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2F0Y2hDb25uZWN0b3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchPendingTransactions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchPendingTransactions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchPendingTransactions: () => (/* binding */ watchPendingTransactions)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/watchPendingTransactions.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchPendingTransactions */\nfunction watchPendingTransactions(config, parameters) {\n    const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } = parameters;\n    let unwatch;\n    const listener = (chainId) => {\n        if (unwatch)\n            unwatch();\n        const client = config.getClient({ chainId });\n        const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.watchPendingTransactions, 'watchPendingTransactions');\n        unwatch = action(rest);\n        return unwatch;\n    };\n    // set up listener for transaction changes\n    const unlisten = listener(parameters.chainId);\n    // set up subscriber for connected chain changes\n    let unsubscribe;\n    if (syncConnectedChain && !parameters.chainId)\n        unsubscribe = config.subscribe(({ chainId }) => chainId, async (chainId) => listener(chainId));\n    return () => {\n        unlisten?.();\n        unsubscribe?.();\n    };\n}\n//# sourceMappingURL=watchPendingTransactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchPendingTransactions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/writeContract.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContract: () => (/* binding */ writeContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/writeContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nasync function writeContract(config, parameters) {\n    const { account, chainId, connector, ...request } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.writeContract, 'writeContract');\n    const hash = await action({\n        ...request,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n    });\n    return hash;\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93cml0ZUNvbnRyYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0U7QUFDbEI7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSwwQ0FBMEM7QUFDdEQ7QUFDQTtBQUNBLG9DQUFvQyxTQUFTO0FBQzdDO0FBQ0EsdUJBQXVCLDBFQUFrQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUJBQW1CLDhEQUFTLFNBQVMsdURBQWtCO0FBQ3ZEO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVSxJQUFJO0FBQ3RDLDJCQUEyQixjQUFjO0FBQ3pDLEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXHdyaXRlQ29udHJhY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd3JpdGVDb250cmFjdCBhcyB2aWVtX3dyaXRlQ29udHJhY3QsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG5pbXBvcnQgeyBnZXRDb25uZWN0b3JDbGllbnQsIH0gZnJvbSAnLi9nZXRDb25uZWN0b3JDbGllbnQuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy93cml0ZUNvbnRyYWN0ICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gd3JpdGVDb250cmFjdChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGFjY291bnQsIGNoYWluSWQsIGNvbm5lY3RvciwgLi4ucmVxdWVzdCB9ID0gcGFyYW1ldGVycztcbiAgICBsZXQgY2xpZW50O1xuICAgIGlmICh0eXBlb2YgYWNjb3VudCA9PT0gJ29iamVjdCcgJiYgYWNjb3VudD8udHlwZSA9PT0gJ2xvY2FsJylcbiAgICAgICAgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgZWxzZVxuICAgICAgICBjbGllbnQgPSBhd2FpdCBnZXRDb25uZWN0b3JDbGllbnQoY29uZmlnLCB7XG4gICAgICAgICAgICBhY2NvdW50OiBhY2NvdW50ID8/IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIGNoYWluSWQsXG4gICAgICAgICAgICBjb25uZWN0b3IsXG4gICAgICAgIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fd3JpdGVDb250cmFjdCwgJ3dyaXRlQ29udHJhY3QnKTtcbiAgICBjb25zdCBoYXNoID0gYXdhaXQgYWN0aW9uKHtcbiAgICAgICAgLi4ucmVxdWVzdCxcbiAgICAgICAgLi4uKGFjY291bnQgPyB7IGFjY291bnQgfSA6IHt9KSxcbiAgICAgICAgY2hhaW46IGNoYWluSWQgPyB7IGlkOiBjaGFpbklkIH0gOiBudWxsLFxuICAgIH0pO1xuICAgIHJldHVybiBoYXNoO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d3JpdGVDb250cmFjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConnector: () => (/* binding */ createConnector)\n/* harmony export */ });\nfunction createConnector(createConnectorFn) {\n    return createConnectorFn;\n}\n//# sourceMappingURL=createConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vY29ubmVjdG9ycy9jcmVhdGVDb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxjb25uZWN0b3JzXFxjcmVhdGVDb25uZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNvbm5lY3RvcihjcmVhdGVDb25uZWN0b3JGbikge1xuICAgIHJldHVybiBjcmVhdGVDb25uZWN0b3JGbjtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyZWF0ZUNvbm5lY3Rvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/injected.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injected: () => (/* binding */ injected)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\ninjected.type = 'injected';\nfunction injected(parameters = {}) {\n    const { shimDisconnect = true, unstable_shimAsyncInject } = parameters;\n    function getTarget() {\n        const target = parameters.target;\n        if (typeof target === 'function') {\n            const result = target();\n            if (result)\n                return result;\n        }\n        if (typeof target === 'object')\n            return target;\n        if (typeof target === 'string')\n            return {\n                ...(targetMap[target] ?? {\n                    id: target,\n                    name: `${target[0].toUpperCase()}${target.slice(1)}`,\n                    provider: `is${target[0].toUpperCase()}${target.slice(1)}`,\n                }),\n            };\n        return {\n            id: 'injected',\n            name: 'Injected',\n            provider(window) {\n                return window?.ethereum;\n            },\n        };\n    }\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let disconnect;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        get icon() {\n            return getTarget().icon;\n        },\n        get id() {\n            return getTarget().id;\n        },\n        get name() {\n            return getTarget().name;\n        },\n        /** @deprecated */\n        get supportsSimulation() {\n            return true;\n        },\n        type: injected.type,\n        async setup() {\n            const provider = await this.getProvider();\n            // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n            if (provider?.on && parameters.target) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            else if (shimDisconnect) {\n                // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n                try {\n                    const permissions = await provider.request({\n                        method: 'wallet_requestPermissions',\n                        params: [{ eth_accounts: {} }],\n                    });\n                    accounts = permissions[0]?.caveats?.[0]?.value?.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                    // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n                    // switch to `'eth_accounts'` ordering if more than one account is connected\n                    // https://github.com/wevm/wagmi/issues/4140\n                    if (accounts.length > 0) {\n                        const sortedAccounts = await this.getAccounts();\n                        accounts = sortedAccounts;\n                    }\n                }\n                catch (err) {\n                    const error = err;\n                    // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n                    // Only bubble up error if user rejects request\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    // Or prompt is already open\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                        throw error;\n                }\n            }\n            try {\n                if (!accounts?.length && !isReconnecting) {\n                    const requestedAccounts = await provider.request({\n                        method: 'eth_requestAccounts',\n                    });\n                    accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                }\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n                // Add connected shim if no target exists\n                if (!parameters.target)\n                    await config.storage?.setItem('injected.connected', true);\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            // Experimental support for MetaMask disconnect\n            // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n            try {\n                // Adding timeout as not all wallets support this method and can hang\n                // https://github.com/wevm/wagmi/issues/4064\n                await (0,viem__WEBPACK_IMPORTED_MODULE_4__.withTimeout)(() => \n                // TODO: Remove explicit type for viem@3\n                provider.request({\n                    // `'wallet_revokePermissions'` added in `viem@2.10.3`\n                    method: 'wallet_revokePermissions',\n                    params: [{ eth_accounts: {} }],\n                }), { timeout: 100 });\n            }\n            catch { }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect) {\n                await config.storage?.setItem(`${this.id}.disconnected`, true);\n            }\n            if (!parameters.target)\n                await config.storage?.removeItem('injected.connected');\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return Number(hexChainId);\n        },\n        async getProvider() {\n            if (typeof window === 'undefined')\n                return undefined;\n            let provider;\n            const target = getTarget();\n            if (typeof target.provider === 'function')\n                provider = target.provider(window);\n            else if (typeof target.provider === 'string')\n                provider = findProvider(window, target.provider);\n            else\n                provider = target.provider;\n            // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n            // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n            if (provider && !provider.removeListener) {\n                // Try using `off` handler if it exists, otherwise noop\n                if ('off' in provider && typeof provider.off === 'function')\n                    provider.removeListener =\n                        provider.off;\n                else\n                    provider.removeListener = () => { };\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem(`${this.id}.disconnected`));\n                if (isDisconnected)\n                    return false;\n                // Don't allow injected connector to connect if no target is set and it hasn't already connected\n                // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n                // automatically whenever there is a targeted connector configured.\n                if (!parameters.target) {\n                    const connected = await config.storage?.getItem('injected.connected');\n                    if (!connected)\n                        return false;\n                }\n                const provider = await this.getProvider();\n                if (!provider) {\n                    if (unstable_shimAsyncInject !== undefined &&\n                        unstable_shimAsyncInject !== false) {\n                        // If no provider is found, check for async injection\n                        // https://github.com/wevm/references/issues/167\n                        // https://github.com/MetaMask/detect-provider\n                        const handleEthereum = async () => {\n                            if (typeof window !== 'undefined')\n                                window.removeEventListener('ethereum#initialized', handleEthereum);\n                            const provider = await this.getProvider();\n                            return !!provider;\n                        };\n                        const timeout = typeof unstable_shimAsyncInject === 'number'\n                            ? unstable_shimAsyncInject\n                            : 1_000;\n                        const res = await Promise.race([\n                            ...(typeof window !== 'undefined'\n                                ? [\n                                    new Promise((resolve) => window.addEventListener('ethereum#initialized', () => resolve(handleEthereum()), { once: true })),\n                                ]\n                                : []),\n                            new Promise((resolve) => setTimeout(() => resolve(handleEthereum()), timeout)),\n                        ]);\n                        if (res)\n                            return true;\n                    }\n                    throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                }\n                // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n                // immediately resolve JSON-RPC requests on page load.\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_5__.withRetry)(() => this.getAccounts());\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError());\n            const promise = new Promise((resolve) => {\n                const listener = ((data) => {\n                    if ('chainId' in data && data.chainId === chainId) {\n                        config.emitter.off('change', listener);\n                        resolve();\n                    }\n                });\n                config.emitter.on('change', listener);\n            });\n            try {\n                await Promise.all([\n                    provider\n                        .request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId) }],\n                    })\n                        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                        // this callback or an externally emitted `'chainChanged'` event.\n                        // https://github.com/MetaMask/metamask-extension/issues/24247\n                        .then(async () => {\n                        const currentChainId = await this.getChainId();\n                        if (currentChainId === chainId)\n                            config.emitter.emit('change', { chainId });\n                    }),\n                    promise,\n                ]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else if (blockExplorer)\n                            blockExplorerUrls = [\n                                blockExplorer.url,\n                                ...Object.values(blockExplorers).map((x) => x.url),\n                            ];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await Promise.all([\n                            provider\n                                .request({\n                                method: 'wallet_addEthereumChain',\n                                params: [addEthereumChain],\n                            })\n                                .then(async () => {\n                                const currentChainId = await this.getChainId();\n                                if (currentChainId === chainId)\n                                    config.emitter.emit('change', { chainId });\n                                else\n                                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(new Error('User rejected switch after adding network.'));\n                            }),\n                            promise,\n                        ]);\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    }\n                }\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(error);\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0)\n                this.onDisconnect();\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            // Manage EIP-1193 event listeners\n            const provider = await this.getProvider();\n            if (provider) {\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n            // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n            // actually disconnected and we don't need to simulate it.\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (provider) {\n                if (chainChanged) {\n                    provider.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n            }\n        },\n    }));\n}\nconst targetMap = {\n    coinbaseWallet: {\n        id: 'coinbaseWallet',\n        name: 'Coinbase Wallet',\n        provider(window) {\n            if (window?.coinbaseWalletExtension)\n                return window.coinbaseWalletExtension;\n            return findProvider(window, 'isCoinbaseWallet');\n        },\n    },\n    metaMask: {\n        id: 'metaMask',\n        name: 'MetaMask',\n        provider(window) {\n            return findProvider(window, (provider) => {\n                if (!provider.isMetaMask)\n                    return false;\n                // Brave tries to make itself look like MetaMask\n                // Could also try RPC `web3_clientVersion` if following is unreliable\n                if (provider.isBraveWallet && !provider._events && !provider._state)\n                    return false;\n                // Other wallets that try to look like MetaMask\n                const flags = [\n                    'isApexWallet',\n                    'isAvalanche',\n                    'isBitKeep',\n                    'isBlockWallet',\n                    'isKuCoinWallet',\n                    'isMathWallet',\n                    'isOkxWallet',\n                    'isOKExWallet',\n                    'isOneInchIOSWallet',\n                    'isOneInchAndroidWallet',\n                    'isOpera',\n                    'isPhantom',\n                    'isPortal',\n                    'isRabby',\n                    'isTokenPocket',\n                    'isTokenary',\n                    'isUniswapWallet',\n                    'isZerion',\n                ];\n                for (const flag of flags)\n                    if (provider[flag])\n                        return false;\n                return true;\n            });\n        },\n    },\n    phantom: {\n        id: 'phantom',\n        name: 'Phantom',\n        provider(window) {\n            if (window?.phantom?.ethereum)\n                return window.phantom?.ethereum;\n            return findProvider(window, 'isPhantom');\n        },\n    },\n};\nfunction findProvider(window, select) {\n    function isProvider(provider) {\n        if (typeof select === 'function')\n            return select(provider);\n        if (typeof select === 'string')\n            return provider[select];\n        return true;\n    }\n    const ethereum = window.ethereum;\n    if (ethereum?.providers)\n        return ethereum.providers.find((provider) => isProvider(provider));\n    if (ethereum && isProvider(ethereum))\n        return ethereum;\n    return undefined;\n}\n//# sourceMappingURL=injected.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/mock.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mock: () => (/* binding */ mock)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/utils/rpc/compat.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\nmock.type = 'mock';\nfunction mock(parameters) {\n    const transactionCache = new Map();\n    const features = parameters.features ??\n        { defaultConnected: false };\n    let connected = features.defaultConnected;\n    let connectedChainId;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'mock',\n        name: 'Mock Connector',\n        type: mock.type,\n        async setup() {\n            connectedChainId = config.chains[0].id;\n        },\n        async connect({ chainId } = {}) {\n            if (features.connectError) {\n                if (typeof features.connectError === 'boolean')\n                    throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to connect.'));\n                throw features.connectError;\n            }\n            const provider = await this.getProvider();\n            const accounts = await provider.request({\n                method: 'eth_requestAccounts',\n            });\n            let currentChainId = await this.getChainId();\n            if (chainId && currentChainId !== chainId) {\n                const chain = await this.switchChain({ chainId });\n                currentChainId = chain.id;\n            }\n            connected = true;\n            return {\n                accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                chainId: currentChainId,\n            };\n        },\n        async disconnect() {\n            connected = false;\n        },\n        async getAccounts() {\n            if (!connected)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ConnectorNotConnectedError();\n            const provider = await this.getProvider();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(hexChainId, 'number');\n        },\n        async isAuthorized() {\n            if (!features.reconnect)\n                return false;\n            if (!connected)\n                return false;\n            const accounts = await this.getAccounts();\n            return !!accounts.length;\n        },\n        async switchChain({ chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_1__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            await provider.request({\n                method: 'wallet_switchEthereumChain',\n                params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(chainId) }],\n            });\n            return chain;\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            connected = false;\n        },\n        async getProvider({ chainId } = {}) {\n            const chain = config.chains.find((x) => x.id === chainId) ?? config.chains[0];\n            const url = chain.rpcUrls.default.http[0];\n            const request = async ({ method, params }) => {\n                // eth methods\n                if (method === 'eth_chainId')\n                    return (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(connectedChainId);\n                if (method === 'eth_requestAccounts')\n                    return parameters.accounts;\n                if (method === 'eth_signTypedData_v4')\n                    if (features.signTypedDataError) {\n                        if (typeof features.signTypedDataError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign typed data.'));\n                        throw features.signTypedDataError;\n                    }\n                // wallet methods\n                if (method === 'wallet_switchEthereumChain') {\n                    if (features.switchChainError) {\n                        if (typeof features.switchChainError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.switchChainError;\n                    }\n                    connectedChainId = (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(params[0].chainId, 'number');\n                    this.onChainChanged(connectedChainId.toString());\n                    return;\n                }\n                if (method === 'wallet_watchAsset') {\n                    if (features.watchAssetError) {\n                        if (typeof features.watchAssetError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.watchAssetError;\n                    }\n                    return connected;\n                }\n                if (method === 'wallet_getCapabilities')\n                    return {\n                        '0x2105': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                            sessionKeys: {\n                                supported: true,\n                            },\n                        },\n                        '0x14A34': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                        },\n                    };\n                if (method === 'wallet_sendCalls') {\n                    const hashes = [];\n                    const calls = params[0].calls;\n                    for (const call of calls) {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_sendTransaction',\n                                params: [call],\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        hashes.push(result);\n                    }\n                    const id = (0,viem__WEBPACK_IMPORTED_MODULE_8__.keccak256)((0,viem__WEBPACK_IMPORTED_MODULE_5__.stringToHex)(JSON.stringify(calls)));\n                    transactionCache.set(id, hashes);\n                    return { id };\n                }\n                if (method === 'wallet_getCallsStatus') {\n                    const hashes = transactionCache.get(params[0]);\n                    if (!hashes)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    const receipts = await Promise.all(hashes.map(async (hash) => {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_getTransactionReceipt',\n                                params: [hash],\n                                id: 0,\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        if (!result)\n                            return null;\n                        return {\n                            blockHash: result.blockHash,\n                            blockNumber: result.blockNumber,\n                            gasUsed: result.gasUsed,\n                            logs: result.logs,\n                            status: result.status,\n                            transactionHash: result.transactionHash,\n                        };\n                    }));\n                    const receipts_ = receipts.filter((x) => x !== null);\n                    if (receipts_.length === 0)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    return {\n                        atomic: false,\n                        chainId: '0x1',\n                        id: params[0],\n                        status: 200,\n                        receipts: receipts_,\n                        version: '2.0.0',\n                    };\n                }\n                if (method === 'wallet_showCallsStatus')\n                    return;\n                // other methods\n                if (method === 'personal_sign') {\n                    if (features.signMessageError) {\n                        if (typeof features.signMessageError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign message.'));\n                        throw features.signMessageError;\n                    }\n                    // Change `personal_sign` to `eth_sign` and swap params\n                    method = 'eth_sign';\n                    params = [params[1], params[0]];\n                }\n                const body = { method, params };\n                const { error, result } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, { body });\n                if (error)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({ body, error, url });\n                return result;\n            };\n            return (0,viem__WEBPACK_IMPORTED_MODULE_9__.custom)({ request })({ retryCount: 0 });\n        },\n    }));\n}\n//# sourceMappingURL=mock.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfig: () => (/* binding */ createConfig)\n/* harmony export */ });\n/* harmony import */ var mipd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mipd */ \"(ssr)/./node_modules/mipd/dist/esm/store.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var _connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors/injected.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _createEmitter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createEmitter.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\");\n/* harmony import */ var _createStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createStorage.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/uid.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\n\n\n\n\n\n\n\n\n\nfunction createConfig(parameters) {\n    const { multiInjectedProviderDiscovery = true, storage = (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.createStorage)({\n        storage: (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultStorage)(),\n    }), syncConnectedChain = true, ssr = false, ...rest } = parameters;\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Set up connectors, clients, etc.\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    const mipd = typeof window !== 'undefined' && multiInjectedProviderDiscovery\n        ? (0,mipd__WEBPACK_IMPORTED_MODULE_1__.createStore)()\n        : undefined;\n    const chains = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => rest.chains);\n    const connectors = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => {\n        const collection = [];\n        const rdnsSet = new Set();\n        for (const connectorFns of rest.connectors ?? []) {\n            const connector = setup(connectorFns);\n            collection.push(connector);\n            if (!ssr && connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    rdnsSet.add(rdns);\n                }\n            }\n        }\n        if (!ssr && mipd) {\n            const providers = mipd.getProviders();\n            for (const provider of providers) {\n                if (rdnsSet.has(provider.info.rdns))\n                    continue;\n                collection.push(setup(providerDetailToConnector(provider)));\n            }\n        }\n        return collection;\n    });\n    function setup(connectorFn) {\n        // Set up emitter with uid and add to connector so they are \"linked\" together.\n        const emitter = (0,_createEmitter_js__WEBPACK_IMPORTED_MODULE_3__.createEmitter)((0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_4__.uid)());\n        const connector = {\n            ...connectorFn({\n                emitter,\n                chains: chains.getState(),\n                storage,\n                transports: rest.transports,\n            }),\n            emitter,\n            uid: emitter.uid,\n        };\n        // Start listening for `connect` events on connector setup\n        // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n        emitter.on('connect', connect);\n        connector.setup?.();\n        return connector;\n    }\n    function providerDetailToConnector(providerDetail) {\n        const { info } = providerDetail;\n        const provider = providerDetail.provider;\n        return (0,_connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__.injected)({ target: { ...info, id: info.rdns, provider } });\n    }\n    const clients = new Map();\n    function getClient(config = {}) {\n        const chainId = config.chainId ?? store.getState().chainId;\n        const chain = chains.getState().find((x) => x.id === chainId);\n        // chainId specified and not configured\n        if (config.chainId && !chain)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        {\n            const client = clients.get(store.getState().chainId);\n            if (client && !chain)\n                return client;\n            if (!chain)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        }\n        // If a memoized client exists for a chain id, use that.\n        {\n            const client = clients.get(chainId);\n            if (client)\n                return client;\n        }\n        let client;\n        if (rest.client)\n            client = rest.client({ chain });\n        else {\n            const chainId = chain.id;\n            const chainIds = chains.getState().map((x) => x.id);\n            // Grab all properties off `rest` and resolve for use in `createClient`\n            const properties = {};\n            const entries = Object.entries(rest);\n            for (const [key, value] of entries) {\n                if (key === 'chains' ||\n                    key === 'client' ||\n                    key === 'connectors' ||\n                    key === 'transports')\n                    continue;\n                if (typeof value === 'object') {\n                    // check if value is chainId-specific since some values can be objects\n                    // e.g. { batch: { multicall: { batchSize: 1024 } } }\n                    if (chainId in value)\n                        properties[key] = value[chainId];\n                    else {\n                        // check if value is chainId-specific, but does not have value for current chainId\n                        const hasChainSpecificValue = chainIds.some((x) => x in value);\n                        if (hasChainSpecificValue)\n                            continue;\n                        properties[key] = value;\n                    }\n                }\n                else\n                    properties[key] = value;\n            }\n            client = (0,viem__WEBPACK_IMPORTED_MODULE_7__.createClient)({\n                ...properties,\n                chain,\n                batch: properties.batch ?? { multicall: true },\n                transport: (parameters) => rest.transports[chainId]({ ...parameters, connectors }),\n            });\n        }\n        clients.set(chainId, client);\n        return client;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Create store\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function getInitialState() {\n        return {\n            chainId: chains.getState()[0].id,\n            connections: new Map(),\n            current: null,\n            status: 'disconnected',\n        };\n    }\n    let currentVersion;\n    const prefix = '0.0.0-canary-';\n    if (_version_js__WEBPACK_IMPORTED_MODULE_8__.version.startsWith(prefix))\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.replace(prefix, ''));\n    // use package major version to version store\n    else\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.split('.')[0] ?? '0');\n    const store = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.subscribeWithSelector)(\n    // only use persist middleware if storage exists\n    storage\n        ? (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.persist)(getInitialState, {\n            migrate(persistedState, version) {\n                if (version === currentVersion)\n                    return persistedState;\n                const initialState = getInitialState();\n                const chainId = validatePersistedChainId(persistedState, initialState.chainId);\n                return { ...initialState, chainId };\n            },\n            name: 'store',\n            partialize(state) {\n                // Only persist \"critical\" store properties to preserve storage size.\n                return {\n                    connections: {\n                        __type: 'Map',\n                        value: Array.from(state.connections.entries()).map(([key, connection]) => {\n                            const { id, name, type, uid } = connection.connector;\n                            const connector = { id, name, type, uid };\n                            return [key, { ...connection, connector }];\n                        }),\n                    },\n                    chainId: state.chainId,\n                    current: state.current,\n                };\n            },\n            merge(persistedState, currentState) {\n                // `status` should not be persisted as it messes with reconnection\n                if (typeof persistedState === 'object' &&\n                    persistedState &&\n                    'status' in persistedState)\n                    delete persistedState.status;\n                // Make sure persisted `chainId` is valid\n                const chainId = validatePersistedChainId(persistedState, currentState.chainId);\n                return {\n                    ...currentState,\n                    ...persistedState,\n                    chainId,\n                };\n            },\n            skipHydration: ssr,\n            storage: storage,\n            version: currentVersion,\n        })\n        : getInitialState));\n    store.setState(getInitialState());\n    function validatePersistedChainId(persistedState, defaultChainId) {\n        return persistedState &&\n            typeof persistedState === 'object' &&\n            'chainId' in persistedState &&\n            typeof persistedState.chainId === 'number' &&\n            chains.getState().some((x) => x.id === persistedState.chainId)\n            ? persistedState.chainId\n            : defaultChainId;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Subscribe to changes\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Update default chain when connector chain changes\n    if (syncConnectedChain)\n        store.subscribe(({ connections, current }) => current ? connections.get(current)?.chainId : undefined, (chainId) => {\n            // If chain is not configured, then don't switch over to it.\n            const isChainConfigured = chains\n                .getState()\n                .some((x) => x.id === chainId);\n            if (!isChainConfigured)\n                return;\n            return store.setState((x) => ({\n                ...x,\n                chainId: chainId ?? x.chainId,\n            }));\n        });\n    // EIP-6963 subscribe for new wallet providers\n    mipd?.subscribe((providerDetails) => {\n        const connectorIdSet = new Set();\n        const connectorRdnsSet = new Set();\n        for (const connector of connectors.getState()) {\n            connectorIdSet.add(connector.id);\n            if (connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    connectorRdnsSet.add(rdns);\n                }\n            }\n        }\n        const newConnectors = [];\n        for (const providerDetail of providerDetails) {\n            if (connectorRdnsSet.has(providerDetail.info.rdns))\n                continue;\n            const connector = setup(providerDetailToConnector(providerDetail));\n            if (connectorIdSet.has(connector.id))\n                continue;\n            newConnectors.push(connector);\n        }\n        if (storage && !store.persist.hasHydrated())\n            return;\n        connectors.setState((x) => [...x, ...newConnectors], true);\n    });\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Emitter listeners\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function change(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (!connection)\n                return x;\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts ??\n                        connection.accounts,\n                    chainId: data.chainId ?? connection.chainId,\n                    connector: connection.connector,\n                }),\n            };\n        });\n    }\n    function connect(data) {\n        // Disable handling if reconnecting/connecting\n        if (store.getState().status === 'connecting' ||\n            store.getState().status === 'reconnecting')\n            return;\n        store.setState((x) => {\n            const connector = connectors.getState().find((x) => x.uid === data.uid);\n            if (!connector)\n                return x;\n            if (connector.emitter.listenerCount('connect'))\n                connector.emitter.off('connect', change);\n            if (!connector.emitter.listenerCount('change'))\n                connector.emitter.on('change', change);\n            if (!connector.emitter.listenerCount('disconnect'))\n                connector.emitter.on('disconnect', disconnect);\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts,\n                    chainId: data.chainId,\n                    connector: connector,\n                }),\n                current: data.uid,\n                status: 'connected',\n            };\n        });\n    }\n    function disconnect(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (connection) {\n                const connector = connection.connector;\n                if (connector.emitter.listenerCount('change'))\n                    connection.connector.emitter.off('change', change);\n                if (connector.emitter.listenerCount('disconnect'))\n                    connection.connector.emitter.off('disconnect', disconnect);\n                if (!connector.emitter.listenerCount('connect'))\n                    connection.connector.emitter.on('connect', connect);\n            }\n            x.connections.delete(data.uid);\n            if (x.connections.size === 0)\n                return {\n                    ...x,\n                    connections: new Map(),\n                    current: null,\n                    status: 'disconnected',\n                };\n            const nextConnection = x.connections.values().next().value;\n            return {\n                ...x,\n                connections: new Map(x.connections),\n                current: nextConnection.connector.uid,\n            };\n        });\n    }\n    return {\n        get chains() {\n            return chains.getState();\n        },\n        get connectors() {\n            return connectors.getState();\n        },\n        storage,\n        getClient,\n        get state() {\n            return store.getState();\n        },\n        setState(value) {\n            let newState;\n            if (typeof value === 'function')\n                newState = value(store.getState());\n            else\n                newState = value;\n            // Reset state if it got set to something not matching the base state\n            const initialState = getInitialState();\n            if (typeof newState !== 'object')\n                newState = initialState;\n            const isCorrupt = Object.keys(initialState).some((x) => !(x in newState));\n            if (isCorrupt)\n                newState = initialState;\n            store.setState(newState, true);\n        },\n        subscribe(selector, listener, options) {\n            return store.subscribe(selector, listener, options\n                ? {\n                    ...options,\n                    fireImmediately: options.emitImmediately,\n                    // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n                }\n                : undefined);\n        },\n        _internal: {\n            mipd,\n            store,\n            ssr: Boolean(ssr),\n            syncConnectedChain,\n            transports: rest.transports,\n            chains: {\n                setState(value) {\n                    const nextChains = (typeof value === 'function' ? value(chains.getState()) : value);\n                    if (nextChains.length === 0)\n                        return;\n                    return chains.setState(nextChains, true);\n                },\n                subscribe(listener) {\n                    return chains.subscribe(listener);\n                },\n            },\n            connectors: {\n                providerDetailToConnector,\n                setup: setup,\n                setState(value) {\n                    return connectors.setState(typeof value === 'function' ? value(connectors.getState()) : value, true);\n                },\n                subscribe(listener) {\n                    return connectors.subscribe(listener);\n                },\n            },\n            events: { change, connect, disconnect },\n        },\n    };\n}\n//# sourceMappingURL=createConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createEmitter.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter),\n/* harmony export */   createEmitter: () => (/* binding */ createEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/eventemitter3/index.mjs\");\n\nclass Emitter {\n    constructor(uid) {\n        Object.defineProperty(this, \"uid\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: uid\n        });\n        Object.defineProperty(this, \"_emitter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter()\n        });\n    }\n    on(eventName, fn) {\n        this._emitter.on(eventName, fn);\n    }\n    once(eventName, fn) {\n        this._emitter.once(eventName, fn);\n    }\n    off(eventName, fn) {\n        this._emitter.off(eventName, fn);\n    }\n    emit(eventName, ...params) {\n        const data = params[0];\n        this._emitter.emit(eventName, { uid: this.uid, ...data });\n    }\n    listenerCount(eventName) {\n        return this._emitter.listenerCount(eventName);\n    }\n}\nfunction createEmitter(uid) {\n    return new Emitter(uid);\n}\n//# sourceMappingURL=createEmitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createStorage.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   getDefaultStorage: () => (/* binding */ getDefaultStorage),\n/* harmony export */   noopStorage: () => (/* binding */ noopStorage)\n/* harmony export */ });\n/* harmony import */ var _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/deserialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\");\n/* harmony import */ var _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/serialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\");\n\n\nfunction createStorage(parameters) {\n    const { deserialize = _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize, key: prefix = 'wagmi', serialize = _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize, storage = noopStorage, } = parameters;\n    function unwrap(value) {\n        if (value instanceof Promise)\n            return value.then((x) => x).catch(() => null);\n        return value;\n    }\n    return {\n        ...storage,\n        key: prefix,\n        async getItem(key, defaultValue) {\n            const value = storage.getItem(`${prefix}.${key}`);\n            const unwrapped = await unwrap(value);\n            if (unwrapped)\n                return deserialize(unwrapped) ?? null;\n            return (defaultValue ?? null);\n        },\n        async setItem(key, value) {\n            const storageKey = `${prefix}.${key}`;\n            if (value === null)\n                await unwrap(storage.removeItem(storageKey));\n            else\n                await unwrap(storage.setItem(storageKey, serialize(value)));\n        },\n        async removeItem(key) {\n            await unwrap(storage.removeItem(`${prefix}.${key}`));\n        },\n    };\n}\nconst noopStorage = {\n    getItem: () => null,\n    setItem: () => { },\n    removeItem: () => { },\n};\nfunction getDefaultStorage() {\n    const storage = (() => {\n        if (typeof window !== 'undefined' && window.localStorage)\n            return window.localStorage;\n        return noopStorage;\n    })();\n    return {\n        getItem(key) {\n            return storage.getItem(key);\n        },\n        removeItem(key) {\n            storage.removeItem(key);\n        },\n        setItem(key, value) {\n            try {\n                storage.setItem(key, value);\n                // silence errors by default (QuotaExceededError, SecurityError, etc.)\n            }\n            catch { }\n        },\n    };\n}\n//# sourceMappingURL=createStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/base.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\");\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _BaseError_instances, _BaseError_walk;\n\nclass BaseError extends Error {\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/core';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)();\n    }\n    constructor(shortMessage, options = {}) {\n        super();\n        _BaseError_instances.add(this);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiCoreError'\n        });\n        const details = options.cause instanceof BaseError\n            ? options.cause.details\n            : options.cause?.message\n                ? options.cause.message\n                : options.details;\n        const docsPath = options.cause instanceof BaseError\n            ? options.cause.docsPath || options.docsPath\n            : options.docsPath;\n        this.message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n            ...(docsPath\n                ? [\n                    `Docs: ${this.docsBaseUrl}${docsPath}.html${options.docsSlug ? `#${options.docsSlug}` : ''}`,\n                ]\n                : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: ${this.version}`,\n        ].join('\\n');\n        if (options.cause)\n            this.cause = options.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = options.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, this, fn);\n    }\n}\n_BaseError_instances = new WeakSet(), _BaseError_walk = function _BaseError_walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err.cause)\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, err.cause, fn);\n    return err;\n};\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/config.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChainNotConfiguredError: () => (/* binding */ ChainNotConfiguredError),\n/* harmony export */   ConnectorAccountNotFoundError: () => (/* binding */ ConnectorAccountNotFoundError),\n/* harmony export */   ConnectorAlreadyConnectedError: () => (/* binding */ ConnectorAlreadyConnectedError),\n/* harmony export */   ConnectorChainMismatchError: () => (/* binding */ ConnectorChainMismatchError),\n/* harmony export */   ConnectorNotConnectedError: () => (/* binding */ ConnectorNotConnectedError),\n/* harmony export */   ConnectorNotFoundError: () => (/* binding */ ConnectorNotFoundError),\n/* harmony export */   ConnectorUnavailableReconnectingError: () => (/* binding */ ConnectorUnavailableReconnectingError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ChainNotConfiguredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Chain not configured.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ChainNotConfiguredError'\n        });\n    }\n}\nclass ConnectorAlreadyConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector already connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAlreadyConnectedError'\n        });\n    }\n}\nclass ConnectorNotConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotConnectedError'\n        });\n    }\n}\nclass ConnectorNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotFoundError'\n        });\n    }\n}\nclass ConnectorAccountNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address, connector, }) {\n        super(`Account \"${address}\" not found for connector \"${connector.name}\".`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAccountNotFoundError'\n        });\n    }\n}\nclass ConnectorChainMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connectionChainId, connectorChainId, }) {\n        super(`The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`, {\n            metaMessages: [\n                `Current Chain ID:  ${connectorChainId}`,\n                `Expected Chain ID: ${connectionChainId}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorChainMismatchError'\n        });\n    }\n}\nclass ConnectorUnavailableReconnectingError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n            details: [\n                'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n                'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n                'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n            ].join(' '),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorUnavailableReconnectingError'\n        });\n    }\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQztBQUMvQixzQ0FBc0MsK0NBQVM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLDZDQUE2QywrQ0FBUztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ08seUNBQXlDLCtDQUFTO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTyxxQ0FBcUMsK0NBQVM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLDRDQUE0QywrQ0FBUztBQUM1RCxrQkFBa0IscUJBQXFCO0FBQ3ZDLDBCQUEwQixRQUFRLDZCQUE2QixlQUFlO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLDBDQUEwQywrQ0FBUztBQUMxRCxrQkFBa0Isc0NBQXNDO0FBQ3hELHlEQUF5RCxpQkFBaUIsK0NBQStDLGtCQUFrQjtBQUMzSTtBQUNBLHNDQUFzQyxpQkFBaUI7QUFDdkQsc0NBQXNDLGtCQUFrQjtBQUN4RDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTyxvREFBb0QsK0NBQVM7QUFDcEUsa0JBQWtCLFdBQVc7QUFDN0IsNEJBQTRCLGVBQWU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcZXJyb3JzXFxjb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdDaGFpbiBub3QgY29uZmlndXJlZC4nKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0NoYWluTm90Q29uZmlndXJlZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgQ29ubmVjdG9yQWxyZWFkeUNvbm5lY3RlZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoJ0Nvbm5lY3RvciBhbHJlYWR5IGNvbm5lY3RlZC4nKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0Nvbm5lY3RvckFscmVhZHlDb25uZWN0ZWRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIENvbm5lY3Rvck5vdENvbm5lY3RlZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoJ0Nvbm5lY3RvciBub3QgY29ubmVjdGVkLicpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnQ29ubmVjdG9yTm90Q29ubmVjdGVkRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBDb25uZWN0b3JOb3RGb3VuZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoJ0Nvbm5lY3RvciBub3QgZm91bmQuJyk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDb25uZWN0b3JOb3RGb3VuZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgQ29ubmVjdG9yQWNjb3VudE5vdEZvdW5kRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHsgYWRkcmVzcywgY29ubmVjdG9yLCB9KSB7XG4gICAgICAgIHN1cGVyKGBBY2NvdW50IFwiJHthZGRyZXNzfVwiIG5vdCBmb3VuZCBmb3IgY29ubmVjdG9yIFwiJHtjb25uZWN0b3IubmFtZX1cIi5gKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0Nvbm5lY3RvckFjY291bnROb3RGb3VuZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgQ29ubmVjdG9yQ2hhaW5NaXNtYXRjaEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGNvbm5lY3Rpb25DaGFpbklkLCBjb25uZWN0b3JDaGFpbklkLCB9KSB7XG4gICAgICAgIHN1cGVyKGBUaGUgY3VycmVudCBjaGFpbiBvZiB0aGUgY29ubmVjdG9yIChpZDogJHtjb25uZWN0b3JDaGFpbklkfSkgZG9lcyBub3QgbWF0Y2ggdGhlIGNvbm5lY3Rpb24ncyBjaGFpbiAoaWQ6ICR7Y29ubmVjdGlvbkNoYWluSWR9KS5gLCB7XG4gICAgICAgICAgICBtZXRhTWVzc2FnZXM6IFtcbiAgICAgICAgICAgICAgICBgQ3VycmVudCBDaGFpbiBJRDogICR7Y29ubmVjdG9yQ2hhaW5JZH1gLFxuICAgICAgICAgICAgICAgIGBFeHBlY3RlZCBDaGFpbiBJRDogJHtjb25uZWN0aW9uQ2hhaW5JZH1gLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDb25uZWN0b3JDaGFpbk1pc21hdGNoRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBDb25uZWN0b3JVbmF2YWlsYWJsZVJlY29ubmVjdGluZ0Vycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGNvbm5lY3RvciB9KSB7XG4gICAgICAgIHN1cGVyKGBDb25uZWN0b3IgXCIke2Nvbm5lY3Rvci5uYW1lfVwiIHVuYXZhaWxhYmxlIHdoaWxlIHJlY29ubmVjdGluZy5gLCB7XG4gICAgICAgICAgICBkZXRhaWxzOiBbXG4gICAgICAgICAgICAgICAgJ0R1cmluZyB0aGUgcmVjb25uZWN0aW9uIHN0ZXAsIHRoZSBvbmx5IGNvbm5lY3RvciBtZXRob2RzIGd1YXJhbnRlZWQgdG8gYmUgYXZhaWxhYmxlIGFyZTogYGlkYCwgYG5hbWVgLCBgdHlwZWAsIGB1aWRgLicsXG4gICAgICAgICAgICAgICAgJ0FsbCBvdGhlciBtZXRob2RzIGFyZSBub3QgZ3VhcmFudGVlZCB0byBiZSBhdmFpbGFibGUgdW50aWwgcmVjb25uZWN0aW9uIGNvbXBsZXRlcyBhbmQgY29ubmVjdG9ycyBhcmUgZnVsbHkgcmVzdG9yZWQuJyxcbiAgICAgICAgICAgICAgICAnVGhpcyBlcnJvciBjb21tb25seSBvY2N1cnMgZm9yIGNvbm5lY3RvcnMgdGhhdCBhc3luY2hyb25vdXNseSBpbmplY3QgYWZ0ZXIgcmVjb25uZWN0aW9uIGhhcyBhbHJlYWR5IHN0YXJ0ZWQuJyxcbiAgICAgICAgICAgIF0uam9pbignICcpLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0Nvbm5lY3RvclVuYXZhaWxhYmxlUmVjb25uZWN0aW5nRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbmZpZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/connector.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderNotFoundError: () => (/* binding */ ProviderNotFoundError),\n/* harmony export */   SwitchChainNotSupportedError: () => (/* binding */ SwitchChainNotSupportedError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Provider not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ProviderNotFoundError'\n        });\n    }\n}\nclass SwitchChainNotSupportedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`\"${connector.name}\" does not support programmatic chain switching.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SwitchChainNotSupportedError'\n        });\n    }\n}\n//# sourceMappingURL=connector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Nvbm5lY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0Isb0NBQW9DLCtDQUFTO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTywyQ0FBMkMsK0NBQVM7QUFDM0Qsa0JBQWtCLFdBQVc7QUFDN0Isa0JBQWtCLGVBQWU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGVycm9yc1xcY29ubmVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VFcnJvciB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY2xhc3MgUHJvdmlkZXJOb3RGb3VuZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoJ1Byb3ZpZGVyIG5vdCBmb3VuZC4nKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ1Byb3ZpZGVyTm90Rm91bmRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIFN3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHsgY29ubmVjdG9yIH0pIHtcbiAgICAgICAgc3VwZXIoYFwiJHtjb25uZWN0b3IubmFtZX1cIiBkb2VzIG5vdCBzdXBwb3J0IHByb2dyYW1tYXRpYyBjaGFpbiBzd2l0Y2hpbmcuYCk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25uZWN0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/reconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\");\n\nfunction hydrate(config, parameters) {\n    const { initialState, reconnectOnMount } = parameters;\n    if (initialState && !config._internal.store.persist.hasHydrated())\n        config.setState({\n            ...initialState,\n            chainId: config.chains.some((x) => x.id === initialState.chainId)\n                ? initialState.chainId\n                : config.chains[0].id,\n            connections: reconnectOnMount ? initialState.connections : new Map(),\n            status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n        });\n    return {\n        async onMount() {\n            if (config._internal.ssr) {\n                await config._internal.store.persist.rehydrate();\n                if (config._internal.mipd) {\n                    config._internal.connectors.setState((connectors) => {\n                        const rdnsSet = new Set();\n                        for (const connector of connectors ?? []) {\n                            if (connector.rdns) {\n                                const rdnsValues = Array.isArray(connector.rdns)\n                                    ? connector.rdns\n                                    : [connector.rdns];\n                                for (const rdns of rdnsValues) {\n                                    rdnsSet.add(rdns);\n                                }\n                            }\n                        }\n                        const mipdConnectors = [];\n                        const providers = config._internal.mipd?.getProviders() ?? [];\n                        for (const provider of providers) {\n                            if (rdnsSet.has(provider.info.rdns))\n                                continue;\n                            const connectorFn = config._internal.connectors.providerDetailToConnector(provider);\n                            const connector = config._internal.connectors.setup(connectorFn);\n                            mipdConnectors.push(connector);\n                        }\n                        return [...connectors, ...mipdConnectors];\n                    });\n                }\n            }\n            if (reconnectOnMount)\n                (0,_actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__.reconnect)(config);\n            else if (config.storage)\n                // Reset connections that may have been hydrated from storage.\n                config.setState((x) => ({\n                    ...x,\n                    connections: new Map(),\n                }));\n        },\n    };\n}\n//# sourceMappingURL=hydrate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/** Forked from https://github.com/epoberezkin/fast-deep-equal */\nfunction deepEqual(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n        if (a.constructor !== b.constructor)\n            return false;\n        let length;\n        let i;\n        if (Array.isArray(a) && Array.isArray(b)) {\n            length = a.length;\n            if (length !== b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!deepEqual(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (a.valueOf !== Object.prototype.valueOf)\n            return a.valueOf() === b.valueOf();\n        if (a.toString !== Object.prototype.toString)\n            return a.toString() === b.toString();\n        const keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            const key = keys[i];\n            if (key && !deepEqual(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    // true if both NaN, false otherwise\n    // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n    return a !== a && b !== b;\n}\n//# sourceMappingURL=deepEqual.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVlcEVxdWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixVQUFVO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixVQUFVO0FBQ25DO0FBQ0E7QUFDQSx5QkFBeUIsVUFBVTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFx1dGlsc1xcZGVlcEVxdWFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKiBGb3JrZWQgZnJvbSBodHRwczovL2dpdGh1Yi5jb20vZXBvYmVyZXpraW4vZmFzdC1kZWVwLWVxdWFsICovXG5leHBvcnQgZnVuY3Rpb24gZGVlcEVxdWFsKGEsIGIpIHtcbiAgICBpZiAoYSA9PT0gYilcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgaWYgKGEgJiYgYiAmJiB0eXBlb2YgYSA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIGIgPT09ICdvYmplY3QnKSB7XG4gICAgICAgIGlmIChhLmNvbnN0cnVjdG9yICE9PSBiLmNvbnN0cnVjdG9yKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICBsZXQgbGVuZ3RoO1xuICAgICAgICBsZXQgaTtcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoYSkgJiYgQXJyYXkuaXNBcnJheShiKSkge1xuICAgICAgICAgICAgbGVuZ3RoID0gYS5sZW5ndGg7XG4gICAgICAgICAgICBpZiAobGVuZ3RoICE9PSBiLmxlbmd0aClcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICBmb3IgKGkgPSBsZW5ndGg7IGktLSAhPT0gMDspXG4gICAgICAgICAgICAgICAgaWYgKCFkZWVwRXF1YWwoYVtpXSwgYltpXSkpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChhLnZhbHVlT2YgIT09IE9iamVjdC5wcm90b3R5cGUudmFsdWVPZilcbiAgICAgICAgICAgIHJldHVybiBhLnZhbHVlT2YoKSA9PT0gYi52YWx1ZU9mKCk7XG4gICAgICAgIGlmIChhLnRvU3RyaW5nICE9PSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nKVxuICAgICAgICAgICAgcmV0dXJuIGEudG9TdHJpbmcoKSA9PT0gYi50b1N0cmluZygpO1xuICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMoYSk7XG4gICAgICAgIGxlbmd0aCA9IGtleXMubGVuZ3RoO1xuICAgICAgICBpZiAobGVuZ3RoICE9PSBPYmplY3Qua2V5cyhiKS5sZW5ndGgpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGZvciAoaSA9IGxlbmd0aDsgaS0tICE9PSAwOylcbiAgICAgICAgICAgIGlmICghT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGIsIGtleXNbaV0pKVxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgZm9yIChpID0gbGVuZ3RoOyBpLS0gIT09IDA7KSB7XG4gICAgICAgICAgICBjb25zdCBrZXkgPSBrZXlzW2ldO1xuICAgICAgICAgICAgaWYgKGtleSAmJiAhZGVlcEVxdWFsKGFba2V5XSwgYltrZXldKSlcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIC8vIHRydWUgaWYgYm90aCBOYU4sIGZhbHNlIG90aGVyd2lzZVxuICAgIC8vIGJpb21lLWlnbm9yZSBsaW50L3N1c3BpY2lvdXMvbm9TZWxmQ29tcGFyZTogPGV4cGxhbmF0aW9uPlxuICAgIHJldHVybiBhICE9PSBhICYmIGIgIT09IGI7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZWVwRXF1YWwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deserialize.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\nfunction deserialize(value, reviver) {\n    return JSON.parse(value, (key, value_) => {\n        let value = value_;\n        if (value?.__type === 'bigint')\n            value = BigInt(value.value);\n        if (value?.__type === 'Map')\n            value = new Map(value.value);\n        return reviver?.(key, value) ?? value;\n    });\n}\n//# sourceMappingURL=deserialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGRlc2VyaWFsaXplLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBkZXNlcmlhbGl6ZSh2YWx1ZSwgcmV2aXZlcikge1xuICAgIHJldHVybiBKU09OLnBhcnNlKHZhbHVlLCAoa2V5LCB2YWx1ZV8pID0+IHtcbiAgICAgICAgbGV0IHZhbHVlID0gdmFsdWVfO1xuICAgICAgICBpZiAodmFsdWU/Ll9fdHlwZSA9PT0gJ2JpZ2ludCcpXG4gICAgICAgICAgICB2YWx1ZSA9IEJpZ0ludCh2YWx1ZS52YWx1ZSk7XG4gICAgICAgIGlmICh2YWx1ZT8uX190eXBlID09PSAnTWFwJylcbiAgICAgICAgICAgIHZhbHVlID0gbmV3IE1hcCh2YWx1ZS52YWx1ZSk7XG4gICAgICAgIHJldHVybiByZXZpdmVyPy4oa2V5LCB2YWx1ZSkgPz8gdmFsdWU7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZXNlcmlhbGl6ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: () => (/* binding */ extractRpcUrls)\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZXh0cmFjdFJwY1VybHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AsWUFBWSxRQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxPQUFPO0FBQ25FO0FBQ0EsNkJBQTZCLE9BQU87QUFDcEM7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGV4dHJhY3RScGNVcmxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBleHRyYWN0UnBjVXJscyhwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBjaGFpbiB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBmYWxsYmFja1VybCA9IGNoYWluLnJwY1VybHMuZGVmYXVsdC5odHRwWzBdO1xuICAgIGlmICghcGFyYW1ldGVycy50cmFuc3BvcnRzKVxuICAgICAgICByZXR1cm4gW2ZhbGxiYWNrVXJsXTtcbiAgICBjb25zdCB0cmFuc3BvcnQgPSBwYXJhbWV0ZXJzLnRyYW5zcG9ydHM/LltjaGFpbi5pZF0/Lih7IGNoYWluIH0pO1xuICAgIGNvbnN0IHRyYW5zcG9ydHMgPSB0cmFuc3BvcnQ/LnZhbHVlPy50cmFuc3BvcnRzIHx8IFt0cmFuc3BvcnRdO1xuICAgIHJldHVybiB0cmFuc3BvcnRzLm1hcCgoeyB2YWx1ZSB9KSA9PiB2YWx1ZT8udXJsIHx8IGZhbGxiYWNrVXJsKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV4dHJhY3RScGNVcmxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getAction.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAction: () => (/* binding */ getAction)\n/* harmony export */ });\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nfunction getAction(client, actionFn, \n// Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n// meaning that `actionFn.name` will not always work. For that case, the consumer\n// needs to pass the name explicitly.\nname) {\n    const action_implicit = client[actionFn.name];\n    if (typeof action_implicit === 'function')\n        return action_implicit;\n    const action_explicit = client[name];\n    if (typeof action_explicit === 'function')\n        return action_explicit;\n    return (params) => actionFn(client, params);\n}\n//# sourceMappingURL=getAction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0QWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxnZXRBY3Rpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXRyaWV2ZXMgYW5kIHJldHVybnMgYW4gYWN0aW9uIGZyb20gdGhlIGNsaWVudCAoaWYgZXhpc3RzKSwgYW5kIGZhbGxzXG4gKiBiYWNrIHRvIHRoZSB0cmVlLXNoYWthYmxlIGFjdGlvbi5cbiAqXG4gKiBVc2VmdWwgZm9yIGV4dHJhY3Rpbmcgb3ZlcnJpZGRlbiBhY3Rpb25zIGZyb20gYSBjbGllbnQgKGllLiBpZiBhIGNvbnN1bWVyXG4gKiB3YW50cyB0byBvdmVycmlkZSB0aGUgYHNlbmRUcmFuc2FjdGlvbmAgaW1wbGVtZW50YXRpb24pLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0QWN0aW9uKGNsaWVudCwgYWN0aW9uRm4sIFxuLy8gU29tZSBtaW5pZmllcnMgZHJvcCBgRnVuY3Rpb24ucHJvdG90eXBlLm5hbWVgLCBvciByZXBsYWNlIGl0IHdpdGggc2hvcnQgbGV0dGVycyxcbi8vIG1lYW5pbmcgdGhhdCBgYWN0aW9uRm4ubmFtZWAgd2lsbCBub3QgYWx3YXlzIHdvcmsuIEZvciB0aGF0IGNhc2UsIHRoZSBjb25zdW1lclxuLy8gbmVlZHMgdG8gcGFzcyB0aGUgbmFtZSBleHBsaWNpdGx5LlxubmFtZSkge1xuICAgIGNvbnN0IGFjdGlvbl9pbXBsaWNpdCA9IGNsaWVudFthY3Rpb25Gbi5uYW1lXTtcbiAgICBpZiAodHlwZW9mIGFjdGlvbl9pbXBsaWNpdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgcmV0dXJuIGFjdGlvbl9pbXBsaWNpdDtcbiAgICBjb25zdCBhY3Rpb25fZXhwbGljaXQgPSBjbGllbnRbbmFtZV07XG4gICAgaWYgKHR5cGVvZiBhY3Rpb25fZXhwbGljaXQgPT09ICdmdW5jdGlvbicpXG4gICAgICAgIHJldHVybiBhY3Rpb25fZXhwbGljaXQ7XG4gICAgcmV0dXJuIChwYXJhbXMpID0+IGFjdGlvbkZuKGNsaWVudCwgcGFyYW1zKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldEFjdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getUnit.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnit: () => (/* binding */ getUnit)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/constants/unit.js\");\n\nfunction getUnit(unit) {\n    if (typeof unit === 'number')\n        return unit;\n    if (unit === 'wei')\n        return 0;\n    return Math.abs(viem__WEBPACK_IMPORTED_MODULE_0__.weiUnits[unit]);\n}\n//# sourceMappingURL=getUnit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VW5pdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQztBQUN6QjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDBDQUFRO0FBQzVCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxnZXRVbml0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdlaVVuaXRzIH0gZnJvbSAndmllbSc7XG5leHBvcnQgZnVuY3Rpb24gZ2V0VW5pdCh1bml0KSB7XG4gICAgaWYgKHR5cGVvZiB1bml0ID09PSAnbnVtYmVyJylcbiAgICAgICAgcmV0dXJuIHVuaXQ7XG4gICAgaWYgKHVuaXQgPT09ICd3ZWknKVxuICAgICAgICByZXR1cm4gMDtcbiAgICByZXR1cm4gTWF0aC5hYnMod2VpVW5pdHNbdW5pdF0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0VW5pdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getVersion.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\nconst getVersion = () => `@wagmi/core@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQyx3Q0FBd0MsZ0RBQU8sQ0FBQztBQUN2RCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGdldFZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4uL3ZlcnNpb24uanMnO1xuZXhwb3J0IGNvbnN0IGdldFZlcnNpb24gPSAoKSA9PiBgQHdhZ21pL2NvcmVAJHt2ZXJzaW9ufWA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRWZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/serialize.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys, cutoff) {\n    return keys.slice(0, cutoff).join('.') || '.';\n}\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array, value) {\n    const { length } = array;\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n    return 0;\n}\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(replacer, circularReplacer) {\n    const hasReplacer = typeof replacer === 'function';\n    const hasCircularReplacer = typeof circularReplacer === 'function';\n    const cache = [];\n    const keys = [];\n    return function replace(key, value) {\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                }\n                else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n                keys[keys.length] = key;\n                const valueCutoff = getCutoff(cache, value);\n                if (valueCutoff !== 0) {\n                    return hasCircularReplacer\n                        ? circularReplacer.call(this, key, value, getReferenceKey(keys, valueCutoff))\n                        : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n                }\n            }\n            else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n        }\n        return hasReplacer ? replacer.call(this, key, value) : value;\n    };\n}\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nfunction serialize(value, replacer, indent, circularReplacer) {\n    return JSON.stringify(value, createReplacer((key, value_) => {\n        let value = value_;\n        if (typeof value === 'bigint')\n            value = { __type: 'bigint', value: value_.toString() };\n        if (value instanceof Map)\n            value = { __type: 'Map', value: Array.from(value_.entries()) };\n        return replacer?.(key, value) ?? value;\n    }, circularReplacer), indent ?? undefined);\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js":
/*!********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/uid.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXHVpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzaXplID0gMjU2O1xubGV0IGluZGV4ID0gc2l6ZTtcbmxldCBidWZmZXI7XG5leHBvcnQgZnVuY3Rpb24gdWlkKGxlbmd0aCA9IDExKSB7XG4gICAgaWYgKCFidWZmZXIgfHwgaW5kZXggKyBsZW5ndGggPiBzaXplICogMikge1xuICAgICAgICBidWZmZXIgPSAnJztcbiAgICAgICAgaW5kZXggPSAwO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNpemU7IGkrKykge1xuICAgICAgICAgICAgYnVmZmVyICs9ICgoMjU2ICsgTWF0aC5yYW5kb20oKSAqIDI1NikgfCAwKS50b1N0cmluZygxNikuc3Vic3RyaW5nKDEpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBidWZmZXIuc3Vic3RyaW5nKGluZGV4LCBpbmRleCsrICsgbGVuZ3RoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVpZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/version.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/version.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.17.2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjE3LjInO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\n");

/***/ })

};
;