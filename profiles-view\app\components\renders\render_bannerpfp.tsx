'use client';

import { useState, useEffect, useRef } from 'react';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';
import DecryptedText from '@/components/ui/DecryptedText';

interface BannerPfpMetadata {
  bannerUrl: string;
  bannerScale: number;
  bannerPosition: { x: number; y: number };
  bannerNaturalSize: { width: number; height: number } | null;
  profileUrl: string;
  profileScale: number;
  profilePosition: { x: number; y: number };
  profileNaturalSize: { width: number; height: number } | null;
  profileShape?: string;
  profileHorizontalPosition?: number;
  profileName?: string;
  profileBio?: string;
  profileNameStyle?: {
    fontSize: string;
    fontWeight: string;
    fontColor: string;
    effect: 'none' | 'typewriter' | 'decrypted';
  };
}

interface RenderBannerPfpProps {
  address: string;
  componentData: {
    address: string;
    chain: string;
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    fontColor?: string | null;
  };
  showPositionLabel?: boolean;
  profileName?: string;
  profileBio?: string;
}

export default function RenderBannerPfp({
  address,
  componentData,
  showPositionLabel = false,
  profileName: propProfileName,
  profileBio: propProfileBio
}: RenderBannerPfpProps) {
  const [bannerPfpMetadata, setBannerPfpMetadata] = useState<BannerPfpMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileName, setProfileName] = useState<string | null>(null);
  const [profileBio, setProfileBio] = useState<string | null>(null);
  const [profileHorizontalPosition, setProfileHorizontalPosition] = useState<number>(50);
  const [profileNameHorizontalPosition, setProfileNameHorizontalPosition] = useState<number>(50);
  const [profileShape, setProfileShape] = useState<string>('circular');
  const [profileNameEffect, setProfileNameEffect] = useState<'none' | 'decrypted'>('decrypted');

  const containerRef = useRef<HTMLDivElement>(null);

  const getBorderRadiusClass = () => {
    switch (profileShape) {
      case 'rectangular':
        return 'rounded-none';
      case 'squarish':
        return 'rounded-md';
      case 'circular':
      default:
        return 'rounded-full';
    }
  };

  useEffect(() => {
    const loadBannerPfpData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/bannerpfp/${address}`);

        if (!response.ok) {
          throw new Error('Failed to load banner/profile data');
        }

        const data = await response.json();
        setBannerPfpMetadata(data);

        // Set profile data from API response or props
        setProfileName(data.profileName || propProfileName || address.substring(0, 8));
        setProfileBio(data.profileBio || propProfileBio || '');
        setProfileHorizontalPosition(data.profileHorizontalPosition || 50);
        setProfileNameHorizontalPosition(data.profileNameHorizontalPosition || 50);
        setProfileShape(data.profileShape || 'circular');
        setProfileNameEffect(data.profileNameStyle?.effect || 'decrypted');

      } catch (error) {
        console.error('Error loading banner/profile data:', error);
        setError('Failed to load banner/profile data');
        // Set fallback data
        setProfileName(propProfileName || address.substring(0, 8));
        setProfileBio(propProfileBio || '');
      } finally {
        setIsLoading(false);
      }
    };

    if (address) {
      loadBannerPfpData();
    }
  }, [address, propProfileName, propProfileBio]);

  if (isLoading) {
    return (
      <div className="relative border border-neutral-800 overflow-hidden w-full min-w-full">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </div>
    );
  }

  if (error && !bannerPfpMetadata) {
    return (
      <div className="relative border border-neutral-800 overflow-hidden w-full min-w-full">
        <div className="text-center text-neutral-400 p-8">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative border border-neutral-800 overflow-hidden w-full min-w-full">
      {/* Background color container */}
      <div style={{ backgroundColor: componentData.backgroundColor, width: '100%', minWidth: '100%', boxSizing: 'border-box', paddingBottom: '0.5rem' }} className="w-full min-w-full">
        {/* Container with relative positioning */}
        <div className="relative w-full" style={{
          marginBottom: profileShape === 'rectangular' ? '9rem' : '8rem',
          width: '100%'
        }}>
          {/* Banner container */}
          <div
            className="w-full h-48 md:h-64 relative overflow-hidden"
            ref={containerRef}
            style={{ width: '100%', minWidth: '100%' }}
          >
            {bannerPfpMetadata?.bannerUrl && (
              <div
                className="absolute inset-0"
                style={{
                  backgroundImage: `url(${bannerPfpMetadata.bannerUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  transform: `translate(${bannerPfpMetadata.bannerPosition?.x || 0}px, ${bannerPfpMetadata.bannerPosition?.y || 0}px) scale(${bannerPfpMetadata.bannerScale || 1})`,
                  transformOrigin: 'center',
                }}
              />
            )}
          </div>

          {/* Profile picture positioned absolutely */}
          <div
            className="absolute flex justify-center"
            style={{
              bottom: profileShape === 'rectangular' ? '-4.5rem' : '-4rem',
              left: `${profileHorizontalPosition}%`,
              transform: 'translateX(-50%)',
              zIndex: 10
            }}
          >
            <div className={`${profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative`}>
              {bannerPfpMetadata?.profileUrl ? (
                <img
                  src={bannerPfpMetadata.profileUrl}
                  alt="Profile"
                  className="w-full h-full object-cover"
                  style={{
                    transform: `scale(${bannerPfpMetadata.profileScale || 1})`,
                    transformOrigin: 'center',
                  }}
                />
              ) : (
                <div className="w-full h-full bg-neutral-800 flex items-center justify-center">
                  <span className="text-neutral-400 text-xs">No Image</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Profile name positioned below the profile picture */}
        <div
          className="flex justify-center w-full"
          style={{
            left: `${profileNameHorizontalPosition}%`,
            transform: 'translateX(-50%)',
            position: 'relative'
          }}
        >
          <div className="text-center px-4">
            {profileName && (
              <h2
                className="text-xl font-bold mb-2"
                style={{ color: componentData.fontColor || '#ffffff' }}
              >
                {profileNameEffect === 'decrypted' ? (
                  <DecryptedText
                    text={profileName}
                    animateOn="view"
                    className="font-bold"
                  />
                ) : (
                  profileName
                )}
              </h2>
            )}
            {profileBio && (
              <p
                className="text-sm text-neutral-300"
                style={{ color: componentData.fontColor || '#ffffff' }}
              >
                {profileBio}
              </p>
            )}
          </div>
        </div>
      </div>

      {showPositionLabel && (
        <div className="absolute top-2 right-2 bg-purple-900/30 text-purple-400 px-2 py-1 rounded-full text-xs font-medium">
          Banner/PFP
        </div>
      )}
    </div>
  );
}
