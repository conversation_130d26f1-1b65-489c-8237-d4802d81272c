"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forma = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.forma = (0, defineChain_js_1.defineChain)({
    id: 984122,
    name: 'Forma',
    network: 'forma',
    nativeCurrency: {
        symbol: 'TIA',
        name: 'TIA',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.forma.art'],
            webSocket: ['wss://ws.forma.art'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Forma Explorer',
            url: 'https://explorer.forma.art',
        },
    },
    contracts: {
        multicall3: {
            address: '0xd53C6FFB123F7349A32980F87faeD8FfDc9ef079',
            blockCreated: 252705,
        },
    },
});
//# sourceMappingURL=forma.js.map