"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ox";
exports.ids = ["vendor-chunks/ox"];
exports.modules = {

/***/ "(ssr)/./node_modules/ox/_cjs/core/AbiConstructor.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_cjs/core/AbiConstructor.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.decode = decode;\nexports.encode = encode;\nexports.format = format;\nexports.from = from;\nexports.fromAbi = fromAbi;\nconst abitype = __webpack_require__(/*! abitype */ \"(ssr)/./node_modules/abitype/dist/cjs/exports/index.js\");\nconst AbiItem = __webpack_require__(/*! ./AbiItem.js */ \"(ssr)/./node_modules/ox/_cjs/core/AbiItem.js\");\nconst AbiParameters = __webpack_require__(/*! ./AbiParameters.js */ \"(ssr)/./node_modules/ox/_cjs/core/AbiParameters.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nfunction decode(abiConstructor, options) {\n    const { bytecode } = options;\n    if (abiConstructor.inputs.length === 0)\n        return undefined;\n    const data = options.data.replace(bytecode, '0x');\n    return AbiParameters.decode(abiConstructor.inputs, data);\n}\nfunction encode(abiConstructor, options) {\n    const { bytecode, args } = options;\n    return Hex.concat(bytecode, abiConstructor.inputs?.length && args?.length\n        ? AbiParameters.encode(abiConstructor.inputs, args)\n        : '0x');\n}\nfunction format(abiConstructor) {\n    return abitype.formatAbiItem(abiConstructor);\n}\nfunction from(abiConstructor) {\n    return AbiItem.from(abiConstructor);\n}\nfunction fromAbi(abi) {\n    const item = abi.find((item) => item.type === 'constructor');\n    if (!item)\n        throw new AbiItem.NotFoundError({ name: 'constructor' });\n    return item;\n}\n//# sourceMappingURL=AbiConstructor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/AbiConstructor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/AbiFunction.js":
/*!**************************************************!*\
  !*** ./node_modules/ox/_cjs/core/AbiFunction.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.decodeData = decodeData;\nexports.decodeResult = decodeResult;\nexports.encodeData = encodeData;\nexports.encodeResult = encodeResult;\nexports.format = format;\nexports.from = from;\nexports.fromAbi = fromAbi;\nexports.getSelector = getSelector;\nconst abitype = __webpack_require__(/*! abitype */ \"(ssr)/./node_modules/abitype/dist/cjs/exports/index.js\");\nconst AbiItem = __webpack_require__(/*! ./AbiItem.js */ \"(ssr)/./node_modules/ox/_cjs/core/AbiItem.js\");\nconst AbiParameters = __webpack_require__(/*! ./AbiParameters.js */ \"(ssr)/./node_modules/ox/_cjs/core/AbiParameters.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nfunction decodeData(abiFunction, data) {\n    const { overloads } = abiFunction;\n    if (Hex.size(data) < 4)\n        throw new AbiItem.InvalidSelectorSizeError({ data });\n    if (abiFunction.inputs.length === 0)\n        return undefined;\n    const item = overloads\n        ? fromAbi([abiFunction, ...overloads], data)\n        : abiFunction;\n    if (Hex.size(data) <= 4)\n        return undefined;\n    return AbiParameters.decode(item.inputs, Hex.slice(data, 4));\n}\nfunction decodeResult(abiFunction, data, options = {}) {\n    const values = AbiParameters.decode(abiFunction.outputs, data, options);\n    if (values && Object.keys(values).length === 0)\n        return undefined;\n    if (values && Object.keys(values).length === 1) {\n        if (Array.isArray(values))\n            return values[0];\n        return Object.values(values)[0];\n    }\n    return values;\n}\nfunction encodeData(abiFunction, ...args) {\n    const { overloads } = abiFunction;\n    const item = overloads\n        ? fromAbi([abiFunction, ...overloads], abiFunction.name, {\n            args: args[0],\n        })\n        : abiFunction;\n    const selector = getSelector(item);\n    const data = args.length > 0\n        ? AbiParameters.encode(item.inputs, args[0])\n        : undefined;\n    return data ? Hex.concat(selector, data) : selector;\n}\nfunction encodeResult(abiFunction, output, options = {}) {\n    const { as = 'Array' } = options;\n    const values = (() => {\n        if (abiFunction.outputs.length === 1)\n            return [output];\n        if (Array.isArray(output))\n            return output;\n        if (as === 'Object')\n            return Object.values(output);\n        return [output];\n    })();\n    return AbiParameters.encode(abiFunction.outputs, values);\n}\nfunction format(abiFunction) {\n    return abitype.formatAbiItem(abiFunction);\n}\nfunction from(abiFunction, options = {}) {\n    return AbiItem.from(abiFunction, options);\n}\nfunction fromAbi(abi, name, options) {\n    const item = AbiItem.fromAbi(abi, name, options);\n    if (item.type !== 'function')\n        throw new AbiItem.NotFoundError({ name, type: 'function' });\n    return item;\n}\nfunction getSelector(abiItem) {\n    return AbiItem.getSelector(abiItem);\n}\n//# sourceMappingURL=AbiFunction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/AbiFunction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/AbiItem.js":
/*!**********************************************!*\
  !*** ./node_modules/ox/_cjs/core/AbiItem.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidSelectorSizeError = exports.NotFoundError = exports.AmbiguityError = void 0;\nexports.format = format;\nexports.from = from;\nexports.fromAbi = fromAbi;\nexports.getSelector = getSelector;\nexports.getSignature = getSignature;\nexports.getSignatureHash = getSignatureHash;\nconst abitype = __webpack_require__(/*! abitype */ \"(ssr)/./node_modules/abitype/dist/cjs/exports/index.js\");\nconst Errors = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Hash = __webpack_require__(/*! ./Hash.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hash.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nconst internal = __webpack_require__(/*! ./internal/abiItem.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/abiItem.js\");\nfunction format(abiItem) {\n    return abitype.formatAbiItem(abiItem);\n}\nfunction from(abiItem, options = {}) {\n    const { prepare = true } = options;\n    const item = (() => {\n        if (Array.isArray(abiItem))\n            return abitype.parseAbiItem(abiItem);\n        if (typeof abiItem === 'string')\n            return abitype.parseAbiItem(abiItem);\n        return abiItem;\n    })();\n    return {\n        ...item,\n        ...(prepare ? { hash: getSignatureHash(item) } : {}),\n    };\n}\nfunction fromAbi(abi, name, options) {\n    const { args = [], prepare = true } = (options ??\n        {});\n    const isSelector = Hex.validate(name, { strict: false });\n    const abiItems = abi.filter((abiItem) => {\n        if (isSelector) {\n            if (abiItem.type === 'function' || abiItem.type === 'error')\n                return getSelector(abiItem) === Hex.slice(name, 0, 4);\n            if (abiItem.type === 'event')\n                return getSignatureHash(abiItem) === name;\n            return false;\n        }\n        return 'name' in abiItem && abiItem.name === name;\n    });\n    if (abiItems.length === 0)\n        throw new NotFoundError({ name: name });\n    if (abiItems.length === 1)\n        return {\n            ...abiItems[0],\n            ...(prepare ? { hash: getSignatureHash(abiItems[0]) } : {}),\n        };\n    let matchedAbiItem = undefined;\n    for (const abiItem of abiItems) {\n        if (!('inputs' in abiItem))\n            continue;\n        if (!args || args.length === 0) {\n            if (!abiItem.inputs || abiItem.inputs.length === 0)\n                return {\n                    ...abiItem,\n                    ...(prepare ? { hash: getSignatureHash(abiItem) } : {}),\n                };\n            continue;\n        }\n        if (!abiItem.inputs)\n            continue;\n        if (abiItem.inputs.length === 0)\n            continue;\n        if (abiItem.inputs.length !== args.length)\n            continue;\n        const matched = args.every((arg, index) => {\n            const abiParameter = 'inputs' in abiItem && abiItem.inputs[index];\n            if (!abiParameter)\n                return false;\n            return internal.isArgOfType(arg, abiParameter);\n        });\n        if (matched) {\n            if (matchedAbiItem &&\n                'inputs' in matchedAbiItem &&\n                matchedAbiItem.inputs) {\n                const ambiguousTypes = internal.getAmbiguousTypes(abiItem.inputs, matchedAbiItem.inputs, args);\n                if (ambiguousTypes)\n                    throw new AmbiguityError({\n                        abiItem,\n                        type: ambiguousTypes[0],\n                    }, {\n                        abiItem: matchedAbiItem,\n                        type: ambiguousTypes[1],\n                    });\n            }\n            matchedAbiItem = abiItem;\n        }\n    }\n    const abiItem = (() => {\n        if (matchedAbiItem)\n            return matchedAbiItem;\n        const [abiItem, ...overloads] = abiItems;\n        return { ...abiItem, overloads };\n    })();\n    if (!abiItem)\n        throw new NotFoundError({ name: name });\n    return {\n        ...abiItem,\n        ...(prepare ? { hash: getSignatureHash(abiItem) } : {}),\n    };\n}\nfunction getSelector(abiItem) {\n    return Hex.slice(getSignatureHash(abiItem), 0, 4);\n}\nfunction getSignature(abiItem) {\n    const signature = (() => {\n        if (typeof abiItem === 'string')\n            return abiItem;\n        return abitype.formatAbiItem(abiItem);\n    })();\n    return internal.normalizeSignature(signature);\n}\nfunction getSignatureHash(abiItem) {\n    if (typeof abiItem !== 'string' && 'hash' in abiItem && abiItem.hash)\n        return abiItem.hash;\n    return Hash.keccak256(Hex.fromString(getSignature(abiItem)));\n}\nclass AmbiguityError extends Errors.BaseError {\n    constructor(x, y) {\n        super('Found ambiguous types in overloaded ABI Items.', {\n            metaMessages: [\n                `\\`${x.type}\\` in \\`${internal.normalizeSignature(abitype.formatAbiItem(x.abiItem))}\\`, and`,\n                `\\`${y.type}\\` in \\`${internal.normalizeSignature(abitype.formatAbiItem(y.abiItem))}\\``,\n                '',\n                'These types encode differently and cannot be distinguished at runtime.',\n                'Remove one of the ambiguous items in the ABI.',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiItem.AmbiguityError'\n        });\n    }\n}\nexports.AmbiguityError = AmbiguityError;\nclass NotFoundError extends Errors.BaseError {\n    constructor({ name, data, type = 'item', }) {\n        const selector = (() => {\n            if (name)\n                return ` with name \"${name}\"`;\n            if (data)\n                return ` with data \"${data}\"`;\n            return '';\n        })();\n        super(`ABI ${type}${selector} not found.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiItem.NotFoundError'\n        });\n    }\n}\nexports.NotFoundError = NotFoundError;\nclass InvalidSelectorSizeError extends Errors.BaseError {\n    constructor({ data }) {\n        super(`Selector size is invalid. Expected 4 bytes. Received ${Hex.size(data)} bytes (\"${data}\").`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiItem.InvalidSelectorSizeError'\n        });\n    }\n}\nexports.InvalidSelectorSizeError = InvalidSelectorSizeError;\n//# sourceMappingURL=AbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/AbiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/AbiParameters.js":
/*!****************************************************!*\
  !*** ./node_modules/ox/_cjs/core/AbiParameters.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidTypeError = exports.InvalidArrayError = exports.LengthMismatchError = exports.BytesSizeMismatchError = exports.ArrayLengthMismatchError = exports.ZeroDataError = exports.DataSizeTooSmallError = void 0;\nexports.decode = decode;\nexports.encode = encode;\nexports.encodePacked = encodePacked;\nexports.format = format;\nexports.from = from;\nconst abitype = __webpack_require__(/*! abitype */ \"(ssr)/./node_modules/abitype/dist/cjs/exports/index.js\");\nconst Address = __webpack_require__(/*! ./Address.js */ \"(ssr)/./node_modules/ox/_cjs/core/Address.js\");\nconst Bytes = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nconst Errors = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nconst Solidity = __webpack_require__(/*! ./Solidity.js */ \"(ssr)/./node_modules/ox/_cjs/core/Solidity.js\");\nconst internal = __webpack_require__(/*! ./internal/abiParameters.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/abiParameters.js\");\nconst Cursor = __webpack_require__(/*! ./internal/cursor.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/cursor.js\");\nfunction decode(parameters, data, options = {}) {\n    const { as = 'Array', checksumAddress = false } = options;\n    const bytes = typeof data === 'string' ? Bytes.fromHex(data) : data;\n    const cursor = Cursor.create(bytes);\n    if (Bytes.size(bytes) === 0 && parameters.length > 0)\n        throw new ZeroDataError();\n    if (Bytes.size(bytes) && Bytes.size(bytes) < 32)\n        throw new DataSizeTooSmallError({\n            data: typeof data === 'string' ? data : Hex.fromBytes(data),\n            parameters: parameters,\n            size: Bytes.size(bytes),\n        });\n    let consumed = 0;\n    const values = as === 'Array' ? [] : {};\n    for (let i = 0; i < parameters.length; ++i) {\n        const param = parameters[i];\n        cursor.setPosition(consumed);\n        const [data, consumed_] = internal.decodeParameter(cursor, param, {\n            checksumAddress,\n            staticPosition: 0,\n        });\n        consumed += consumed_;\n        if (as === 'Array')\n            values.push(data);\n        else\n            values[param.name ?? i] = data;\n    }\n    return values;\n}\nfunction encode(parameters, values, options) {\n    const { checksumAddress = false } = options ?? {};\n    if (parameters.length !== values.length)\n        throw new LengthMismatchError({\n            expectedLength: parameters.length,\n            givenLength: values.length,\n        });\n    const preparedParameters = internal.prepareParameters({\n        checksumAddress,\n        parameters: parameters,\n        values: values,\n    });\n    const data = internal.encode(preparedParameters);\n    if (data.length === 0)\n        return '0x';\n    return data;\n}\nfunction encodePacked(types, values) {\n    if (types.length !== values.length)\n        throw new LengthMismatchError({\n            expectedLength: types.length,\n            givenLength: values.length,\n        });\n    const data = [];\n    for (let i = 0; i < types.length; i++) {\n        const type = types[i];\n        const value = values[i];\n        data.push(encodePacked.encode(type, value));\n    }\n    return Hex.concat(...data);\n}\n(function (encodePacked) {\n    function encode(type, value, isArray = false) {\n        if (type === 'address') {\n            const address = value;\n            Address.assert(address);\n            return Hex.padLeft(address.toLowerCase(), isArray ? 32 : 0);\n        }\n        if (type === 'string')\n            return Hex.fromString(value);\n        if (type === 'bytes')\n            return value;\n        if (type === 'bool')\n            return Hex.padLeft(Hex.fromBoolean(value), isArray ? 32 : 1);\n        const intMatch = type.match(Solidity.integerRegex);\n        if (intMatch) {\n            const [_type, baseType, bits = '256'] = intMatch;\n            const size = Number.parseInt(bits) / 8;\n            return Hex.fromNumber(value, {\n                size: isArray ? 32 : size,\n                signed: baseType === 'int',\n            });\n        }\n        const bytesMatch = type.match(Solidity.bytesRegex);\n        if (bytesMatch) {\n            const [_type, size] = bytesMatch;\n            if (Number.parseInt(size) !== (value.length - 2) / 2)\n                throw new BytesSizeMismatchError({\n                    expectedSize: Number.parseInt(size),\n                    value: value,\n                });\n            return Hex.padRight(value, isArray ? 32 : 0);\n        }\n        const arrayMatch = type.match(Solidity.arrayRegex);\n        if (arrayMatch && Array.isArray(value)) {\n            const [_type, childType] = arrayMatch;\n            const data = [];\n            for (let i = 0; i < value.length; i++) {\n                data.push(encode(childType, value[i], true));\n            }\n            if (data.length === 0)\n                return '0x';\n            return Hex.concat(...data);\n        }\n        throw new InvalidTypeError(type);\n    }\n    encodePacked.encode = encode;\n})(encodePacked || (exports.encodePacked = encodePacked = {}));\nfunction format(parameters) {\n    return abitype.formatAbiParameters(parameters);\n}\nfunction from(parameters) {\n    if (Array.isArray(parameters) && typeof parameters[0] === 'string')\n        return abitype.parseAbiParameters(parameters);\n    if (typeof parameters === 'string')\n        return abitype.parseAbiParameters(parameters);\n    return parameters;\n}\nclass DataSizeTooSmallError extends Errors.BaseError {\n    constructor({ data, parameters, size, }) {\n        super(`Data size of ${size} bytes is too small for given parameters.`, {\n            metaMessages: [\n                `Params: (${abitype.formatAbiParameters(parameters)})`,\n                `Data:   ${data} (${size} bytes)`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.DataSizeTooSmallError'\n        });\n    }\n}\nexports.DataSizeTooSmallError = DataSizeTooSmallError;\nclass ZeroDataError extends Errors.BaseError {\n    constructor() {\n        super('Cannot decode zero data (\"0x\") with ABI parameters.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.ZeroDataError'\n        });\n    }\n}\nexports.ZeroDataError = ZeroDataError;\nclass ArrayLengthMismatchError extends Errors.BaseError {\n    constructor({ expectedLength, givenLength, type, }) {\n        super(`Array length mismatch for type \\`${type}\\`. Expected: \\`${expectedLength}\\`. Given: \\`${givenLength}\\`.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.ArrayLengthMismatchError'\n        });\n    }\n}\nexports.ArrayLengthMismatchError = ArrayLengthMismatchError;\nclass BytesSizeMismatchError extends Errors.BaseError {\n    constructor({ expectedSize, value, }) {\n        super(`Size of bytes \"${value}\" (bytes${Hex.size(value)}) does not match expected size (bytes${expectedSize}).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.BytesSizeMismatchError'\n        });\n    }\n}\nexports.BytesSizeMismatchError = BytesSizeMismatchError;\nclass LengthMismatchError extends Errors.BaseError {\n    constructor({ expectedLength, givenLength, }) {\n        super([\n            'ABI encoding parameters/values length mismatch.',\n            `Expected length (parameters): ${expectedLength}`,\n            `Given length (values): ${givenLength}`,\n        ].join('\\n'));\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.LengthMismatchError'\n        });\n    }\n}\nexports.LengthMismatchError = LengthMismatchError;\nclass InvalidArrayError extends Errors.BaseError {\n    constructor(value) {\n        super(`Value \\`${value}\\` is not a valid array.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.InvalidArrayError'\n        });\n    }\n}\nexports.InvalidArrayError = InvalidArrayError;\nclass InvalidTypeError extends Errors.BaseError {\n    constructor(type) {\n        super(`Type \\`${type}\\` is not a valid ABI Type.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiParameters.InvalidTypeError'\n        });\n    }\n}\nexports.InvalidTypeError = InvalidTypeError;\n//# sourceMappingURL=AbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/AbiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Address.js":
/*!**********************************************!*\
  !*** ./node_modules/ox/_cjs/core/Address.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidChecksumError = exports.InvalidInputError = exports.InvalidAddressError = void 0;\nexports.assert = assert;\nexports.checksum = checksum;\nexports.from = from;\nexports.fromPublicKey = fromPublicKey;\nexports.isEqual = isEqual;\nexports.validate = validate;\nconst Bytes = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nconst Caches = __webpack_require__(/*! ./Caches.js */ \"(ssr)/./node_modules/ox/_cjs/core/Caches.js\");\nconst Errors = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Hash = __webpack_require__(/*! ./Hash.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hash.js\");\nconst PublicKey = __webpack_require__(/*! ./PublicKey.js */ \"(ssr)/./node_modules/ox/_cjs/core/PublicKey.js\");\nconst addressRegex = /^0x[a-fA-F0-9]{40}$/;\nfunction assert(value, options = {}) {\n    const { strict = true } = options;\n    if (!addressRegex.test(value))\n        throw new InvalidAddressError({\n            address: value,\n            cause: new InvalidInputError(),\n        });\n    if (strict) {\n        if (value.toLowerCase() === value)\n            return;\n        if (checksum(value) !== value)\n            throw new InvalidAddressError({\n                address: value,\n                cause: new InvalidChecksumError(),\n            });\n    }\n}\nfunction checksum(address) {\n    if (Caches.checksum.has(address))\n        return Caches.checksum.get(address);\n    assert(address, { strict: false });\n    const hexAddress = address.substring(2).toLowerCase();\n    const hash = Hash.keccak256(Bytes.fromString(hexAddress), { as: 'Bytes' });\n    const characters = hexAddress.split('');\n    for (let i = 0; i < 40; i += 2) {\n        if (hash[i >> 1] >> 4 >= 8 && characters[i]) {\n            characters[i] = characters[i].toUpperCase();\n        }\n        if ((hash[i >> 1] & 0x0f) >= 8 && characters[i + 1]) {\n            characters[i + 1] = characters[i + 1].toUpperCase();\n        }\n    }\n    const result = `0x${characters.join('')}`;\n    Caches.checksum.set(address, result);\n    return result;\n}\nfunction from(address, options = {}) {\n    const { checksum: checksumVal = false } = options;\n    assert(address);\n    if (checksumVal)\n        return checksum(address);\n    return address;\n}\nfunction fromPublicKey(publicKey, options = {}) {\n    const address = Hash.keccak256(`0x${PublicKey.toHex(publicKey).slice(4)}`).substring(26);\n    return from(`0x${address}`, options);\n}\nfunction isEqual(addressA, addressB) {\n    assert(addressA, { strict: false });\n    assert(addressB, { strict: false });\n    return addressA.toLowerCase() === addressB.toLowerCase();\n}\nfunction validate(address, options = {}) {\n    const { strict = true } = options ?? {};\n    try {\n        assert(address, { strict });\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nclass InvalidAddressError extends Errors.BaseError {\n    constructor({ address, cause }) {\n        super(`Address \"${address}\" is invalid.`, {\n            cause,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Address.InvalidAddressError'\n        });\n    }\n}\nexports.InvalidAddressError = InvalidAddressError;\nclass InvalidInputError extends Errors.BaseError {\n    constructor() {\n        super('Address is not a 20 byte (40 hexadecimal character) value.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Address.InvalidInputError'\n        });\n    }\n}\nexports.InvalidInputError = InvalidInputError;\nclass InvalidChecksumError extends Errors.BaseError {\n    constructor() {\n        super('Address does not match its checksum counterpart.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Address.InvalidChecksumError'\n        });\n    }\n}\nexports.InvalidChecksumError = InvalidChecksumError;\n//# sourceMappingURL=Address.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Address.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/BlockOverrides.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_cjs/core/BlockOverrides.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fromRpc = fromRpc;\nexports.toRpc = toRpc;\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nconst Withdrawal = __webpack_require__(/*! ./Withdrawal.js */ \"(ssr)/./node_modules/ox/_cjs/core/Withdrawal.js\");\nfunction fromRpc(rpcBlockOverrides) {\n    return {\n        ...(rpcBlockOverrides.baseFeePerGas && {\n            baseFeePerGas: BigInt(rpcBlockOverrides.baseFeePerGas),\n        }),\n        ...(rpcBlockOverrides.blobBaseFee && {\n            blobBaseFee: BigInt(rpcBlockOverrides.blobBaseFee),\n        }),\n        ...(rpcBlockOverrides.feeRecipient && {\n            feeRecipient: rpcBlockOverrides.feeRecipient,\n        }),\n        ...(rpcBlockOverrides.gasLimit && {\n            gasLimit: BigInt(rpcBlockOverrides.gasLimit),\n        }),\n        ...(rpcBlockOverrides.number && {\n            number: BigInt(rpcBlockOverrides.number),\n        }),\n        ...(rpcBlockOverrides.prevRandao && {\n            prevRandao: BigInt(rpcBlockOverrides.prevRandao),\n        }),\n        ...(rpcBlockOverrides.time && {\n            time: BigInt(rpcBlockOverrides.time),\n        }),\n        ...(rpcBlockOverrides.withdrawals && {\n            withdrawals: rpcBlockOverrides.withdrawals.map(Withdrawal.fromRpc),\n        }),\n    };\n}\nfunction toRpc(blockOverrides) {\n    return {\n        ...(typeof blockOverrides.baseFeePerGas === 'bigint' && {\n            baseFeePerGas: Hex.fromNumber(blockOverrides.baseFeePerGas),\n        }),\n        ...(typeof blockOverrides.blobBaseFee === 'bigint' && {\n            blobBaseFee: Hex.fromNumber(blockOverrides.blobBaseFee),\n        }),\n        ...(typeof blockOverrides.feeRecipient === 'string' && {\n            feeRecipient: blockOverrides.feeRecipient,\n        }),\n        ...(typeof blockOverrides.gasLimit === 'bigint' && {\n            gasLimit: Hex.fromNumber(blockOverrides.gasLimit),\n        }),\n        ...(typeof blockOverrides.number === 'bigint' && {\n            number: Hex.fromNumber(blockOverrides.number),\n        }),\n        ...(typeof blockOverrides.prevRandao === 'bigint' && {\n            prevRandao: Hex.fromNumber(blockOverrides.prevRandao),\n        }),\n        ...(typeof blockOverrides.time === 'bigint' && {\n            time: Hex.fromNumber(blockOverrides.time),\n        }),\n        ...(blockOverrides.withdrawals && {\n            withdrawals: blockOverrides.withdrawals.map(Withdrawal.toRpc),\n        }),\n    };\n}\n//# sourceMappingURL=BlockOverrides.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/BlockOverrides.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Bytes.js":
/*!********************************************!*\
  !*** ./node_modules/ox/_cjs/core/Bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SizeExceedsPaddingSizeError = exports.SliceOffsetOutOfBoundsError = exports.SizeOverflowError = exports.InvalidBytesTypeError = exports.InvalidBytesBooleanError = void 0;\nexports.assert = assert;\nexports.concat = concat;\nexports.from = from;\nexports.fromArray = fromArray;\nexports.fromBoolean = fromBoolean;\nexports.fromHex = fromHex;\nexports.fromNumber = fromNumber;\nexports.fromString = fromString;\nexports.isEqual = isEqual;\nexports.padLeft = padLeft;\nexports.padRight = padRight;\nexports.random = random;\nexports.size = size;\nexports.slice = slice;\nexports.toBigInt = toBigInt;\nexports.toBoolean = toBoolean;\nexports.toHex = toHex;\nexports.toNumber = toNumber;\nexports.toString = toString;\nexports.trimLeft = trimLeft;\nexports.trimRight = trimRight;\nexports.validate = validate;\nconst utils_1 = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/@noble/curves/abstract/utils.js\");\nconst Errors = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nconst Json = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_cjs/core/Json.js\");\nconst internal = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/bytes.js\");\nconst internal_hex = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/hex.js\");\nconst decoder = new TextDecoder();\nconst encoder = new TextEncoder();\nfunction assert(value) {\n    if (value instanceof Uint8Array)\n        return;\n    if (!value)\n        throw new InvalidBytesTypeError(value);\n    if (typeof value !== 'object')\n        throw new InvalidBytesTypeError(value);\n    if (!('BYTES_PER_ELEMENT' in value))\n        throw new InvalidBytesTypeError(value);\n    if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n        throw new InvalidBytesTypeError(value);\n}\nfunction concat(...values) {\n    let length = 0;\n    for (const arr of values) {\n        length += arr.length;\n    }\n    const result = new Uint8Array(length);\n    for (let i = 0, index = 0; i < values.length; i++) {\n        const arr = values[i];\n        result.set(arr, index);\n        index += arr.length;\n    }\n    return result;\n}\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return value;\n    if (typeof value === 'string')\n        return fromHex(value);\n    return fromArray(value);\n}\nfunction fromArray(value) {\n    return value instanceof Uint8Array ? value : new Uint8Array(value);\n}\nfunction fromBoolean(value, options = {}) {\n    const { size } = options;\n    const bytes = new Uint8Array(1);\n    bytes[0] = Number(value);\n    if (typeof size === 'number') {\n        internal.assertSize(bytes, size);\n        return padLeft(bytes, size);\n    }\n    return bytes;\n}\nfunction fromHex(value, options = {}) {\n    const { size } = options;\n    let hex = value;\n    if (size) {\n        internal_hex.assertSize(value, size);\n        hex = Hex.padRight(value, size);\n    }\n    let hexString = hex.slice(2);\n    if (hexString.length % 2)\n        hexString = `0${hexString}`;\n    const length = hexString.length / 2;\n    const bytes = new Uint8Array(length);\n    for (let index = 0, j = 0; index < length; index++) {\n        const nibbleLeft = internal.charCodeToBase16(hexString.charCodeAt(j++));\n        const nibbleRight = internal.charCodeToBase16(hexString.charCodeAt(j++));\n        if (nibbleLeft === undefined || nibbleRight === undefined) {\n            throw new Errors.BaseError(`Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`);\n        }\n        bytes[index] = nibbleLeft * 16 + nibbleRight;\n    }\n    return bytes;\n}\nfunction fromNumber(value, options) {\n    const hex = Hex.fromNumber(value, options);\n    return fromHex(hex);\n}\nfunction fromString(value, options = {}) {\n    const { size } = options;\n    const bytes = encoder.encode(value);\n    if (typeof size === 'number') {\n        internal.assertSize(bytes, size);\n        return padRight(bytes, size);\n    }\n    return bytes;\n}\nfunction isEqual(bytesA, bytesB) {\n    return (0, utils_1.equalBytes)(bytesA, bytesB);\n}\nfunction padLeft(value, size) {\n    return internal.pad(value, { dir: 'left', size });\n}\nfunction padRight(value, size) {\n    return internal.pad(value, { dir: 'right', size });\n}\nfunction random(length) {\n    return crypto.getRandomValues(new Uint8Array(length));\n}\nfunction size(value) {\n    return value.length;\n}\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    internal.assertStartOffset(value, start);\n    const value_ = value.slice(start, end);\n    if (strict)\n        internal.assertEndOffset(value_, start, end);\n    return value_;\n}\nfunction toBigInt(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        internal.assertSize(bytes, size);\n    const hex = Hex.fromBytes(bytes, options);\n    return Hex.toBigInt(hex, options);\n}\nfunction toBoolean(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        internal.assertSize(bytes_, size);\n        bytes_ = trimLeft(bytes_);\n    }\n    if (bytes_.length > 1 || bytes_[0] > 1)\n        throw new InvalidBytesBooleanError(bytes_);\n    return Boolean(bytes_[0]);\n}\nfunction toHex(value, options = {}) {\n    return Hex.fromBytes(value, options);\n}\nfunction toNumber(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        internal.assertSize(bytes, size);\n    const hex = Hex.fromBytes(bytes, options);\n    return Hex.toNumber(hex, options);\n}\nfunction toString(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        internal.assertSize(bytes_, size);\n        bytes_ = trimRight(bytes_);\n    }\n    return decoder.decode(bytes_);\n}\nfunction trimLeft(value) {\n    return internal.trim(value, { dir: 'left' });\n}\nfunction trimRight(value) {\n    return internal.trim(value, { dir: 'right' });\n}\nfunction validate(value) {\n    try {\n        assert(value);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nclass InvalidBytesBooleanError extends Errors.BaseError {\n    constructor(bytes) {\n        super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The bytes array must contain a single byte of either a `0` or `1` value.',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesBooleanError'\n        });\n    }\n}\nexports.InvalidBytesBooleanError = InvalidBytesBooleanError;\nclass InvalidBytesTypeError extends Errors.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`, {\n            metaMessages: ['Bytes values must be of type `Bytes`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesTypeError'\n        });\n    }\n}\nexports.InvalidBytesTypeError = InvalidBytesTypeError;\nclass SizeOverflowError extends Errors.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeOverflowError'\n        });\n    }\n}\nexports.SizeOverflowError = SizeOverflowError;\nclass SliceOffsetOutOfBoundsError extends Errors.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\nexports.SliceOffsetOutOfBoundsError = SliceOffsetOutOfBoundsError;\nclass SizeExceedsPaddingSizeError extends Errors.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeExceedsPaddingSizeError'\n        });\n    }\n}\nexports.SizeExceedsPaddingSizeError = SizeExceedsPaddingSizeError;\n//# sourceMappingURL=Bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Caches.js":
/*!*********************************************!*\
  !*** ./node_modules/ox/_cjs/core/Caches.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.checksum = void 0;\nexports.clear = clear;\nconst lru_js_1 = __webpack_require__(/*! ./internal/lru.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/lru.js\");\nconst caches = {\n    checksum: new lru_js_1.LruMap(8192),\n};\nexports.checksum = caches.checksum;\nfunction clear() {\n    for (const cache of Object.values(caches))\n        cache.clear();\n}\n//# sourceMappingURL=Caches.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL0NhY2hlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0I7QUFDaEIsYUFBYTtBQUNiLGlCQUFpQixtQkFBTyxDQUFDLDRFQUFtQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxveFxcX2Nqc1xcY29yZVxcQ2FjaGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jaGVja3N1bSA9IHZvaWQgMDtcbmV4cG9ydHMuY2xlYXIgPSBjbGVhcjtcbmNvbnN0IGxydV9qc18xID0gcmVxdWlyZShcIi4vaW50ZXJuYWwvbHJ1LmpzXCIpO1xuY29uc3QgY2FjaGVzID0ge1xuICAgIGNoZWNrc3VtOiBuZXcgbHJ1X2pzXzEuTHJ1TWFwKDgxOTIpLFxufTtcbmV4cG9ydHMuY2hlY2tzdW0gPSBjYWNoZXMuY2hlY2tzdW07XG5mdW5jdGlvbiBjbGVhcigpIHtcbiAgICBmb3IgKGNvbnN0IGNhY2hlIG9mIE9iamVjdC52YWx1ZXMoY2FjaGVzKSlcbiAgICAgICAgY2FjaGUuY2xlYXIoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUNhY2hlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Caches.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Errors.js":
/*!*********************************************!*\
  !*** ./node_modules/ox/_cjs/core/Errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ./internal/errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/errors.js\");\nclass BaseError extends Error {\n    constructor(shortMessage, options = {}) {\n        const details = (() => {\n            if (options.cause instanceof BaseError) {\n                if (options.cause.details)\n                    return options.cause.details;\n                if (options.cause.shortMessage)\n                    return options.cause.shortMessage;\n            }\n            if (options.cause &&\n                'details' in options.cause &&\n                typeof options.cause.details === 'string')\n                return options.cause.details;\n            if (options.cause?.message)\n                return options.cause.message;\n            return options.details;\n        })();\n        const docsPath = (() => {\n            if (options.cause instanceof BaseError)\n                return options.cause.docsPath || options.docsPath;\n            return options.docsPath;\n        })();\n        const docsBaseUrl = 'https://oxlib.sh';\n        const docs = `${docsBaseUrl}${docsPath ?? ''}`;\n        const message = [\n            shortMessage || 'An error occurred.',\n            ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n            ...(details || docsPath\n                ? [\n                    '',\n                    details ? `Details: ${details}` : undefined,\n                    docsPath ? `See: ${docs}` : undefined,\n                ]\n                : []),\n        ]\n            .filter((x) => typeof x === 'string')\n            .join('\\n');\n        super(message, options.cause ? { cause: options.cause } : undefined);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"cause\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'BaseError'\n        });\n        Object.defineProperty(this, \"version\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: `ox@${(0, errors_js_1.getVersion)()}`\n        });\n        this.cause = options.cause;\n        this.details = details;\n        this.docs = docs;\n        this.docsPath = docsPath;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return walk(this, fn);\n    }\n}\nexports.BaseError = BaseError;\nfunction walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err && typeof err === 'object' && 'cause' in err && err.cause)\n        return walk(err.cause, fn);\n    return fn ? null : err;\n}\n//# sourceMappingURL=Errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Hash.js":
/*!*******************************************!*\
  !*** ./node_modules/ox/_cjs/core/Hash.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.keccak256 = keccak256;\nexports.ripemd160 = ripemd160;\nexports.sha256 = sha256;\nexports.validate = validate;\nconst ripemd160_1 = __webpack_require__(/*! @noble/hashes/ripemd160 */ \"(ssr)/./node_modules/@noble/hashes/ripemd160.js\");\nconst sha3_1 = __webpack_require__(/*! @noble/hashes/sha3 */ \"(ssr)/./node_modules/@noble/hashes/sha3.js\");\nconst sha256_1 = __webpack_require__(/*! @noble/hashes/sha256 */ \"(ssr)/./node_modules/@noble/hashes/sha256.js\");\nconst Bytes = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nfunction keccak256(value, options = {}) {\n    const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options;\n    const bytes = (0, sha3_1.keccak_256)(Bytes.from(value));\n    if (as === 'Bytes')\n        return bytes;\n    return Hex.fromBytes(bytes);\n}\nfunction ripemd160(value, options = {}) {\n    const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options;\n    const bytes = (0, ripemd160_1.ripemd160)(Bytes.from(value));\n    if (as === 'Bytes')\n        return bytes;\n    return Hex.fromBytes(bytes);\n}\nfunction sha256(value, options = {}) {\n    const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options;\n    const bytes = (0, sha256_1.sha256)(Bytes.from(value));\n    if (as === 'Bytes')\n        return bytes;\n    return Hex.fromBytes(bytes);\n}\nfunction validate(value) {\n    return Hex.validate(value) && Hex.size(value) === 32;\n}\n//# sourceMappingURL=Hash.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Hash.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Hex.js":
/*!******************************************!*\
  !*** ./node_modules/ox/_cjs/core/Hex.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SizeExceedsPaddingSizeError = exports.SliceOffsetOutOfBoundsError = exports.SizeOverflowError = exports.InvalidLengthError = exports.InvalidHexValueError = exports.InvalidHexTypeError = exports.InvalidHexBooleanError = exports.IntegerOutOfRangeError = void 0;\nexports.assert = assert;\nexports.concat = concat;\nexports.from = from;\nexports.fromBoolean = fromBoolean;\nexports.fromBytes = fromBytes;\nexports.fromNumber = fromNumber;\nexports.fromString = fromString;\nexports.isEqual = isEqual;\nexports.padLeft = padLeft;\nexports.padRight = padRight;\nexports.random = random;\nexports.slice = slice;\nexports.size = size;\nexports.trimLeft = trimLeft;\nexports.trimRight = trimRight;\nexports.toBigInt = toBigInt;\nexports.toBoolean = toBoolean;\nexports.toBytes = toBytes;\nexports.toNumber = toNumber;\nexports.toString = toString;\nexports.validate = validate;\nconst utils_1 = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/@noble/curves/abstract/utils.js\");\nconst Bytes = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nconst Errors = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Json = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_cjs/core/Json.js\");\nconst internal_bytes = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/bytes.js\");\nconst internal = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/internal/hex.js\");\nconst encoder = new TextEncoder();\nconst hexes = Array.from({ length: 256 }, (_v, i) => i.toString(16).padStart(2, '0'));\nfunction assert(value, options = {}) {\n    const { strict = false } = options;\n    if (!value)\n        throw new InvalidHexTypeError(value);\n    if (typeof value !== 'string')\n        throw new InvalidHexTypeError(value);\n    if (strict) {\n        if (!/^0x[0-9a-fA-F]*$/.test(value))\n            throw new InvalidHexValueError(value);\n    }\n    if (!value.startsWith('0x'))\n        throw new InvalidHexValueError(value);\n}\nfunction concat(...values) {\n    return `0x${values.reduce((acc, x) => acc + x.replace('0x', ''), '')}`;\n}\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return fromBytes(value);\n    if (Array.isArray(value))\n        return fromBytes(new Uint8Array(value));\n    return value;\n}\nfunction fromBoolean(value, options = {}) {\n    const hex = `0x${Number(value)}`;\n    if (typeof options.size === 'number') {\n        internal.assertSize(hex, options.size);\n        return padLeft(hex, options.size);\n    }\n    return hex;\n}\nfunction fromBytes(value, options = {}) {\n    let string = '';\n    for (let i = 0; i < value.length; i++)\n        string += hexes[value[i]];\n    const hex = `0x${string}`;\n    if (typeof options.size === 'number') {\n        internal.assertSize(hex, options.size);\n        return padRight(hex, options.size);\n    }\n    return hex;\n}\nfunction fromNumber(value, options = {}) {\n    const { signed, size } = options;\n    const value_ = BigInt(value);\n    let maxValue;\n    if (size) {\n        if (signed)\n            maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n        else\n            maxValue = 2n ** (BigInt(size) * 8n) - 1n;\n    }\n    else if (typeof value === 'number') {\n        maxValue = BigInt(Number.MAX_SAFE_INTEGER);\n    }\n    const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0;\n    if ((maxValue && value_ > maxValue) || value_ < minValue) {\n        const suffix = typeof value === 'bigint' ? 'n' : '';\n        throw new IntegerOutOfRangeError({\n            max: maxValue ? `${maxValue}${suffix}` : undefined,\n            min: `${minValue}${suffix}`,\n            signed,\n            size,\n            value: `${value}${suffix}`,\n        });\n    }\n    const stringValue = (signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_).toString(16);\n    const hex = `0x${stringValue}`;\n    if (size)\n        return padLeft(hex, size);\n    return hex;\n}\nfunction fromString(value, options = {}) {\n    return fromBytes(encoder.encode(value), options);\n}\nfunction isEqual(hexA, hexB) {\n    return (0, utils_1.equalBytes)(Bytes.fromHex(hexA), Bytes.fromHex(hexB));\n}\nfunction padLeft(value, size) {\n    return internal.pad(value, { dir: 'left', size });\n}\nfunction padRight(value, size) {\n    return internal.pad(value, { dir: 'right', size });\n}\nfunction random(length) {\n    return fromBytes(Bytes.random(length));\n}\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    internal.assertStartOffset(value, start);\n    const value_ = `0x${value\n        .replace('0x', '')\n        .slice((start ?? 0) * 2, (end ?? value.length) * 2)}`;\n    if (strict)\n        internal.assertEndOffset(value_, start, end);\n    return value_;\n}\nfunction size(value) {\n    return Math.ceil((value.length - 2) / 2);\n}\nfunction trimLeft(value) {\n    return internal.trim(value, { dir: 'left' });\n}\nfunction trimRight(value) {\n    return internal.trim(value, { dir: 'right' });\n}\nfunction toBigInt(hex, options = {}) {\n    const { signed } = options;\n    if (options.size)\n        internal.assertSize(hex, options.size);\n    const value = BigInt(hex);\n    if (!signed)\n        return value;\n    const size = (hex.length - 2) / 2;\n    const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n;\n    const max_signed = max_unsigned >> 1n;\n    if (value <= max_signed)\n        return value;\n    return value - max_unsigned - 1n;\n}\nfunction toBoolean(hex, options = {}) {\n    if (options.size)\n        internal.assertSize(hex, options.size);\n    const hex_ = trimLeft(hex);\n    if (hex_ === '0x')\n        return false;\n    if (hex_ === '0x1')\n        return true;\n    throw new InvalidHexBooleanError(hex);\n}\nfunction toBytes(hex, options = {}) {\n    return Bytes.fromHex(hex, options);\n}\nfunction toNumber(hex, options = {}) {\n    const { signed, size } = options;\n    if (!signed && !size)\n        return Number(hex);\n    return Number(toBigInt(hex, options));\n}\nfunction toString(hex, options = {}) {\n    const { size } = options;\n    let bytes = Bytes.fromHex(hex);\n    if (size) {\n        internal_bytes.assertSize(bytes, size);\n        bytes = Bytes.trimRight(bytes);\n    }\n    return new TextDecoder().decode(bytes);\n}\nfunction validate(value, options = {}) {\n    const { strict = false } = options;\n    try {\n        assert(value, { strict });\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nclass IntegerOutOfRangeError extends Errors.BaseError {\n    constructor({ max, min, signed, size, value, }) {\n        super(`Number \\`${value}\\` is not in safe${size ? ` ${size * 8}-bit` : ''}${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.IntegerOutOfRangeError'\n        });\n    }\n}\nexports.IntegerOutOfRangeError = IntegerOutOfRangeError;\nclass InvalidHexBooleanError extends Errors.BaseError {\n    constructor(hex) {\n        super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexBooleanError'\n        });\n    }\n}\nexports.InvalidHexBooleanError = InvalidHexBooleanError;\nclass InvalidHexTypeError extends Errors.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? Json.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`, {\n            metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexTypeError'\n        });\n    }\n}\nexports.InvalidHexTypeError = InvalidHexTypeError;\nclass InvalidHexValueError extends Errors.BaseError {\n    constructor(value) {\n        super(`Value \\`${value}\\` is an invalid hex value.`, {\n            metaMessages: [\n                'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexValueError'\n        });\n    }\n}\nexports.InvalidHexValueError = InvalidHexValueError;\nclass InvalidLengthError extends Errors.BaseError {\n    constructor(value) {\n        super(`Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`, {\n            metaMessages: ['It must be an even length.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidLengthError'\n        });\n    }\n}\nexports.InvalidLengthError = InvalidLengthError;\nclass SizeOverflowError extends Errors.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeOverflowError'\n        });\n    }\n}\nexports.SizeOverflowError = SizeOverflowError;\nclass SliceOffsetOutOfBoundsError extends Errors.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\nexports.SliceOffsetOutOfBoundsError = SliceOffsetOutOfBoundsError;\nclass SizeExceedsPaddingSizeError extends Errors.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeExceedsPaddingSizeError'\n        });\n    }\n}\nexports.SizeExceedsPaddingSizeError = SizeExceedsPaddingSizeError;\n//# sourceMappingURL=Hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Json.js":
/*!*******************************************!*\
  !*** ./node_modules/ox/_cjs/core/Json.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parse = parse;\nexports.stringify = stringify;\nconst bigIntSuffix = '#__bigint';\nfunction parse(string, reviver) {\n    return JSON.parse(string, (key, value_) => {\n        const value = value_;\n        if (typeof value === 'string' && value.endsWith(bigIntSuffix))\n            return BigInt(value.slice(0, -bigIntSuffix.length));\n        return typeof reviver === 'function' ? reviver(key, value) : value;\n    });\n}\nfunction stringify(value, replacer, space) {\n    return JSON.stringify(value, (key, value) => {\n        if (typeof replacer === 'function')\n            return replacer(key, value);\n        if (typeof value === 'bigint')\n            return value.toString() + bigIntSuffix;\n        return value;\n    }, space);\n}\n//# sourceMappingURL=Json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL0pzb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYTtBQUNiLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcb3hcXF9janNcXGNvcmVcXEpzb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnBhcnNlID0gcGFyc2U7XG5leHBvcnRzLnN0cmluZ2lmeSA9IHN0cmluZ2lmeTtcbmNvbnN0IGJpZ0ludFN1ZmZpeCA9ICcjX19iaWdpbnQnO1xuZnVuY3Rpb24gcGFyc2Uoc3RyaW5nLCByZXZpdmVyKSB7XG4gICAgcmV0dXJuIEpTT04ucGFyc2Uoc3RyaW5nLCAoa2V5LCB2YWx1ZV8pID0+IHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSB2YWx1ZV87XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLmVuZHNXaXRoKGJpZ0ludFN1ZmZpeCkpXG4gICAgICAgICAgICByZXR1cm4gQmlnSW50KHZhbHVlLnNsaWNlKDAsIC1iaWdJbnRTdWZmaXgubGVuZ3RoKSk7XG4gICAgICAgIHJldHVybiB0eXBlb2YgcmV2aXZlciA9PT0gJ2Z1bmN0aW9uJyA/IHJldml2ZXIoa2V5LCB2YWx1ZSkgOiB2YWx1ZTtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIHN0cmluZ2lmeSh2YWx1ZSwgcmVwbGFjZXIsIHNwYWNlKSB7XG4gICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KHZhbHVlLCAoa2V5LCB2YWx1ZSkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIHJlcGxhY2VyID09PSAnZnVuY3Rpb24nKVxuICAgICAgICAgICAgcmV0dXJuIHJlcGxhY2VyKGtleSwgdmFsdWUpO1xuICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnYmlnaW50JylcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b1N0cmluZygpICsgYmlnSW50U3VmZml4O1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfSwgc3BhY2UpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9SnNvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/PublicKey.js":
/*!************************************************!*\
  !*** ./node_modules/ox/_cjs/core/PublicKey.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidSerializedSizeError = exports.InvalidUncompressedPrefixError = exports.InvalidCompressedPrefixError = exports.InvalidPrefixError = exports.InvalidError = void 0;\nexports.assert = assert;\nexports.compress = compress;\nexports.from = from;\nexports.fromBytes = fromBytes;\nexports.fromHex = fromHex;\nexports.toBytes = toBytes;\nexports.toHex = toHex;\nexports.validate = validate;\nconst Bytes = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nconst Errors = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nconst Json = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_cjs/core/Json.js\");\nfunction assert(publicKey, options = {}) {\n    const { compressed } = options;\n    const { prefix, x, y } = publicKey;\n    if (compressed === false ||\n        (typeof x === 'bigint' && typeof y === 'bigint')) {\n        if (prefix !== 4)\n            throw new InvalidPrefixError({\n                prefix,\n                cause: new InvalidUncompressedPrefixError(),\n            });\n        return;\n    }\n    if (compressed === true ||\n        (typeof x === 'bigint' && typeof y === 'undefined')) {\n        if (prefix !== 3 && prefix !== 2)\n            throw new InvalidPrefixError({\n                prefix,\n                cause: new InvalidCompressedPrefixError(),\n            });\n        return;\n    }\n    throw new InvalidError({ publicKey });\n}\nfunction compress(publicKey) {\n    const { x, y } = publicKey;\n    return {\n        prefix: y % 2n === 0n ? 2 : 3,\n        x,\n    };\n}\nfunction from(value) {\n    const publicKey = (() => {\n        if (Hex.validate(value))\n            return fromHex(value);\n        if (Bytes.validate(value))\n            return fromBytes(value);\n        const { prefix, x, y } = value;\n        if (typeof x === 'bigint' && typeof y === 'bigint')\n            return { prefix: prefix ?? 0x04, x, y };\n        return { prefix, x };\n    })();\n    assert(publicKey);\n    return publicKey;\n}\nfunction fromBytes(publicKey) {\n    return fromHex(Hex.fromBytes(publicKey));\n}\nfunction fromHex(publicKey) {\n    if (publicKey.length !== 132 &&\n        publicKey.length !== 130 &&\n        publicKey.length !== 68)\n        throw new InvalidSerializedSizeError({ publicKey });\n    if (publicKey.length === 130) {\n        const x = BigInt(Hex.slice(publicKey, 0, 32));\n        const y = BigInt(Hex.slice(publicKey, 32, 64));\n        return {\n            prefix: 4,\n            x,\n            y,\n        };\n    }\n    if (publicKey.length === 132) {\n        const prefix = Number(Hex.slice(publicKey, 0, 1));\n        const x = BigInt(Hex.slice(publicKey, 1, 33));\n        const y = BigInt(Hex.slice(publicKey, 33, 65));\n        return {\n            prefix,\n            x,\n            y,\n        };\n    }\n    const prefix = Number(Hex.slice(publicKey, 0, 1));\n    const x = BigInt(Hex.slice(publicKey, 1, 33));\n    return {\n        prefix,\n        x,\n    };\n}\nfunction toBytes(publicKey, options = {}) {\n    return Bytes.fromHex(toHex(publicKey, options));\n}\nfunction toHex(publicKey, options = {}) {\n    assert(publicKey);\n    const { prefix, x, y } = publicKey;\n    const { includePrefix = true } = options;\n    const publicKey_ = Hex.concat(includePrefix ? Hex.fromNumber(prefix, { size: 1 }) : '0x', Hex.fromNumber(x, { size: 32 }), typeof y === 'bigint' ? Hex.fromNumber(y, { size: 32 }) : '0x');\n    return publicKey_;\n}\nfunction validate(publicKey, options = {}) {\n    try {\n        assert(publicKey, options);\n        return true;\n    }\n    catch (error) {\n        return false;\n    }\n}\nclass InvalidError extends Errors.BaseError {\n    constructor({ publicKey }) {\n        super(`Value \\`${Json.stringify(publicKey)}\\` is not a valid public key.`, {\n            metaMessages: [\n                'Public key must contain:',\n                '- an `x` and `prefix` value (compressed)',\n                '- an `x`, `y`, and `prefix` value (uncompressed)',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'PublicKey.InvalidError'\n        });\n    }\n}\nexports.InvalidError = InvalidError;\nclass InvalidPrefixError extends Errors.BaseError {\n    constructor({ prefix, cause }) {\n        super(`Prefix \"${prefix}\" is invalid.`, {\n            cause,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'PublicKey.InvalidPrefixError'\n        });\n    }\n}\nexports.InvalidPrefixError = InvalidPrefixError;\nclass InvalidCompressedPrefixError extends Errors.BaseError {\n    constructor() {\n        super('Prefix must be 2 or 3 for compressed public keys.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'PublicKey.InvalidCompressedPrefixError'\n        });\n    }\n}\nexports.InvalidCompressedPrefixError = InvalidCompressedPrefixError;\nclass InvalidUncompressedPrefixError extends Errors.BaseError {\n    constructor() {\n        super('Prefix must be 4 for uncompressed public keys.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'PublicKey.InvalidUncompressedPrefixError'\n        });\n    }\n}\nexports.InvalidUncompressedPrefixError = InvalidUncompressedPrefixError;\nclass InvalidSerializedSizeError extends Errors.BaseError {\n    constructor({ publicKey }) {\n        super(`Value \\`${publicKey}\\` is an invalid public key size.`, {\n            metaMessages: [\n                'Expected: 33 bytes (compressed + prefix), 64 bytes (uncompressed) or 65 bytes (uncompressed + prefix).',\n                `Received ${Hex.size(Hex.from(publicKey))} bytes.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'PublicKey.InvalidSerializedSizeError'\n        });\n    }\n}\nexports.InvalidSerializedSizeError = InvalidSerializedSizeError;\n//# sourceMappingURL=PublicKey.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/PublicKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Solidity.js":
/*!***********************************************!*\
  !*** ./node_modules/ox/_cjs/core/Solidity.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.minInt120 = exports.minInt112 = exports.minInt104 = exports.minInt96 = exports.minInt88 = exports.minInt80 = exports.minInt72 = exports.minInt64 = exports.minInt56 = exports.minInt48 = exports.minInt40 = exports.minInt32 = exports.minInt24 = exports.minInt16 = exports.minInt8 = exports.maxInt256 = exports.maxInt248 = exports.maxInt240 = exports.maxInt232 = exports.maxInt224 = exports.maxInt216 = exports.maxInt208 = exports.maxInt200 = exports.maxInt192 = exports.maxInt184 = exports.maxInt176 = exports.maxInt168 = exports.maxInt160 = exports.maxInt152 = exports.maxInt144 = exports.maxInt136 = exports.maxInt128 = exports.maxInt120 = exports.maxInt112 = exports.maxInt104 = exports.maxInt96 = exports.maxInt88 = exports.maxInt80 = exports.maxInt72 = exports.maxInt64 = exports.maxInt56 = exports.maxInt48 = exports.maxInt40 = exports.maxInt32 = exports.maxInt24 = exports.maxInt16 = exports.maxInt8 = exports.integerRegex = exports.bytesRegex = exports.arrayRegex = void 0;\nexports.maxUint256 = exports.maxUint248 = exports.maxUint240 = exports.maxUint232 = exports.maxUint224 = exports.maxUint216 = exports.maxUint208 = exports.maxUint200 = exports.maxUint192 = exports.maxUint184 = exports.maxUint176 = exports.maxUint168 = exports.maxUint160 = exports.maxUint152 = exports.maxUint144 = exports.maxUint136 = exports.maxUint128 = exports.maxUint120 = exports.maxUint112 = exports.maxUint104 = exports.maxUint96 = exports.maxUint88 = exports.maxUint80 = exports.maxUint72 = exports.maxUint64 = exports.maxUint56 = exports.maxUint48 = exports.maxUint40 = exports.maxUint32 = exports.maxUint24 = exports.maxUint16 = exports.maxUint8 = exports.minInt256 = exports.minInt248 = exports.minInt240 = exports.minInt232 = exports.minInt224 = exports.minInt216 = exports.minInt208 = exports.minInt200 = exports.minInt192 = exports.minInt184 = exports.minInt176 = exports.minInt168 = exports.minInt160 = exports.minInt152 = exports.minInt144 = exports.minInt136 = exports.minInt128 = void 0;\nexports.arrayRegex = /^(.*)\\[([0-9]*)\\]$/;\nexports.bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\nexports.integerRegex = /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\nexports.maxInt8 = 2n ** (8n - 1n) - 1n;\nexports.maxInt16 = 2n ** (16n - 1n) - 1n;\nexports.maxInt24 = 2n ** (24n - 1n) - 1n;\nexports.maxInt32 = 2n ** (32n - 1n) - 1n;\nexports.maxInt40 = 2n ** (40n - 1n) - 1n;\nexports.maxInt48 = 2n ** (48n - 1n) - 1n;\nexports.maxInt56 = 2n ** (56n - 1n) - 1n;\nexports.maxInt64 = 2n ** (64n - 1n) - 1n;\nexports.maxInt72 = 2n ** (72n - 1n) - 1n;\nexports.maxInt80 = 2n ** (80n - 1n) - 1n;\nexports.maxInt88 = 2n ** (88n - 1n) - 1n;\nexports.maxInt96 = 2n ** (96n - 1n) - 1n;\nexports.maxInt104 = 2n ** (104n - 1n) - 1n;\nexports.maxInt112 = 2n ** (112n - 1n) - 1n;\nexports.maxInt120 = 2n ** (120n - 1n) - 1n;\nexports.maxInt128 = 2n ** (128n - 1n) - 1n;\nexports.maxInt136 = 2n ** (136n - 1n) - 1n;\nexports.maxInt144 = 2n ** (144n - 1n) - 1n;\nexports.maxInt152 = 2n ** (152n - 1n) - 1n;\nexports.maxInt160 = 2n ** (160n - 1n) - 1n;\nexports.maxInt168 = 2n ** (168n - 1n) - 1n;\nexports.maxInt176 = 2n ** (176n - 1n) - 1n;\nexports.maxInt184 = 2n ** (184n - 1n) - 1n;\nexports.maxInt192 = 2n ** (192n - 1n) - 1n;\nexports.maxInt200 = 2n ** (200n - 1n) - 1n;\nexports.maxInt208 = 2n ** (208n - 1n) - 1n;\nexports.maxInt216 = 2n ** (216n - 1n) - 1n;\nexports.maxInt224 = 2n ** (224n - 1n) - 1n;\nexports.maxInt232 = 2n ** (232n - 1n) - 1n;\nexports.maxInt240 = 2n ** (240n - 1n) - 1n;\nexports.maxInt248 = 2n ** (248n - 1n) - 1n;\nexports.maxInt256 = 2n ** (256n - 1n) - 1n;\nexports.minInt8 = -(2n ** (8n - 1n));\nexports.minInt16 = -(2n ** (16n - 1n));\nexports.minInt24 = -(2n ** (24n - 1n));\nexports.minInt32 = -(2n ** (32n - 1n));\nexports.minInt40 = -(2n ** (40n - 1n));\nexports.minInt48 = -(2n ** (48n - 1n));\nexports.minInt56 = -(2n ** (56n - 1n));\nexports.minInt64 = -(2n ** (64n - 1n));\nexports.minInt72 = -(2n ** (72n - 1n));\nexports.minInt80 = -(2n ** (80n - 1n));\nexports.minInt88 = -(2n ** (88n - 1n));\nexports.minInt96 = -(2n ** (96n - 1n));\nexports.minInt104 = -(2n ** (104n - 1n));\nexports.minInt112 = -(2n ** (112n - 1n));\nexports.minInt120 = -(2n ** (120n - 1n));\nexports.minInt128 = -(2n ** (128n - 1n));\nexports.minInt136 = -(2n ** (136n - 1n));\nexports.minInt144 = -(2n ** (144n - 1n));\nexports.minInt152 = -(2n ** (152n - 1n));\nexports.minInt160 = -(2n ** (160n - 1n));\nexports.minInt168 = -(2n ** (168n - 1n));\nexports.minInt176 = -(2n ** (176n - 1n));\nexports.minInt184 = -(2n ** (184n - 1n));\nexports.minInt192 = -(2n ** (192n - 1n));\nexports.minInt200 = -(2n ** (200n - 1n));\nexports.minInt208 = -(2n ** (208n - 1n));\nexports.minInt216 = -(2n ** (216n - 1n));\nexports.minInt224 = -(2n ** (224n - 1n));\nexports.minInt232 = -(2n ** (232n - 1n));\nexports.minInt240 = -(2n ** (240n - 1n));\nexports.minInt248 = -(2n ** (248n - 1n));\nexports.minInt256 = -(2n ** (256n - 1n));\nexports.maxUint8 = 2n ** 8n - 1n;\nexports.maxUint16 = 2n ** 16n - 1n;\nexports.maxUint24 = 2n ** 24n - 1n;\nexports.maxUint32 = 2n ** 32n - 1n;\nexports.maxUint40 = 2n ** 40n - 1n;\nexports.maxUint48 = 2n ** 48n - 1n;\nexports.maxUint56 = 2n ** 56n - 1n;\nexports.maxUint64 = 2n ** 64n - 1n;\nexports.maxUint72 = 2n ** 72n - 1n;\nexports.maxUint80 = 2n ** 80n - 1n;\nexports.maxUint88 = 2n ** 88n - 1n;\nexports.maxUint96 = 2n ** 96n - 1n;\nexports.maxUint104 = 2n ** 104n - 1n;\nexports.maxUint112 = 2n ** 112n - 1n;\nexports.maxUint120 = 2n ** 120n - 1n;\nexports.maxUint128 = 2n ** 128n - 1n;\nexports.maxUint136 = 2n ** 136n - 1n;\nexports.maxUint144 = 2n ** 144n - 1n;\nexports.maxUint152 = 2n ** 152n - 1n;\nexports.maxUint160 = 2n ** 160n - 1n;\nexports.maxUint168 = 2n ** 168n - 1n;\nexports.maxUint176 = 2n ** 176n - 1n;\nexports.maxUint184 = 2n ** 184n - 1n;\nexports.maxUint192 = 2n ** 192n - 1n;\nexports.maxUint200 = 2n ** 200n - 1n;\nexports.maxUint208 = 2n ** 208n - 1n;\nexports.maxUint216 = 2n ** 216n - 1n;\nexports.maxUint224 = 2n ** 224n - 1n;\nexports.maxUint232 = 2n ** 232n - 1n;\nexports.maxUint240 = 2n ** 240n - 1n;\nexports.maxUint248 = 2n ** 248n - 1n;\nexports.maxUint256 = 2n ** 256n - 1n;\n//# sourceMappingURL=Solidity.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Solidity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/Withdrawal.js":
/*!*************************************************!*\
  !*** ./node_modules/ox/_cjs/core/Withdrawal.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fromRpc = fromRpc;\nexports.toRpc = toRpc;\nconst Hex = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nfunction fromRpc(withdrawal) {\n    return {\n        ...withdrawal,\n        amount: BigInt(withdrawal.amount),\n        index: Number(withdrawal.index),\n        validatorIndex: Number(withdrawal.validatorIndex),\n    };\n}\nfunction toRpc(withdrawal) {\n    return {\n        address: withdrawal.address,\n        amount: Hex.fromNumber(withdrawal.amount),\n        index: Hex.fromNumber(withdrawal.index),\n        validatorIndex: Hex.fromNumber(withdrawal.validatorIndex),\n    };\n}\n//# sourceMappingURL=Withdrawal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL1dpdGhkcmF3YWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZUFBZTtBQUNmLGFBQWE7QUFDYixZQUFZLG1CQUFPLENBQUMsMERBQVU7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxveFxcX2Nqc1xcY29yZVxcV2l0aGRyYXdhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZnJvbVJwYyA9IGZyb21ScGM7XG5leHBvcnRzLnRvUnBjID0gdG9ScGM7XG5jb25zdCBIZXggPSByZXF1aXJlKFwiLi9IZXguanNcIik7XG5mdW5jdGlvbiBmcm9tUnBjKHdpdGhkcmF3YWwpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICAuLi53aXRoZHJhd2FsLFxuICAgICAgICBhbW91bnQ6IEJpZ0ludCh3aXRoZHJhd2FsLmFtb3VudCksXG4gICAgICAgIGluZGV4OiBOdW1iZXIod2l0aGRyYXdhbC5pbmRleCksXG4gICAgICAgIHZhbGlkYXRvckluZGV4OiBOdW1iZXIod2l0aGRyYXdhbC52YWxpZGF0b3JJbmRleCksXG4gICAgfTtcbn1cbmZ1bmN0aW9uIHRvUnBjKHdpdGhkcmF3YWwpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBhZGRyZXNzOiB3aXRoZHJhd2FsLmFkZHJlc3MsXG4gICAgICAgIGFtb3VudDogSGV4LmZyb21OdW1iZXIod2l0aGRyYXdhbC5hbW91bnQpLFxuICAgICAgICBpbmRleDogSGV4LmZyb21OdW1iZXIod2l0aGRyYXdhbC5pbmRleCksXG4gICAgICAgIHZhbGlkYXRvckluZGV4OiBIZXguZnJvbU51bWJlcih3aXRoZHJhd2FsLnZhbGlkYXRvckluZGV4KSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9V2l0aGRyYXdhbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/Withdrawal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/abiItem.js":
/*!*******************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/abiItem.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.normalizeSignature = normalizeSignature;\nexports.isArgOfType = isArgOfType;\nexports.getAmbiguousTypes = getAmbiguousTypes;\nconst Address = __webpack_require__(/*! ../Address.js */ \"(ssr)/./node_modules/ox/_cjs/core/Address.js\");\nconst Errors = __webpack_require__(/*! ../Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nfunction normalizeSignature(signature) {\n    let active = true;\n    let current = '';\n    let level = 0;\n    let result = '';\n    let valid = false;\n    for (let i = 0; i < signature.length; i++) {\n        const char = signature[i];\n        if (['(', ')', ','].includes(char))\n            active = true;\n        if (char === '(')\n            level++;\n        if (char === ')')\n            level--;\n        if (!active)\n            continue;\n        if (level === 0) {\n            if (char === ' ' && ['event', 'function', 'error', ''].includes(result))\n                result = '';\n            else {\n                result += char;\n                if (char === ')') {\n                    valid = true;\n                    break;\n                }\n            }\n            continue;\n        }\n        if (char === ' ') {\n            if (signature[i - 1] !== ',' && current !== ',' && current !== ',(') {\n                current = '';\n                active = false;\n            }\n            continue;\n        }\n        result += char;\n        current += char;\n    }\n    if (!valid)\n        throw new Errors.BaseError('Unable to normalize signature.');\n    return result;\n}\nfunction isArgOfType(arg, abiParameter) {\n    const argType = typeof arg;\n    const abiParameterType = abiParameter.type;\n    switch (abiParameterType) {\n        case 'address':\n            return Address.validate(arg, { strict: false });\n        case 'bool':\n            return argType === 'boolean';\n        case 'function':\n            return argType === 'string';\n        case 'string':\n            return argType === 'string';\n        default: {\n            if (abiParameterType === 'tuple' && 'components' in abiParameter)\n                return Object.values(abiParameter.components).every((component, index) => {\n                    return isArgOfType(Object.values(arg)[index], component);\n                });\n            if (/^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(abiParameterType))\n                return argType === 'number' || argType === 'bigint';\n            if (/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(abiParameterType))\n                return argType === 'string' || arg instanceof Uint8Array;\n            if (/[a-z]+[1-9]{0,3}(\\[[0-9]{0,}\\])+$/.test(abiParameterType)) {\n                return (Array.isArray(arg) &&\n                    arg.every((x) => isArgOfType(x, {\n                        ...abiParameter,\n                        type: abiParameterType.replace(/(\\[[0-9]{0,}\\])$/, ''),\n                    })));\n            }\n            return false;\n        }\n    }\n}\nfunction getAmbiguousTypes(sourceParameters, targetParameters, args) {\n    for (const parameterIndex in sourceParameters) {\n        const sourceParameter = sourceParameters[parameterIndex];\n        const targetParameter = targetParameters[parameterIndex];\n        if (sourceParameter.type === 'tuple' &&\n            targetParameter.type === 'tuple' &&\n            'components' in sourceParameter &&\n            'components' in targetParameter)\n            return getAmbiguousTypes(sourceParameter.components, targetParameter.components, args[parameterIndex]);\n        const types = [sourceParameter.type, targetParameter.type];\n        const ambiguous = (() => {\n            if (types.includes('address') && types.includes('bytes20'))\n                return true;\n            if (types.includes('address') && types.includes('string'))\n                return Address.validate(args[parameterIndex], {\n                    strict: false,\n                });\n            if (types.includes('address') && types.includes('bytes'))\n                return Address.validate(args[parameterIndex], {\n                    strict: false,\n                });\n            return false;\n        })();\n        if (ambiguous)\n            return types;\n    }\n    return;\n}\n//# sourceMappingURL=abiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL2ludGVybmFsL2FiaUl0ZW0uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMEJBQTBCO0FBQzFCLG1CQUFtQjtBQUNuQix5QkFBeUI7QUFDekIsZ0JBQWdCLG1CQUFPLENBQUMsbUVBQWU7QUFDdkMsZUFBZSxtQkFBTyxDQUFDLGlFQUFjO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsZUFBZTtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixJQUFJLFNBQVMsR0FBRztBQUM3QztBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsR0FBRztBQUNwRSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcb3hcXF9janNcXGNvcmVcXGludGVybmFsXFxhYmlJdGVtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5ub3JtYWxpemVTaWduYXR1cmUgPSBub3JtYWxpemVTaWduYXR1cmU7XG5leHBvcnRzLmlzQXJnT2ZUeXBlID0gaXNBcmdPZlR5cGU7XG5leHBvcnRzLmdldEFtYmlndW91c1R5cGVzID0gZ2V0QW1iaWd1b3VzVHlwZXM7XG5jb25zdCBBZGRyZXNzID0gcmVxdWlyZShcIi4uL0FkZHJlc3MuanNcIik7XG5jb25zdCBFcnJvcnMgPSByZXF1aXJlKFwiLi4vRXJyb3JzLmpzXCIpO1xuZnVuY3Rpb24gbm9ybWFsaXplU2lnbmF0dXJlKHNpZ25hdHVyZSkge1xuICAgIGxldCBhY3RpdmUgPSB0cnVlO1xuICAgIGxldCBjdXJyZW50ID0gJyc7XG4gICAgbGV0IGxldmVsID0gMDtcbiAgICBsZXQgcmVzdWx0ID0gJyc7XG4gICAgbGV0IHZhbGlkID0gZmFsc2U7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzaWduYXR1cmUubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgY2hhciA9IHNpZ25hdHVyZVtpXTtcbiAgICAgICAgaWYgKFsnKCcsICcpJywgJywnXS5pbmNsdWRlcyhjaGFyKSlcbiAgICAgICAgICAgIGFjdGl2ZSA9IHRydWU7XG4gICAgICAgIGlmIChjaGFyID09PSAnKCcpXG4gICAgICAgICAgICBsZXZlbCsrO1xuICAgICAgICBpZiAoY2hhciA9PT0gJyknKVxuICAgICAgICAgICAgbGV2ZWwtLTtcbiAgICAgICAgaWYgKCFhY3RpdmUpXG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgaWYgKGxldmVsID09PSAwKSB7XG4gICAgICAgICAgICBpZiAoY2hhciA9PT0gJyAnICYmIFsnZXZlbnQnLCAnZnVuY3Rpb24nLCAnZXJyb3InLCAnJ10uaW5jbHVkZXMocmVzdWx0KSlcbiAgICAgICAgICAgICAgICByZXN1bHQgPSAnJztcbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHJlc3VsdCArPSBjaGFyO1xuICAgICAgICAgICAgICAgIGlmIChjaGFyID09PSAnKScpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsaWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY2hhciA9PT0gJyAnKSB7XG4gICAgICAgICAgICBpZiAoc2lnbmF0dXJlW2kgLSAxXSAhPT0gJywnICYmIGN1cnJlbnQgIT09ICcsJyAmJiBjdXJyZW50ICE9PSAnLCgnKSB7XG4gICAgICAgICAgICAgICAgY3VycmVudCA9ICcnO1xuICAgICAgICAgICAgICAgIGFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0ICs9IGNoYXI7XG4gICAgICAgIGN1cnJlbnQgKz0gY2hhcjtcbiAgICB9XG4gICAgaWYgKCF2YWxpZClcbiAgICAgICAgdGhyb3cgbmV3IEVycm9ycy5CYXNlRXJyb3IoJ1VuYWJsZSB0byBub3JtYWxpemUgc2lnbmF0dXJlLicpO1xuICAgIHJldHVybiByZXN1bHQ7XG59XG5mdW5jdGlvbiBpc0FyZ09mVHlwZShhcmcsIGFiaVBhcmFtZXRlcikge1xuICAgIGNvbnN0IGFyZ1R5cGUgPSB0eXBlb2YgYXJnO1xuICAgIGNvbnN0IGFiaVBhcmFtZXRlclR5cGUgPSBhYmlQYXJhbWV0ZXIudHlwZTtcbiAgICBzd2l0Y2ggKGFiaVBhcmFtZXRlclR5cGUpIHtcbiAgICAgICAgY2FzZSAnYWRkcmVzcyc6XG4gICAgICAgICAgICByZXR1cm4gQWRkcmVzcy52YWxpZGF0ZShhcmcsIHsgc3RyaWN0OiBmYWxzZSB9KTtcbiAgICAgICAgY2FzZSAnYm9vbCc6XG4gICAgICAgICAgICByZXR1cm4gYXJnVHlwZSA9PT0gJ2Jvb2xlYW4nO1xuICAgICAgICBjYXNlICdmdW5jdGlvbic6XG4gICAgICAgICAgICByZXR1cm4gYXJnVHlwZSA9PT0gJ3N0cmluZyc7XG4gICAgICAgIGNhc2UgJ3N0cmluZyc6XG4gICAgICAgICAgICByZXR1cm4gYXJnVHlwZSA9PT0gJ3N0cmluZyc7XG4gICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAgIGlmIChhYmlQYXJhbWV0ZXJUeXBlID09PSAndHVwbGUnICYmICdjb21wb25lbnRzJyBpbiBhYmlQYXJhbWV0ZXIpXG4gICAgICAgICAgICAgICAgcmV0dXJuIE9iamVjdC52YWx1ZXMoYWJpUGFyYW1ldGVyLmNvbXBvbmVudHMpLmV2ZXJ5KChjb21wb25lbnQsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBpc0FyZ09mVHlwZShPYmplY3QudmFsdWVzKGFyZylbaW5kZXhdLCBjb21wb25lbnQpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKC9edT9pbnQoOHwxNnwyNHwzMnw0MHw0OHw1Nnw2NHw3Mnw4MHw4OHw5NnwxMDR8MTEyfDEyMHwxMjh8MTM2fDE0NHwxNTJ8MTYwfDE2OHwxNzZ8MTg0fDE5MnwyMDB8MjA4fDIxNnwyMjR8MjMyfDI0MHwyNDh8MjU2KT8kLy50ZXN0KGFiaVBhcmFtZXRlclR5cGUpKVxuICAgICAgICAgICAgICAgIHJldHVybiBhcmdUeXBlID09PSAnbnVtYmVyJyB8fCBhcmdUeXBlID09PSAnYmlnaW50JztcbiAgICAgICAgICAgIGlmICgvXmJ5dGVzKFsxLTldfDFbMC05XXwyWzAtOV18M1swLTJdKT8kLy50ZXN0KGFiaVBhcmFtZXRlclR5cGUpKVxuICAgICAgICAgICAgICAgIHJldHVybiBhcmdUeXBlID09PSAnc3RyaW5nJyB8fCBhcmcgaW5zdGFuY2VvZiBVaW50OEFycmF5O1xuICAgICAgICAgICAgaWYgKC9bYS16XStbMS05XXswLDN9KFxcW1swLTldezAsfVxcXSkrJC8udGVzdChhYmlQYXJhbWV0ZXJUeXBlKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiAoQXJyYXkuaXNBcnJheShhcmcpICYmXG4gICAgICAgICAgICAgICAgICAgIGFyZy5ldmVyeSgoeCkgPT4gaXNBcmdPZlR5cGUoeCwge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4uYWJpUGFyYW1ldGVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogYWJpUGFyYW1ldGVyVHlwZS5yZXBsYWNlKC8oXFxbWzAtOV17MCx9XFxdKSQvLCAnJyksXG4gICAgICAgICAgICAgICAgICAgIH0pKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG59XG5mdW5jdGlvbiBnZXRBbWJpZ3VvdXNUeXBlcyhzb3VyY2VQYXJhbWV0ZXJzLCB0YXJnZXRQYXJhbWV0ZXJzLCBhcmdzKSB7XG4gICAgZm9yIChjb25zdCBwYXJhbWV0ZXJJbmRleCBpbiBzb3VyY2VQYXJhbWV0ZXJzKSB7XG4gICAgICAgIGNvbnN0IHNvdXJjZVBhcmFtZXRlciA9IHNvdXJjZVBhcmFtZXRlcnNbcGFyYW1ldGVySW5kZXhdO1xuICAgICAgICBjb25zdCB0YXJnZXRQYXJhbWV0ZXIgPSB0YXJnZXRQYXJhbWV0ZXJzW3BhcmFtZXRlckluZGV4XTtcbiAgICAgICAgaWYgKHNvdXJjZVBhcmFtZXRlci50eXBlID09PSAndHVwbGUnICYmXG4gICAgICAgICAgICB0YXJnZXRQYXJhbWV0ZXIudHlwZSA9PT0gJ3R1cGxlJyAmJlxuICAgICAgICAgICAgJ2NvbXBvbmVudHMnIGluIHNvdXJjZVBhcmFtZXRlciAmJlxuICAgICAgICAgICAgJ2NvbXBvbmVudHMnIGluIHRhcmdldFBhcmFtZXRlcilcbiAgICAgICAgICAgIHJldHVybiBnZXRBbWJpZ3VvdXNUeXBlcyhzb3VyY2VQYXJhbWV0ZXIuY29tcG9uZW50cywgdGFyZ2V0UGFyYW1ldGVyLmNvbXBvbmVudHMsIGFyZ3NbcGFyYW1ldGVySW5kZXhdKTtcbiAgICAgICAgY29uc3QgdHlwZXMgPSBbc291cmNlUGFyYW1ldGVyLnR5cGUsIHRhcmdldFBhcmFtZXRlci50eXBlXTtcbiAgICAgICAgY29uc3QgYW1iaWd1b3VzID0gKCgpID0+IHtcbiAgICAgICAgICAgIGlmICh0eXBlcy5pbmNsdWRlcygnYWRkcmVzcycpICYmIHR5cGVzLmluY2x1ZGVzKCdieXRlczIwJykpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICBpZiAodHlwZXMuaW5jbHVkZXMoJ2FkZHJlc3MnKSAmJiB0eXBlcy5pbmNsdWRlcygnc3RyaW5nJykpXG4gICAgICAgICAgICAgICAgcmV0dXJuIEFkZHJlc3MudmFsaWRhdGUoYXJnc1twYXJhbWV0ZXJJbmRleF0sIHtcbiAgICAgICAgICAgICAgICAgICAgc3RyaWN0OiBmYWxzZSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICh0eXBlcy5pbmNsdWRlcygnYWRkcmVzcycpICYmIHR5cGVzLmluY2x1ZGVzKCdieXRlcycpKVxuICAgICAgICAgICAgICAgIHJldHVybiBBZGRyZXNzLnZhbGlkYXRlKGFyZ3NbcGFyYW1ldGVySW5kZXhdLCB7XG4gICAgICAgICAgICAgICAgICAgIHN0cmljdDogZmFsc2UsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0pKCk7XG4gICAgICAgIGlmIChhbWJpZ3VvdXMpXG4gICAgICAgICAgICByZXR1cm4gdHlwZXM7XG4gICAgfVxuICAgIHJldHVybjtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFiaUl0ZW0uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/abiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/abiParameters.js":
/*!*************************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/abiParameters.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.decodeParameter = decodeParameter;\nexports.decodeAddress = decodeAddress;\nexports.decodeArray = decodeArray;\nexports.decodeBool = decodeBool;\nexports.decodeBytes = decodeBytes;\nexports.decodeNumber = decodeNumber;\nexports.decodeTuple = decodeTuple;\nexports.decodeString = decodeString;\nexports.prepareParameters = prepareParameters;\nexports.prepareParameter = prepareParameter;\nexports.encode = encode;\nexports.encodeAddress = encodeAddress;\nexports.encodeArray = encodeArray;\nexports.encodeBytes = encodeBytes;\nexports.encodeBoolean = encodeBoolean;\nexports.encodeNumber = encodeNumber;\nexports.encodeString = encodeString;\nexports.encodeTuple = encodeTuple;\nexports.getArrayComponents = getArrayComponents;\nexports.hasDynamicChild = hasDynamicChild;\nconst AbiParameters = __webpack_require__(/*! ../AbiParameters.js */ \"(ssr)/./node_modules/ox/_cjs/core/AbiParameters.js\");\nconst Address = __webpack_require__(/*! ../Address.js */ \"(ssr)/./node_modules/ox/_cjs/core/Address.js\");\nconst Bytes = __webpack_require__(/*! ../Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nconst Errors = __webpack_require__(/*! ../Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst Hex = __webpack_require__(/*! ../Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nconst Solidity_js_1 = __webpack_require__(/*! ../Solidity.js */ \"(ssr)/./node_modules/ox/_cjs/core/Solidity.js\");\nfunction decodeParameter(cursor, param, options) {\n    const { checksumAddress, staticPosition } = options;\n    const arrayComponents = getArrayComponents(param.type);\n    if (arrayComponents) {\n        const [length, type] = arrayComponents;\n        return decodeArray(cursor, { ...param, type }, { checksumAddress, length, staticPosition });\n    }\n    if (param.type === 'tuple')\n        return decodeTuple(cursor, param, {\n            checksumAddress,\n            staticPosition,\n        });\n    if (param.type === 'address')\n        return decodeAddress(cursor, { checksum: checksumAddress });\n    if (param.type === 'bool')\n        return decodeBool(cursor);\n    if (param.type.startsWith('bytes'))\n        return decodeBytes(cursor, param, { staticPosition });\n    if (param.type.startsWith('uint') || param.type.startsWith('int'))\n        return decodeNumber(cursor, param);\n    if (param.type === 'string')\n        return decodeString(cursor, { staticPosition });\n    throw new AbiParameters.InvalidTypeError(param.type);\n}\nconst sizeOfLength = 32;\nconst sizeOfOffset = 32;\nfunction decodeAddress(cursor, options = {}) {\n    const { checksum = false } = options;\n    const value = cursor.readBytes(32);\n    const wrap = (address) => checksum ? Address.checksum(address) : address;\n    return [wrap(Hex.fromBytes(Bytes.slice(value, -20))), 32];\n}\nfunction decodeArray(cursor, param, options) {\n    const { checksumAddress, length, staticPosition } = options;\n    if (!length) {\n        const offset = Bytes.toNumber(cursor.readBytes(sizeOfOffset));\n        const start = staticPosition + offset;\n        const startOfData = start + sizeOfLength;\n        cursor.setPosition(start);\n        const length = Bytes.toNumber(cursor.readBytes(sizeOfLength));\n        const dynamicChild = hasDynamicChild(param);\n        let consumed = 0;\n        const value = [];\n        for (let i = 0; i < length; ++i) {\n            cursor.setPosition(startOfData + (dynamicChild ? i * 32 : consumed));\n            const [data, consumed_] = decodeParameter(cursor, param, {\n                checksumAddress,\n                staticPosition: startOfData,\n            });\n            consumed += consumed_;\n            value.push(data);\n        }\n        cursor.setPosition(staticPosition + 32);\n        return [value, 32];\n    }\n    if (hasDynamicChild(param)) {\n        const offset = Bytes.toNumber(cursor.readBytes(sizeOfOffset));\n        const start = staticPosition + offset;\n        const value = [];\n        for (let i = 0; i < length; ++i) {\n            cursor.setPosition(start + i * 32);\n            const [data] = decodeParameter(cursor, param, {\n                checksumAddress,\n                staticPosition: start,\n            });\n            value.push(data);\n        }\n        cursor.setPosition(staticPosition + 32);\n        return [value, 32];\n    }\n    let consumed = 0;\n    const value = [];\n    for (let i = 0; i < length; ++i) {\n        const [data, consumed_] = decodeParameter(cursor, param, {\n            checksumAddress,\n            staticPosition: staticPosition + consumed,\n        });\n        consumed += consumed_;\n        value.push(data);\n    }\n    return [value, consumed];\n}\nfunction decodeBool(cursor) {\n    return [Bytes.toBoolean(cursor.readBytes(32), { size: 32 }), 32];\n}\nfunction decodeBytes(cursor, param, { staticPosition }) {\n    const [_, size] = param.type.split('bytes');\n    if (!size) {\n        const offset = Bytes.toNumber(cursor.readBytes(32));\n        cursor.setPosition(staticPosition + offset);\n        const length = Bytes.toNumber(cursor.readBytes(32));\n        if (length === 0) {\n            cursor.setPosition(staticPosition + 32);\n            return ['0x', 32];\n        }\n        const data = cursor.readBytes(length);\n        cursor.setPosition(staticPosition + 32);\n        return [Hex.fromBytes(data), 32];\n    }\n    const value = Hex.fromBytes(cursor.readBytes(Number.parseInt(size), 32));\n    return [value, 32];\n}\nfunction decodeNumber(cursor, param) {\n    const signed = param.type.startsWith('int');\n    const size = Number.parseInt(param.type.split('int')[1] || '256');\n    const value = cursor.readBytes(32);\n    return [\n        size > 48\n            ? Bytes.toBigInt(value, { signed })\n            : Bytes.toNumber(value, { signed }),\n        32,\n    ];\n}\nfunction decodeTuple(cursor, param, options) {\n    const { checksumAddress, staticPosition } = options;\n    const hasUnnamedChild = param.components.length === 0 || param.components.some(({ name }) => !name);\n    const value = hasUnnamedChild ? [] : {};\n    let consumed = 0;\n    if (hasDynamicChild(param)) {\n        const offset = Bytes.toNumber(cursor.readBytes(sizeOfOffset));\n        const start = staticPosition + offset;\n        for (let i = 0; i < param.components.length; ++i) {\n            const component = param.components[i];\n            cursor.setPosition(start + consumed);\n            const [data, consumed_] = decodeParameter(cursor, component, {\n                checksumAddress,\n                staticPosition: start,\n            });\n            consumed += consumed_;\n            value[hasUnnamedChild ? i : component?.name] = data;\n        }\n        cursor.setPosition(staticPosition + 32);\n        return [value, 32];\n    }\n    for (let i = 0; i < param.components.length; ++i) {\n        const component = param.components[i];\n        const [data, consumed_] = decodeParameter(cursor, component, {\n            checksumAddress,\n            staticPosition,\n        });\n        value[hasUnnamedChild ? i : component?.name] = data;\n        consumed += consumed_;\n    }\n    return [value, consumed];\n}\nfunction decodeString(cursor, { staticPosition }) {\n    const offset = Bytes.toNumber(cursor.readBytes(32));\n    const start = staticPosition + offset;\n    cursor.setPosition(start);\n    const length = Bytes.toNumber(cursor.readBytes(32));\n    if (length === 0) {\n        cursor.setPosition(staticPosition + 32);\n        return ['', 32];\n    }\n    const data = cursor.readBytes(length, 32);\n    const value = Bytes.toString(Bytes.trimLeft(data));\n    cursor.setPosition(staticPosition + 32);\n    return [value, 32];\n}\nfunction prepareParameters({ checksumAddress, parameters, values, }) {\n    const preparedParameters = [];\n    for (let i = 0; i < parameters.length; i++) {\n        preparedParameters.push(prepareParameter({\n            checksumAddress,\n            parameter: parameters[i],\n            value: values[i],\n        }));\n    }\n    return preparedParameters;\n}\nfunction prepareParameter({ checksumAddress = false, parameter: parameter_, value, }) {\n    const parameter = parameter_;\n    const arrayComponents = getArrayComponents(parameter.type);\n    if (arrayComponents) {\n        const [length, type] = arrayComponents;\n        return encodeArray(value, {\n            checksumAddress,\n            length,\n            parameter: {\n                ...parameter,\n                type,\n            },\n        });\n    }\n    if (parameter.type === 'tuple') {\n        return encodeTuple(value, {\n            checksumAddress,\n            parameter: parameter,\n        });\n    }\n    if (parameter.type === 'address') {\n        return encodeAddress(value, {\n            checksum: checksumAddress,\n        });\n    }\n    if (parameter.type === 'bool') {\n        return encodeBoolean(value);\n    }\n    if (parameter.type.startsWith('uint') || parameter.type.startsWith('int')) {\n        const signed = parameter.type.startsWith('int');\n        const [, , size = '256'] = Solidity_js_1.integerRegex.exec(parameter.type) ?? [];\n        return encodeNumber(value, {\n            signed,\n            size: Number(size),\n        });\n    }\n    if (parameter.type.startsWith('bytes')) {\n        return encodeBytes(value, { type: parameter.type });\n    }\n    if (parameter.type === 'string') {\n        return encodeString(value);\n    }\n    throw new AbiParameters.InvalidTypeError(parameter.type);\n}\nfunction encode(preparedParameters) {\n    let staticSize = 0;\n    for (let i = 0; i < preparedParameters.length; i++) {\n        const { dynamic, encoded } = preparedParameters[i];\n        if (dynamic)\n            staticSize += 32;\n        else\n            staticSize += Hex.size(encoded);\n    }\n    const staticParameters = [];\n    const dynamicParameters = [];\n    let dynamicSize = 0;\n    for (let i = 0; i < preparedParameters.length; i++) {\n        const { dynamic, encoded } = preparedParameters[i];\n        if (dynamic) {\n            staticParameters.push(Hex.fromNumber(staticSize + dynamicSize, { size: 32 }));\n            dynamicParameters.push(encoded);\n            dynamicSize += Hex.size(encoded);\n        }\n        else {\n            staticParameters.push(encoded);\n        }\n    }\n    return Hex.concat(...staticParameters, ...dynamicParameters);\n}\nfunction encodeAddress(value, options) {\n    const { checksum = false } = options;\n    Address.assert(value, { strict: checksum });\n    return {\n        dynamic: false,\n        encoded: Hex.padLeft(value.toLowerCase()),\n    };\n}\nfunction encodeArray(value, options) {\n    const { checksumAddress, length, parameter } = options;\n    const dynamic = length === null;\n    if (!Array.isArray(value))\n        throw new AbiParameters.InvalidArrayError(value);\n    if (!dynamic && value.length !== length)\n        throw new AbiParameters.ArrayLengthMismatchError({\n            expectedLength: length,\n            givenLength: value.length,\n            type: `${parameter.type}[${length}]`,\n        });\n    let dynamicChild = false;\n    const preparedParameters = [];\n    for (let i = 0; i < value.length; i++) {\n        const preparedParam = prepareParameter({\n            checksumAddress,\n            parameter,\n            value: value[i],\n        });\n        if (preparedParam.dynamic)\n            dynamicChild = true;\n        preparedParameters.push(preparedParam);\n    }\n    if (dynamic || dynamicChild) {\n        const data = encode(preparedParameters);\n        if (dynamic) {\n            const length = Hex.fromNumber(preparedParameters.length, { size: 32 });\n            return {\n                dynamic: true,\n                encoded: preparedParameters.length > 0 ? Hex.concat(length, data) : length,\n            };\n        }\n        if (dynamicChild)\n            return { dynamic: true, encoded: data };\n    }\n    return {\n        dynamic: false,\n        encoded: Hex.concat(...preparedParameters.map(({ encoded }) => encoded)),\n    };\n}\nfunction encodeBytes(value, { type }) {\n    const [, parametersize] = type.split('bytes');\n    const bytesSize = Hex.size(value);\n    if (!parametersize) {\n        let value_ = value;\n        if (bytesSize % 32 !== 0)\n            value_ = Hex.padRight(value_, Math.ceil((value.length - 2) / 2 / 32) * 32);\n        return {\n            dynamic: true,\n            encoded: Hex.concat(Hex.padLeft(Hex.fromNumber(bytesSize, { size: 32 })), value_),\n        };\n    }\n    if (bytesSize !== Number.parseInt(parametersize))\n        throw new AbiParameters.BytesSizeMismatchError({\n            expectedSize: Number.parseInt(parametersize),\n            value,\n        });\n    return { dynamic: false, encoded: Hex.padRight(value) };\n}\nfunction encodeBoolean(value) {\n    if (typeof value !== 'boolean')\n        throw new Errors.BaseError(`Invalid boolean value: \"${value}\" (type: ${typeof value}). Expected: \\`true\\` or \\`false\\`.`);\n    return { dynamic: false, encoded: Hex.padLeft(Hex.fromBoolean(value)) };\n}\nfunction encodeNumber(value, { signed, size }) {\n    if (typeof size === 'number') {\n        const max = 2n ** (BigInt(size) - (signed ? 1n : 0n)) - 1n;\n        const min = signed ? -max - 1n : 0n;\n        if (value > max || value < min)\n            throw new Hex.IntegerOutOfRangeError({\n                max: max.toString(),\n                min: min.toString(),\n                signed,\n                size: size / 8,\n                value: value.toString(),\n            });\n    }\n    return {\n        dynamic: false,\n        encoded: Hex.fromNumber(value, {\n            size: 32,\n            signed,\n        }),\n    };\n}\nfunction encodeString(value) {\n    const hexValue = Hex.fromString(value);\n    const partsLength = Math.ceil(Hex.size(hexValue) / 32);\n    const parts = [];\n    for (let i = 0; i < partsLength; i++) {\n        parts.push(Hex.padRight(Hex.slice(hexValue, i * 32, (i + 1) * 32)));\n    }\n    return {\n        dynamic: true,\n        encoded: Hex.concat(Hex.padRight(Hex.fromNumber(Hex.size(hexValue), { size: 32 })), ...parts),\n    };\n}\nfunction encodeTuple(value, options) {\n    const { checksumAddress, parameter } = options;\n    let dynamic = false;\n    const preparedParameters = [];\n    for (let i = 0; i < parameter.components.length; i++) {\n        const param_ = parameter.components[i];\n        const index = Array.isArray(value) ? i : param_.name;\n        const preparedParam = prepareParameter({\n            checksumAddress,\n            parameter: param_,\n            value: value[index],\n        });\n        preparedParameters.push(preparedParam);\n        if (preparedParam.dynamic)\n            dynamic = true;\n    }\n    return {\n        dynamic,\n        encoded: dynamic\n            ? encode(preparedParameters)\n            : Hex.concat(...preparedParameters.map(({ encoded }) => encoded)),\n    };\n}\nfunction getArrayComponents(type) {\n    const matches = type.match(/^(.*)\\[(\\d+)?\\]$/);\n    return matches\n        ?\n            [matches[2] ? Number(matches[2]) : null, matches[1]]\n        : undefined;\n}\nfunction hasDynamicChild(param) {\n    const { type } = param;\n    if (type === 'string')\n        return true;\n    if (type === 'bytes')\n        return true;\n    if (type.endsWith('[]'))\n        return true;\n    if (type === 'tuple')\n        return param.components?.some(hasDynamicChild);\n    const arrayComponents = getArrayComponents(param.type);\n    if (arrayComponents &&\n        hasDynamicChild({\n            ...param,\n            type: arrayComponents[1],\n        }))\n        return true;\n    return false;\n}\n//# sourceMappingURL=abiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/abiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/bytes.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/bytes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.charCodeMap = void 0;\nexports.assertSize = assertSize;\nexports.assertStartOffset = assertStartOffset;\nexports.assertEndOffset = assertEndOffset;\nexports.charCodeToBase16 = charCodeToBase16;\nexports.pad = pad;\nexports.trim = trim;\nconst Bytes = __webpack_require__(/*! ../Bytes.js */ \"(ssr)/./node_modules/ox/_cjs/core/Bytes.js\");\nfunction assertSize(bytes, size_) {\n    if (Bytes.size(bytes) > size_)\n        throw new Bytes.SizeOverflowError({\n            givenSize: Bytes.size(bytes),\n            maxSize: size_,\n        });\n}\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > Bytes.size(value) - 1)\n        throw new Bytes.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: Bytes.size(value),\n        });\n}\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        Bytes.size(value) !== end - start) {\n        throw new Bytes.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: Bytes.size(value),\n        });\n    }\n}\nexports.charCodeMap = {\n    zero: 48,\n    nine: 57,\n    A: 65,\n    F: 70,\n    a: 97,\n    f: 102,\n};\nfunction charCodeToBase16(char) {\n    if (char >= exports.charCodeMap.zero && char <= exports.charCodeMap.nine)\n        return char - exports.charCodeMap.zero;\n    if (char >= exports.charCodeMap.A && char <= exports.charCodeMap.F)\n        return char - (exports.charCodeMap.A - 10);\n    if (char >= exports.charCodeMap.a && char <= exports.charCodeMap.f)\n        return char - (exports.charCodeMap.a - 10);\n    return undefined;\n}\nfunction pad(bytes, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return bytes;\n    if (bytes.length > size)\n        throw new Bytes.SizeExceedsPaddingSizeError({\n            size: bytes.length,\n            targetSize: size,\n            type: 'Bytes',\n        });\n    const paddedBytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n        const padEnd = dir === 'right';\n        paddedBytes[padEnd ? i : size - i - 1] =\n            bytes[padEnd ? i : bytes.length - i - 1];\n    }\n    return paddedBytes;\n}\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value;\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    return data;\n}\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/cursor.js":
/*!******************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/cursor.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RecursiveReadLimitExceededError = exports.PositionOutOfBoundsError = exports.NegativeOffsetError = void 0;\nexports.create = create;\nconst Errors = __webpack_require__(/*! ../Errors.js */ \"(ssr)/./node_modules/ox/_cjs/core/Errors.js\");\nconst staticCursor = {\n    bytes: new Uint8Array(),\n    dataView: new DataView(new ArrayBuffer(0)),\n    position: 0,\n    positionReadCount: new Map(),\n    recursiveReadCount: 0,\n    recursiveReadLimit: Number.POSITIVE_INFINITY,\n    assertReadLimit() {\n        if (this.recursiveReadCount >= this.recursiveReadLimit)\n            throw new RecursiveReadLimitExceededError({\n                count: this.recursiveReadCount + 1,\n                limit: this.recursiveReadLimit,\n            });\n    },\n    assertPosition(position) {\n        if (position < 0 || position > this.bytes.length - 1)\n            throw new PositionOutOfBoundsError({\n                length: this.bytes.length,\n                position,\n            });\n    },\n    decrementPosition(offset) {\n        if (offset < 0)\n            throw new NegativeOffsetError({ offset });\n        const position = this.position - offset;\n        this.assertPosition(position);\n        this.position = position;\n    },\n    getReadCount(position) {\n        return this.positionReadCount.get(position || this.position) || 0;\n    },\n    incrementPosition(offset) {\n        if (offset < 0)\n            throw new NegativeOffsetError({ offset });\n        const position = this.position + offset;\n        this.assertPosition(position);\n        this.position = position;\n    },\n    inspectByte(position_) {\n        const position = position_ ?? this.position;\n        this.assertPosition(position);\n        return this.bytes[position];\n    },\n    inspectBytes(length, position_) {\n        const position = position_ ?? this.position;\n        this.assertPosition(position + length - 1);\n        return this.bytes.subarray(position, position + length);\n    },\n    inspectUint8(position_) {\n        const position = position_ ?? this.position;\n        this.assertPosition(position);\n        return this.bytes[position];\n    },\n    inspectUint16(position_) {\n        const position = position_ ?? this.position;\n        this.assertPosition(position + 1);\n        return this.dataView.getUint16(position);\n    },\n    inspectUint24(position_) {\n        const position = position_ ?? this.position;\n        this.assertPosition(position + 2);\n        return ((this.dataView.getUint16(position) << 8) +\n            this.dataView.getUint8(position + 2));\n    },\n    inspectUint32(position_) {\n        const position = position_ ?? this.position;\n        this.assertPosition(position + 3);\n        return this.dataView.getUint32(position);\n    },\n    pushByte(byte) {\n        this.assertPosition(this.position);\n        this.bytes[this.position] = byte;\n        this.position++;\n    },\n    pushBytes(bytes) {\n        this.assertPosition(this.position + bytes.length - 1);\n        this.bytes.set(bytes, this.position);\n        this.position += bytes.length;\n    },\n    pushUint8(value) {\n        this.assertPosition(this.position);\n        this.bytes[this.position] = value;\n        this.position++;\n    },\n    pushUint16(value) {\n        this.assertPosition(this.position + 1);\n        this.dataView.setUint16(this.position, value);\n        this.position += 2;\n    },\n    pushUint24(value) {\n        this.assertPosition(this.position + 2);\n        this.dataView.setUint16(this.position, value >> 8);\n        this.dataView.setUint8(this.position + 2, value & ~4294967040);\n        this.position += 3;\n    },\n    pushUint32(value) {\n        this.assertPosition(this.position + 3);\n        this.dataView.setUint32(this.position, value);\n        this.position += 4;\n    },\n    readByte() {\n        this.assertReadLimit();\n        this._touch();\n        const value = this.inspectByte();\n        this.position++;\n        return value;\n    },\n    readBytes(length, size) {\n        this.assertReadLimit();\n        this._touch();\n        const value = this.inspectBytes(length);\n        this.position += size ?? length;\n        return value;\n    },\n    readUint8() {\n        this.assertReadLimit();\n        this._touch();\n        const value = this.inspectUint8();\n        this.position += 1;\n        return value;\n    },\n    readUint16() {\n        this.assertReadLimit();\n        this._touch();\n        const value = this.inspectUint16();\n        this.position += 2;\n        return value;\n    },\n    readUint24() {\n        this.assertReadLimit();\n        this._touch();\n        const value = this.inspectUint24();\n        this.position += 3;\n        return value;\n    },\n    readUint32() {\n        this.assertReadLimit();\n        this._touch();\n        const value = this.inspectUint32();\n        this.position += 4;\n        return value;\n    },\n    get remaining() {\n        return this.bytes.length - this.position;\n    },\n    setPosition(position) {\n        const oldPosition = this.position;\n        this.assertPosition(position);\n        this.position = position;\n        return () => (this.position = oldPosition);\n    },\n    _touch() {\n        if (this.recursiveReadLimit === Number.POSITIVE_INFINITY)\n            return;\n        const count = this.getReadCount();\n        this.positionReadCount.set(this.position, count + 1);\n        if (count > 0)\n            this.recursiveReadCount++;\n    },\n};\nfunction create(bytes, { recursiveReadLimit = 8_192 } = {}) {\n    const cursor = Object.create(staticCursor);\n    cursor.bytes = bytes;\n    cursor.dataView = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n    cursor.positionReadCount = new Map();\n    cursor.recursiveReadLimit = recursiveReadLimit;\n    return cursor;\n}\nclass NegativeOffsetError extends Errors.BaseError {\n    constructor({ offset }) {\n        super(`Offset \\`${offset}\\` cannot be negative.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Cursor.NegativeOffsetError'\n        });\n    }\n}\nexports.NegativeOffsetError = NegativeOffsetError;\nclass PositionOutOfBoundsError extends Errors.BaseError {\n    constructor({ length, position }) {\n        super(`Position \\`${position}\\` is out of bounds (\\`0 < position < ${length}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Cursor.PositionOutOfBoundsError'\n        });\n    }\n}\nexports.PositionOutOfBoundsError = PositionOutOfBoundsError;\nclass RecursiveReadLimitExceededError extends Errors.BaseError {\n    constructor({ count, limit }) {\n        super(`Recursive read limit of \\`${limit}\\` exceeded (recursive read count: \\`${count}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Cursor.RecursiveReadLimitExceededError'\n        });\n    }\n}\nexports.RecursiveReadLimitExceededError = RecursiveReadLimitExceededError;\n//# sourceMappingURL=cursor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/cursor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/errors.js":
/*!******************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/errors.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getUrl = getUrl;\nexports.getVersion = getVersion;\nexports.prettyPrint = prettyPrint;\nconst version_js_1 = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/ox/_cjs/core/version.js\");\nfunction getUrl(url) {\n    return url;\n}\nfunction getVersion() {\n    return version_js_1.version;\n}\nfunction prettyPrint(args) {\n    if (!args)\n        return '';\n    const entries = Object.entries(args)\n        .map(([key, value]) => {\n        if (value === undefined || value === false)\n            return null;\n        return [key, value];\n    })\n        .filter(Boolean);\n    const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0);\n    return entries\n        .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n        .join('\\n');\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL2ludGVybmFsL2Vycm9ycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxjQUFjO0FBQ2Qsa0JBQWtCO0FBQ2xCLG1CQUFtQjtBQUNuQixxQkFBcUIsbUJBQU8sQ0FBQyxtRUFBZTtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxHQUFHLElBQUksMkJBQTJCLEVBQUUsTUFBTTtBQUM5RTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcb3hcXF9janNcXGNvcmVcXGludGVybmFsXFxlcnJvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmdldFVybCA9IGdldFVybDtcbmV4cG9ydHMuZ2V0VmVyc2lvbiA9IGdldFZlcnNpb247XG5leHBvcnRzLnByZXR0eVByaW50ID0gcHJldHR5UHJpbnQ7XG5jb25zdCB2ZXJzaW9uX2pzXzEgPSByZXF1aXJlKFwiLi4vdmVyc2lvbi5qc1wiKTtcbmZ1bmN0aW9uIGdldFVybCh1cmwpIHtcbiAgICByZXR1cm4gdXJsO1xufVxuZnVuY3Rpb24gZ2V0VmVyc2lvbigpIHtcbiAgICByZXR1cm4gdmVyc2lvbl9qc18xLnZlcnNpb247XG59XG5mdW5jdGlvbiBwcmV0dHlQcmludChhcmdzKSB7XG4gICAgaWYgKCFhcmdzKVxuICAgICAgICByZXR1cm4gJyc7XG4gICAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGFyZ3MpXG4gICAgICAgIC5tYXAoKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gZmFsc2UpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgcmV0dXJuIFtrZXksIHZhbHVlXTtcbiAgICB9KVxuICAgICAgICAuZmlsdGVyKEJvb2xlYW4pO1xuICAgIGNvbnN0IG1heExlbmd0aCA9IGVudHJpZXMucmVkdWNlKChhY2MsIFtrZXldKSA9PiBNYXRoLm1heChhY2MsIGtleS5sZW5ndGgpLCAwKTtcbiAgICByZXR1cm4gZW50cmllc1xuICAgICAgICAubWFwKChba2V5LCB2YWx1ZV0pID0+IGAgICR7YCR7a2V5fTpgLnBhZEVuZChtYXhMZW5ndGggKyAxKX0gICR7dmFsdWV9YClcbiAgICAgICAgLmpvaW4oJ1xcbicpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXJyb3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/hex.js":
/*!***************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/hex.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assertSize = assertSize;\nexports.assertStartOffset = assertStartOffset;\nexports.assertEndOffset = assertEndOffset;\nexports.pad = pad;\nexports.trim = trim;\nconst Hex = __webpack_require__(/*! ../Hex.js */ \"(ssr)/./node_modules/ox/_cjs/core/Hex.js\");\nfunction assertSize(hex, size_) {\n    if (Hex.size(hex) > size_)\n        throw new Hex.SizeOverflowError({\n            givenSize: Hex.size(hex),\n            maxSize: size_,\n        });\n}\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > Hex.size(value) - 1)\n        throw new Hex.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: Hex.size(value),\n        });\n}\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        Hex.size(value) !== end - start) {\n        throw new Hex.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: Hex.size(value),\n        });\n    }\n}\nfunction pad(hex_, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return hex_;\n    const hex = hex_.replace('0x', '');\n    if (hex.length > size * 2)\n        throw new Hex.SizeExceedsPaddingSizeError({\n            size: Math.ceil(hex.length / 2),\n            targetSize: size,\n            type: 'Hex',\n        });\n    return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}`;\n}\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value.replace('0x', '');\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    if (data === '0')\n        return '0x';\n    if (dir === 'right' && data.length % 2 === 1)\n        return `0x${data}0`;\n    return `0x${data}`;\n}\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/internal/lru.js":
/*!***************************************************!*\
  !*** ./node_modules/ox/_cjs/core/internal/lru.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LruMap = void 0;\nclass LruMap extends Map {\n    constructor(size) {\n        super();\n        Object.defineProperty(this, \"maxSize\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxSize = size;\n    }\n    get(key) {\n        const value = super.get(key);\n        if (super.has(key) && value !== undefined) {\n            this.delete(key);\n            super.set(key, value);\n        }\n        return value;\n    }\n    set(key, value) {\n        super.set(key, value);\n        if (this.maxSize && this.size > this.maxSize) {\n            const firstKey = this.keys().next().value;\n            if (firstKey)\n                this.delete(firstKey);\n        }\n        return this;\n    }\n}\nexports.LruMap = LruMap;\n//# sourceMappingURL=lru.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL2ludGVybmFsL2xydS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXG94XFxfY2pzXFxjb3JlXFxpbnRlcm5hbFxcbHJ1LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5McnVNYXAgPSB2b2lkIDA7XG5jbGFzcyBMcnVNYXAgZXh0ZW5kcyBNYXAge1xuICAgIGNvbnN0cnVjdG9yKHNpemUpIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibWF4U2l6ZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogdm9pZCAwXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLm1heFNpemUgPSBzaXplO1xuICAgIH1cbiAgICBnZXQoa2V5KSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gc3VwZXIuZ2V0KGtleSk7XG4gICAgICAgIGlmIChzdXBlci5oYXMoa2V5KSAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLmRlbGV0ZShrZXkpO1xuICAgICAgICAgICAgc3VwZXIuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgc2V0KGtleSwgdmFsdWUpIHtcbiAgICAgICAgc3VwZXIuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICBpZiAodGhpcy5tYXhTaXplICYmIHRoaXMuc2l6ZSA+IHRoaXMubWF4U2l6ZSkge1xuICAgICAgICAgICAgY29uc3QgZmlyc3RLZXkgPSB0aGlzLmtleXMoKS5uZXh0KCkudmFsdWU7XG4gICAgICAgICAgICBpZiAoZmlyc3RLZXkpXG4gICAgICAgICAgICAgICAgdGhpcy5kZWxldGUoZmlyc3RLZXkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbn1cbmV4cG9ydHMuTHJ1TWFwID0gTHJ1TWFwO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bHJ1LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/internal/lru.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_cjs/core/version.js":
/*!**********************************************!*\
  !*** ./node_modules/ox/_cjs/core/version.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '0.1.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2Nqcy9jb3JlL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZUFBZTtBQUNmLGVBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxveFxcX2Nqc1xcY29yZVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudmVyc2lvbiA9IHZvaWQgMDtcbmV4cG9ydHMudmVyc2lvbiA9ICcwLjEuMSc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_cjs/core/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/BlockOverrides.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_esm/core/BlockOverrides.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromRpc: () => (/* binding */ fromRpc),\n/* harmony export */   toRpc: () => (/* binding */ toRpc)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n/* harmony import */ var _Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Withdrawal.js */ \"(ssr)/./node_modules/ox/_esm/core/Withdrawal.js\");\n\n\n/**\n * Converts an {@link ox#BlockOverrides.Rpc} to an {@link ox#BlockOverrides.BlockOverrides}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.fromRpc({\n *   baseFeePerGas: '0x1',\n *   blobBaseFee: '0x2',\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: '0x4',\n *   number: '0x5',\n *   prevRandao: '0x6',\n *   time: '0x1234567890',\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: '0x1',\n *       index: '0x0',\n *       validatorIndex: '0x1',\n *     },\n *   ],\n * })\n * ```\n *\n * @param rpcBlockOverrides - The RPC block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.BlockOverrides}.\n */\nfunction fromRpc(rpcBlockOverrides) {\n    return {\n        ...(rpcBlockOverrides.baseFeePerGas && {\n            baseFeePerGas: BigInt(rpcBlockOverrides.baseFeePerGas),\n        }),\n        ...(rpcBlockOverrides.blobBaseFee && {\n            blobBaseFee: BigInt(rpcBlockOverrides.blobBaseFee),\n        }),\n        ...(rpcBlockOverrides.feeRecipient && {\n            feeRecipient: rpcBlockOverrides.feeRecipient,\n        }),\n        ...(rpcBlockOverrides.gasLimit && {\n            gasLimit: BigInt(rpcBlockOverrides.gasLimit),\n        }),\n        ...(rpcBlockOverrides.number && {\n            number: BigInt(rpcBlockOverrides.number),\n        }),\n        ...(rpcBlockOverrides.prevRandao && {\n            prevRandao: BigInt(rpcBlockOverrides.prevRandao),\n        }),\n        ...(rpcBlockOverrides.time && {\n            time: BigInt(rpcBlockOverrides.time),\n        }),\n        ...(rpcBlockOverrides.withdrawals && {\n            withdrawals: rpcBlockOverrides.withdrawals.map(_Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__.fromRpc),\n        }),\n    };\n}\n/**\n * Converts an {@link ox#BlockOverrides.BlockOverrides} to an {@link ox#BlockOverrides.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.toRpc({\n *   baseFeePerGas: 1n,\n *   blobBaseFee: 2n,\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: 4n,\n *   number: 5n,\n *   prevRandao: 6n,\n *   time: 78187493520n,\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: 1n,\n *       index: 0,\n *       validatorIndex: 1,\n *     },\n *   ],\n * })\n * ```\n *\n * @param blockOverrides - The block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.Rpc}.\n */\nfunction toRpc(blockOverrides) {\n    return {\n        ...(typeof blockOverrides.baseFeePerGas === 'bigint' && {\n            baseFeePerGas: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.baseFeePerGas),\n        }),\n        ...(typeof blockOverrides.blobBaseFee === 'bigint' && {\n            blobBaseFee: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.blobBaseFee),\n        }),\n        ...(typeof blockOverrides.feeRecipient === 'string' && {\n            feeRecipient: blockOverrides.feeRecipient,\n        }),\n        ...(typeof blockOverrides.gasLimit === 'bigint' && {\n            gasLimit: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.gasLimit),\n        }),\n        ...(typeof blockOverrides.number === 'bigint' && {\n            number: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.number),\n        }),\n        ...(typeof blockOverrides.prevRandao === 'bigint' && {\n            prevRandao: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.prevRandao),\n        }),\n        ...(typeof blockOverrides.time === 'bigint' && {\n            time: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.time),\n        }),\n        ...(blockOverrides.withdrawals && {\n            withdrawals: blockOverrides.withdrawals.map(_Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__.toRpc),\n        }),\n    };\n}\n//# sourceMappingURL=BlockOverrides.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/BlockOverrides.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Bytes.js":
/*!********************************************!*\
  !*** ./node_modules/ox/_esm/core/Bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidBytesBooleanError: () => (/* binding */ InvalidBytesBooleanError),\n/* harmony export */   InvalidBytesTypeError: () => (/* binding */ InvalidBytesTypeError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   fromArray: () => (/* binding */ fromArray),\n/* harmony export */   fromBoolean: () => (/* binding */ fromBoolean),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   fromNumber: () => (/* binding */ fromNumber),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   padLeft: () => (/* binding */ padLeft),\n/* harmony export */   padRight: () => (/* binding */ padRight),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toBigInt: () => (/* binding */ toBigInt),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _Errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_esm/core/Errors.js\");\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n/* harmony import */ var _Json_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_esm/core/Json.js\");\n/* harmony import */ var _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\");\n/* harmony import */ var _internal_hex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/hex.js\");\n\n\n\n\n\n\nconst decoder = /*#__PURE__*/ new TextDecoder();\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Asserts if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.assert('abc')\n * // @error: Bytes.InvalidBytesTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid Bytes value.\n * // @error: Bytes values must be of type `Uint8Array`.\n * ```\n *\n * @param value - Value to assert.\n */\nfunction assert(value) {\n    if (value instanceof Uint8Array)\n        return;\n    if (!value)\n        throw new InvalidBytesTypeError(value);\n    if (typeof value !== 'object')\n        throw new InvalidBytesTypeError(value);\n    if (!('BYTES_PER_ELEMENT' in value))\n        throw new InvalidBytesTypeError(value);\n    if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n        throw new InvalidBytesTypeError(value);\n}\n/**\n * Concatenates two or more {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.concat(\n *   Bytes.from([1]),\n *   Bytes.from([69]),\n *   Bytes.from([420, 69]),\n * )\n * // @log: Uint8Array [ 1, 69, 420, 69 ]\n * ```\n *\n * @param values - Values to concatenate.\n * @returns Concatenated {@link ox#Bytes.Bytes}.\n */\nfunction concat(...values) {\n    let length = 0;\n    for (const arr of values) {\n        length += arr.length;\n    }\n    const result = new Uint8Array(length);\n    for (let i = 0, index = 0; i < values.length; i++) {\n        const arr = values[i];\n        result.set(arr, index);\n        index += arr.length;\n    }\n    return result;\n}\n/**\n * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Bytes.fromBoolean`\n *\n * - `Bytes.fromString`\n *\n * - `Bytes.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.from([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n *\n * const data = Bytes.from('0xdeadbeef')\n * // @log: Uint8Array([222, 173, 190, 239])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return value;\n    if (typeof value === 'string')\n        return fromHex(value);\n    return fromArray(value);\n}\n/**\n * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromArray([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nfunction fromArray(value) {\n    return value instanceof Uint8Array ? value : new Uint8Array(value);\n}\n/**\n * Encodes a boolean value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true)\n * // @log: Uint8Array([1])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true, { size: 32 })\n * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n * ```\n *\n * @param value - Boolean value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromBoolean(value, options = {}) {\n    const { size } = options;\n    const bytes = new Uint8Array(1);\n    bytes[0] = Number(value);\n    if (typeof size === 'number') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n        return padLeft(bytes, size);\n    }\n    return bytes;\n}\n/**\n * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Hex.Hex} value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromHex(value, options = {}) {\n    const { size } = options;\n    let hex = value;\n    if (size) {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize(value, size);\n        hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.padRight(value, size);\n    }\n    let hexString = hex.slice(2);\n    if (hexString.length % 2)\n        hexString = `0${hexString}`;\n    const length = hexString.length / 2;\n    const bytes = new Uint8Array(length);\n    for (let index = 0, j = 0; index < length; index++) {\n        const nibbleLeft = _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.charCodeToBase16(hexString.charCodeAt(j++));\n        const nibbleRight = _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.charCodeToBase16(hexString.charCodeAt(j++));\n        if (nibbleLeft === undefined || nibbleRight === undefined) {\n            throw new _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError(`Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`);\n        }\n        bytes[index] = nibbleLeft * 16 + nibbleRight;\n    }\n    return bytes;\n}\n/**\n * Encodes a number value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420)\n * // @log: Uint8Array([1, 164])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420, { size: 4 })\n * // @log: Uint8Array([0, 0, 1, 164])\n * ```\n *\n * @param value - Number value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromNumber(value, options) {\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromNumber(value, options);\n    return fromHex(hex);\n}\n/**\n * Encodes a string into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - String to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromString(value, options = {}) {\n    const { size } = options;\n    const bytes = encoder.encode(value);\n    if (typeof size === 'number') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n        return padRight(bytes, size);\n    }\n    return bytes;\n}\n/**\n * Checks if two {@link ox#Bytes.Bytes} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))\n * // @log: true\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))\n * // @log: false\n * ```\n *\n * @param bytesA - First {@link ox#Bytes.Bytes} value.\n * @param bytesB - Second {@link ox#Bytes.Bytes} value.\n * @returns `true` if the two values are equal, otherwise `false`.\n */\nfunction isEqual(bytesA, bytesB) {\n    return (0,_noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_4__.equalBytes)(bytesA, bytesB);\n}\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.from([1]), 4)\n * // @log: Uint8Array([0, 0, 0, 1])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nfunction padLeft(value, size) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'left', size });\n}\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padRight(Bytes.from([1]), 4)\n * // @log: Uint8Array([1, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nfunction padRight(value, size) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'right', size });\n}\n/**\n * Generates random {@link ox#Bytes.Bytes} of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.random(32)\n * // @log: Uint8Array([... x32])\n * ```\n *\n * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.\n * @returns Random {@link ox#Bytes.Bytes} of the specified length.\n */\nfunction random(length) {\n    return crypto.getRandomValues(new Uint8Array(length));\n}\n/**\n * Retrieves the size of a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.size(Bytes.from([1, 2, 3, 4]))\n * // @log: 4\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Size of the {@link ox#Bytes.Bytes} value.\n */\nfunction size(value) {\n    return value.length;\n}\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(\n *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),\n *   1,\n *   4,\n * )\n * // @log: Uint8Array([2, 3, 4])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value.\n * @param start - Start offset.\n * @param end - End offset.\n * @param options - Slice options.\n * @returns Sliced {@link ox#Bytes.Bytes} value.\n */\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertStartOffset(value, start);\n    const value_ = value.slice(start, end);\n    if (strict)\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertEndOffset(value_, start, end);\n    return value_;\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a bigint.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBigInt(Bytes.from([1, 164]))\n * // @log: 420n\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded bigint.\n */\nfunction toBigInt(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(bytes, options);\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.toBigInt(hex, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a boolean.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([1]))\n * // @log: true\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded boolean.\n */\nfunction toBoolean(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes_, size);\n        bytes_ = trimLeft(bytes_);\n    }\n    if (bytes_.length > 1 || bytes_[0] > 1)\n        throw new InvalidBytesBooleanError(bytes_);\n    return Boolean(bytes_[0]);\n}\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded {@link ox#Hex.Hex} value.\n */\nfunction toHex(value, options = {}) {\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(value, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a number.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toNumber(Bytes.from([1, 164]))\n * // @log: 420\n * ```\n */\nfunction toNumber(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(bytes, options);\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.toNumber(hex, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a string.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: 'Hello world'\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded string.\n */\nfunction toString(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes_, size);\n        bytes_ = trimRight(bytes_);\n    }\n    return decoder.decode(bytes_);\n}\n/**\n * Trims leading zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nfunction trimLeft(value) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'left' });\n}\n/**\n * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nfunction trimRight(value) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'right' });\n}\n/**\n * Checks if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.validate('0x')\n * // @log: false\n *\n * Bytes.validate(Bytes.from([1, 2, 3]))\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.\n */\nfunction validate(value) {\n    try {\n        assert(value);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Thrown when the bytes value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([5]))\n * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.\n * // @error: The bytes array must contain a single byte of either a `0` or `1` value.\n * ```\n */\nclass InvalidBytesBooleanError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor(bytes) {\n        super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The bytes array must contain a single byte of either a `0` or `1` value.',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesBooleanError'\n        });\n    }\n}\n/**\n * Thrown when a value cannot be converted to bytes.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * Bytes.from('foo')\n * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.\n * ```\n */\nclass InvalidBytesTypeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? _Json_js__WEBPACK_IMPORTED_MODULE_5__.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`, {\n            metaMessages: ['Bytes values must be of type `Bytes`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesTypeError'\n        });\n    }\n}\n/**\n * Thrown when a size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromString('Hello World!', { size: 8 })\n * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nclass SizeOverflowError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeOverflowError'\n        });\n    }\n}\n/**\n * Thrown when a slice offset is out-of-bounds.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(Bytes.from([1, 2, 3]), 4)\n * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).\n * ```\n */\nclass SliceOffsetOutOfBoundsError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\n/**\n * Thrown when a the padding size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)\n * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).\n * ```\n */\nclass SizeExceedsPaddingSizeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeExceedsPaddingSizeError'\n        });\n    }\n}\n//# sourceMappingURL=Bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Errors.js":
/*!*********************************************!*\
  !*** ./node_modules/ox/_esm/core/Errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _internal_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/errors.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/errors.js\");\n\n/**\n * Base error class inherited by all errors thrown by ox.\n *\n * @example\n * ```ts\n * import { Errors } from 'ox'\n * throw new Errors.BaseError('An error occurred')\n * ```\n */\nclass BaseError extends Error {\n    constructor(shortMessage, options = {}) {\n        const details = (() => {\n            if (options.cause instanceof BaseError) {\n                if (options.cause.details)\n                    return options.cause.details;\n                if (options.cause.shortMessage)\n                    return options.cause.shortMessage;\n            }\n            if (options.cause &&\n                'details' in options.cause &&\n                typeof options.cause.details === 'string')\n                return options.cause.details;\n            if (options.cause?.message)\n                return options.cause.message;\n            return options.details;\n        })();\n        const docsPath = (() => {\n            if (options.cause instanceof BaseError)\n                return options.cause.docsPath || options.docsPath;\n            return options.docsPath;\n        })();\n        const docsBaseUrl = 'https://oxlib.sh';\n        const docs = `${docsBaseUrl}${docsPath ?? ''}`;\n        const message = [\n            shortMessage || 'An error occurred.',\n            ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n            ...(details || docsPath\n                ? [\n                    '',\n                    details ? `Details: ${details}` : undefined,\n                    docsPath ? `See: ${docs}` : undefined,\n                ]\n                : []),\n        ]\n            .filter((x) => typeof x === 'string')\n            .join('\\n');\n        super(message, options.cause ? { cause: options.cause } : undefined);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"cause\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'BaseError'\n        });\n        Object.defineProperty(this, \"version\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: `ox@${(0,_internal_errors_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)()}`\n        });\n        this.cause = options.cause;\n        this.details = details;\n        this.docs = docs;\n        this.docsPath = docsPath;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return walk(this, fn);\n    }\n}\n/** @internal */\nfunction walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err && typeof err === 'object' && 'cause' in err && err.cause)\n        return walk(err.cause, fn);\n    return fn ? null : err;\n}\n//# sourceMappingURL=Errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Hex.js":
/*!******************************************!*\
  !*** ./node_modules/ox/_esm/core/Hex.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegerOutOfRangeError: () => (/* binding */ IntegerOutOfRangeError),\n/* harmony export */   InvalidHexBooleanError: () => (/* binding */ InvalidHexBooleanError),\n/* harmony export */   InvalidHexTypeError: () => (/* binding */ InvalidHexTypeError),\n/* harmony export */   InvalidHexValueError: () => (/* binding */ InvalidHexValueError),\n/* harmony export */   InvalidLengthError: () => (/* binding */ InvalidLengthError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   fromBoolean: () => (/* binding */ fromBoolean),\n/* harmony export */   fromBytes: () => (/* binding */ fromBytes),\n/* harmony export */   fromNumber: () => (/* binding */ fromNumber),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   padLeft: () => (/* binding */ padLeft),\n/* harmony export */   padRight: () => (/* binding */ padRight),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toBigInt: () => (/* binding */ toBigInt),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _Bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/Bytes.js\");\n/* harmony import */ var _Errors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_esm/core/Errors.js\");\n/* harmony import */ var _Json_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_esm/core/Json.js\");\n/* harmony import */ var _internal_bytes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\");\n/* harmony import */ var _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/hex.js\");\n\n\n\n\n\n\nconst encoder = /*#__PURE__*/ new TextEncoder();\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) => i.toString(16).padStart(2, '0'));\n/**\n * Asserts if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('abc')\n * // @error: InvalidHexValueTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid hex type.\n * // @error: Hex types must be represented as `\"0x\\${string}\"`.\n * ```\n *\n * @param value - The value to assert.\n * @param options - Options.\n */\nfunction assert(value, options = {}) {\n    const { strict = false } = options;\n    if (!value)\n        throw new InvalidHexTypeError(value);\n    if (typeof value !== 'string')\n        throw new InvalidHexTypeError(value);\n    if (strict) {\n        if (!/^0x[0-9a-fA-F]*$/.test(value))\n            throw new InvalidHexValueError(value);\n    }\n    if (!value.startsWith('0x'))\n        throw new InvalidHexValueError(value);\n}\n/**\n * Concatenates two or more {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.concat('0x123', '0x456')\n * // @log: '0x123456'\n * ```\n *\n * @param values - The {@link ox#Hex.Hex} values to concatenate.\n * @returns The concatenated {@link ox#Hex.Hex} value.\n */\nfunction concat(...values) {\n    return `0x${values.reduce((acc, x) => acc + x.replace('0x', ''), '')}`;\n}\n/**\n * Instantiates a {@link ox#Hex.Hex} value from a hex string or {@link ox#Bytes.Bytes} value.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Hex.fromBoolean`\n *\n * - `Hex.fromString`\n *\n * - `Hex.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.from('0x48656c6c6f20576f726c6421')\n * // @log: '0x48656c6c6f20576f726c6421'\n *\n * Hex.from(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return fromBytes(value);\n    if (Array.isArray(value))\n        return fromBytes(new Uint8Array(value));\n    return value;\n}\n/**\n * Encodes a boolean into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromBoolean(true)\n * // @log: '0x1'\n *\n * Hex.fromBoolean(false)\n * // @log: '0x0'\n *\n * Hex.fromBoolean(true, { size: 32 })\n * // @log: '0x0000000000000000000000000000000000000000000000000000000000000001'\n * ```\n *\n * @param value - The boolean value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromBoolean(value, options = {}) {\n    const hex = `0x${Number(value)}`;\n    if (typeof options.size === 'number') {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n        return padLeft(hex, options.size);\n    }\n    return hex;\n}\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.fromBytes(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromBytes(value, options = {}) {\n    let string = '';\n    for (let i = 0; i < value.length; i++)\n        string += hexes[value[i]];\n    const hex = `0x${string}`;\n    if (typeof options.size === 'number') {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n        return padRight(hex, options.size);\n    }\n    return hex;\n}\n/**\n * Encodes a number or bigint into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420)\n * // @log: '0x1a4'\n *\n * Hex.fromNumber(420, { size: 32 })\n * // @log: '0x00000000000000000000000000000000000000000000000000000000000001a4'\n * ```\n *\n * @param value - The number or bigint value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromNumber(value, options = {}) {\n    const { signed, size } = options;\n    const value_ = BigInt(value);\n    let maxValue;\n    if (size) {\n        if (signed)\n            maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n        else\n            maxValue = 2n ** (BigInt(size) * 8n) - 1n;\n    }\n    else if (typeof value === 'number') {\n        maxValue = BigInt(Number.MAX_SAFE_INTEGER);\n    }\n    const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0;\n    if ((maxValue && value_ > maxValue) || value_ < minValue) {\n        const suffix = typeof value === 'bigint' ? 'n' : '';\n        throw new IntegerOutOfRangeError({\n            max: maxValue ? `${maxValue}${suffix}` : undefined,\n            min: `${minValue}${suffix}`,\n            signed,\n            size,\n            value: `${value}${suffix}`,\n        });\n    }\n    const stringValue = (signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_).toString(16);\n    const hex = `0x${stringValue}`;\n    if (size)\n        return padLeft(hex, size);\n    return hex;\n}\n/**\n * Encodes a string into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n * Hex.fromString('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * Hex.fromString('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n * ```\n *\n * @param value - The string value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromString(value, options = {}) {\n    return fromBytes(encoder.encode(value), options);\n}\n/**\n * Checks if two {@link ox#Hex.Hex} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.isEqual('0xdeadbeef', '0xdeadbeef')\n * // @log: true\n *\n * Hex.isEqual('0xda', '0xba')\n * // @log: false\n * ```\n *\n * @param hexA - The first {@link ox#Hex.Hex} value.\n * @param hexB - The second {@link ox#Hex.Hex} value.\n * @returns `true` if the two {@link ox#Hex.Hex} values are equal, `false` otherwise.\n */\nfunction isEqual(hexA, hexB) {\n    return (0,_noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_1__.equalBytes)(_Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hexA), _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hexB));\n}\n/**\n * Pads a {@link ox#Hex.Hex} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1234', 4)\n * // @log: '0x00001234'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nfunction padLeft(value, size) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'left', size });\n}\n/**\n * Pads a {@link ox#Hex.Hex} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts\n * import { Hex } from 'ox'\n *\n * Hex.padRight('0x1234', 4)\n * // @log: '0x12340000'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nfunction padRight(value, size) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'right', size });\n}\n/**\n * Generates a random {@link ox#Hex.Hex} value of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const hex = Hex.random(32)\n * // @log: '0x...'\n * ```\n *\n * @returns Random {@link ox#Hex.Hex} value.\n */\nfunction random(length) {\n    return fromBytes(_Bytes_js__WEBPACK_IMPORTED_MODULE_2__.random(length));\n}\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 1, 4)\n * // @log: '0x234567'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to slice.\n * @param start - The start offset (in bytes).\n * @param end - The end offset (in bytes).\n * @param options - Options.\n * @returns The sliced {@link ox#Hex.Hex} value.\n */\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertStartOffset(value, start);\n    const value_ = `0x${value\n        .replace('0x', '')\n        .slice((start ?? 0) * 2, (end ?? value.length) * 2)}`;\n    if (strict)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertEndOffset(value_, start, end);\n    return value_;\n}\n/**\n * Retrieves the size of a {@link ox#Hex.Hex} value (in bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.size('0xdeadbeef')\n * // @log: 4\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to get the size of.\n * @returns The size of the {@link ox#Hex.Hex} value (in bytes).\n */\nfunction size(value) {\n    return Math.ceil((value.length - 2) / 2);\n}\n/**\n * Trims leading zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimLeft('0x00000000deadbeef')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nfunction trimLeft(value) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'left' });\n}\n/**\n * Trims trailing zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimRight('0xdeadbeef00000000')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nfunction trimRight(value) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'right' });\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a BigInt.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBigInt('0x1a4')\n * // @log: 420n\n *\n * Hex.toBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420n\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded BigInt.\n */\nfunction toBigInt(hex, options = {}) {\n    const { signed } = options;\n    if (options.size)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n    const value = BigInt(hex);\n    if (!signed)\n        return value;\n    const size = (hex.length - 2) / 2;\n    const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n;\n    const max_signed = max_unsigned >> 1n;\n    if (value <= max_signed)\n        return value;\n    return value - max_unsigned - 1n;\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0x01')\n * // @log: true\n *\n * Hex.toBoolean('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // @log: true\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded boolean.\n */\nfunction toBoolean(hex, options = {}) {\n    if (options.size)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n    const hex_ = trimLeft(hex);\n    if (hex_ === '0x')\n        return false;\n    if (hex_ === '0x1')\n        return true;\n    throw new InvalidHexBooleanError(hex);\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const data = Hex.toBytes('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded {@link ox#Bytes.Bytes}.\n */\nfunction toBytes(hex, options = {}) {\n    return _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hex, options);\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a number.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toNumber('0x1a4')\n * // @log: 420\n *\n * Hex.toNumber('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded number.\n */\nfunction toNumber(hex, options = {}) {\n    const { signed, size } = options;\n    if (!signed && !size)\n        return Number(hex);\n    return Number(toBigInt(hex, options));\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a string.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toString('0x48656c6c6f20576f726c6421')\n * // @log: 'Hello world!'\n *\n * Hex.toString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // @log: 'Hello world'\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded string.\n */\nfunction toString(hex, options = {}) {\n    const { size } = options;\n    let bytes = _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hex);\n    if (size) {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_3__.assertSize(bytes, size);\n        bytes = _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.trimRight(bytes);\n    }\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Checks if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.validate('0xdeadbeef')\n * // @log: true\n *\n * Hex.validate(Bytes.from([1, 2, 3]))\n * // @log: false\n * ```\n *\n * @param value - The value to check.\n * @param options - Options.\n * @returns `true` if the value is a {@link ox#Hex.Hex}, `false` otherwise.\n */\nfunction validate(value, options = {}) {\n    const { strict = false } = options;\n    try {\n        assert(value, { strict });\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Thrown when the provided integer is out of range, and cannot be represented as a hex value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420182738912731283712937129)\n * // @error: Hex.IntegerOutOfRangeError: Number \\`4.2018273891273126e+26\\` is not in safe unsigned integer range (`0` to `9007199254740991`)\n * ```\n */\nclass IntegerOutOfRangeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ max, min, signed, size, value, }) {\n        super(`Number \\`${value}\\` is not in safe${size ? ` ${size * 8}-bit` : ''}${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.IntegerOutOfRangeError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0xa')\n * // @error: Hex.InvalidHexBooleanError: Hex value `\"0xa\"` is not a valid boolean.\n * // @error: The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).\n * ```\n */\nclass InvalidHexBooleanError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(hex) {\n        super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexBooleanError'\n        });\n    }\n}\n/**\n * Thrown when the provided value is not a valid hex type.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert(1)\n * // @error: Hex.InvalidHexTypeError: Value `1` of type `number` is an invalid hex type.\n * ```\n */\nclass InvalidHexTypeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? _Json_js__WEBPACK_IMPORTED_MODULE_5__.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`, {\n            metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexTypeError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('0x0123456789abcdefg')\n * // @error: Hex.InvalidHexValueError: Value `0x0123456789abcdefg` is an invalid hex value.\n * // @error: Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).\n * ```\n */\nclass InvalidHexValueError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Value \\`${value}\\` is an invalid hex value.`, {\n            metaMessages: [\n                'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexValueError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value is an odd length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromHex('0xabcde')\n * // @error: Hex.InvalidLengthError: Hex value `\"0xabcde\"` is an odd length (5 nibbles).\n * ```\n */\nclass InvalidLengthError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`, {\n            metaMessages: ['It must be an even length.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidLengthError'\n        });\n    }\n}\n/**\n * Thrown when the size of the value exceeds the expected max size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromString('Hello World!', { size: 8 })\n * // @error: Hex.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nclass SizeOverflowError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeOverflowError'\n        });\n    }\n}\n/**\n * Thrown when the slice offset exceeds the bounds of the value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 6)\n * // @error: Hex.SliceOffsetOutOfBoundsError: Slice starting at offset `6` is out-of-bounds (size: `5`).\n * ```\n */\nclass SliceOffsetOutOfBoundsError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\n/**\n * Thrown when the size of the value exceeds the pad size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1a4e12a45a21323123aaa87a897a897a898a6567a578a867a98778a667a85a875a87a6a787a65a675a6a9', 32)\n * // @error: Hex.SizeExceedsPaddingSizeError: Hex size (`43`) exceeds padding size (`32`).\n * ```\n */\nclass SizeExceedsPaddingSizeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeExceedsPaddingSizeError'\n        });\n    }\n}\n//# sourceMappingURL=Hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Json.js":
/*!*******************************************!*\
  !*** ./node_modules/ox/_esm/core/Json.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\nconst bigIntSuffix = '#__bigint';\n/**\n * Parses a JSON string, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.parse('{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}')\n * // @log: {\n * // @log:   foo: 'bar',\n * // @log:   baz: 69420694206942069420694206942069420694206942069420n\n * // @log: }\n * ```\n *\n * @param string - The value to parse.\n * @param reviver - A function that transforms the results.\n * @returns The parsed value.\n */\nfunction parse(string, reviver) {\n    return JSON.parse(string, (key, value_) => {\n        const value = value_;\n        if (typeof value === 'string' && value.endsWith(bigIntSuffix))\n            return BigInt(value.slice(0, -bigIntSuffix.length));\n        return typeof reviver === 'function' ? reviver(key, value) : value;\n    });\n}\n/**\n * Stringifies a value to its JSON representation, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.stringify({\n *   foo: 'bar',\n *   baz: 69420694206942069420694206942069420694206942069420n,\n * })\n * // @log: '{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}'\n * ```\n *\n * @param value - The value to stringify.\n * @param replacer - A function that transforms the results. It is passed the key and value of the property, and must return the value to be used in the JSON string. If this function returns `undefined`, the property is not included in the resulting JSON string.\n * @param space - A string or number that determines the indentation of the JSON string. If it is a number, it indicates the number of spaces to use as indentation; if it is a string (e.g. `'\\t'`), it uses the string as the indentation character.\n * @returns The JSON string.\n */\nfunction stringify(value, replacer, space) {\n    return JSON.stringify(value, (key, value) => {\n        if (typeof replacer === 'function')\n            return replacer(key, value);\n        if (typeof value === 'bigint')\n            return value.toString() + bigIntSuffix;\n        return value;\n    }, space);\n}\n//# sourceMappingURL=Json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Withdrawal.js":
/*!*************************************************!*\
  !*** ./node_modules/ox/_esm/core/Withdrawal.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromRpc: () => (/* binding */ fromRpc),\n/* harmony export */   toRpc: () => (/* binding */ toRpc)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n\n/**\n * Converts a {@link ox#Withdrawal.Rpc} to an {@link ox#Withdrawal.Withdrawal}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.fromRpc({\n *   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n *   amount: '0x620323',\n *   index: '0x0',\n *   validatorIndex: '0x1',\n * })\n * // @log: {\n * // @log:   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n * // @log:   amount: 6423331n,\n * // @log:   index: 0,\n * // @log:   validatorIndex: 1\n * // @log: }\n * ```\n *\n * @param withdrawal - The RPC withdrawal to convert.\n * @returns An instantiated {@link ox#Withdrawal.Withdrawal}.\n */\nfunction fromRpc(withdrawal) {\n    return {\n        ...withdrawal,\n        amount: BigInt(withdrawal.amount),\n        index: Number(withdrawal.index),\n        validatorIndex: Number(withdrawal.validatorIndex),\n    };\n}\n/**\n * Converts a {@link ox#Withdrawal.Withdrawal} to an {@link ox#Withdrawal.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.toRpc({\n *   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n *   amount: 6423331n,\n *   index: 0,\n *   validatorIndex: 1,\n * })\n * // @log: {\n * // @log:   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n * // @log:   amount: '0x620323',\n * // @log:   index: '0x0',\n * // @log:   validatorIndex: '0x1',\n * // @log: }\n * ```\n *\n * @param withdrawal - The Withdrawal to convert.\n * @returns An RPC Withdrawal.\n */\nfunction toRpc(withdrawal) {\n    return {\n        address: withdrawal.address,\n        amount: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.amount),\n        index: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.index),\n        validatorIndex: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.validatorIndex),\n    };\n}\n//# sourceMappingURL=Withdrawal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Withdrawal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/bytes.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/bytes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEndOffset: () => (/* binding */ assertEndOffset),\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   assertStartOffset: () => (/* binding */ assertStartOffset),\n/* harmony export */   charCodeMap: () => (/* binding */ charCodeMap),\n/* harmony export */   charCodeToBase16: () => (/* binding */ charCodeToBase16),\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\n/* harmony import */ var _Bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/Bytes.js\");\n\n/** @internal */\nfunction assertSize(bytes, size_) {\n    if (_Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(bytes) > size_)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SizeOverflowError({\n            givenSize: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(bytes),\n            maxSize: size_,\n        });\n}\n/** @internal */\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value) - 1)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n}\n/** @internal */\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value) !== end - start) {\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n    }\n}\n/** @internal */\nconst charCodeMap = {\n    zero: 48,\n    nine: 57,\n    A: 65,\n    F: 70,\n    a: 97,\n    f: 102,\n};\n/** @internal */\nfunction charCodeToBase16(char) {\n    if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n        return char - charCodeMap.zero;\n    if (char >= charCodeMap.A && char <= charCodeMap.F)\n        return char - (charCodeMap.A - 10);\n    if (char >= charCodeMap.a && char <= charCodeMap.f)\n        return char - (charCodeMap.a - 10);\n    return undefined;\n}\n/** @internal */\nfunction pad(bytes, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return bytes;\n    if (bytes.length > size)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: bytes.length,\n            targetSize: size,\n            type: 'Bytes',\n        });\n    const paddedBytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n        const padEnd = dir === 'right';\n        paddedBytes[padEnd ? i : size - i - 1] =\n            bytes[padEnd ? i : bytes.length - i - 1];\n    }\n    return paddedBytes;\n}\n/** @internal */\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value;\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    return data;\n}\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/errors.js":
/*!******************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/errors.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUrl: () => (/* binding */ getUrl),\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   prettyPrint: () => (/* binding */ prettyPrint)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/ox/_esm/core/version.js\");\n\n/** @internal */\nfunction getUrl(url) {\n    return url;\n}\n/** @internal */\nfunction getVersion() {\n    return _version_js__WEBPACK_IMPORTED_MODULE_0__.version;\n}\n/** @internal */\nfunction prettyPrint(args) {\n    if (!args)\n        return '';\n    const entries = Object.entries(args)\n        .map(([key, value]) => {\n        if (value === undefined || value === false)\n            return null;\n        return [key, value];\n    })\n        .filter(Boolean);\n    const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0);\n    return entries\n        .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n        .join('\\n');\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL2ludGVybmFsL2Vycm9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdDO0FBQ3hDO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsZ0RBQU87QUFDbEI7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLEdBQUcsSUFBSSwyQkFBMkIsRUFBRSxNQUFNO0FBQzlFO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxveFxcX2VzbVxcY29yZVxcaW50ZXJuYWxcXGVycm9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG4vKiogQGludGVybmFsICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0VXJsKHVybCkge1xuICAgIHJldHVybiB1cmw7XG59XG4vKiogQGludGVybmFsICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0VmVyc2lvbigpIHtcbiAgICByZXR1cm4gdmVyc2lvbjtcbn1cbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBwcmV0dHlQcmludChhcmdzKSB7XG4gICAgaWYgKCFhcmdzKVxuICAgICAgICByZXR1cm4gJyc7XG4gICAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGFyZ3MpXG4gICAgICAgIC5tYXAoKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gZmFsc2UpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgcmV0dXJuIFtrZXksIHZhbHVlXTtcbiAgICB9KVxuICAgICAgICAuZmlsdGVyKEJvb2xlYW4pO1xuICAgIGNvbnN0IG1heExlbmd0aCA9IGVudHJpZXMucmVkdWNlKChhY2MsIFtrZXldKSA9PiBNYXRoLm1heChhY2MsIGtleS5sZW5ndGgpLCAwKTtcbiAgICByZXR1cm4gZW50cmllc1xuICAgICAgICAubWFwKChba2V5LCB2YWx1ZV0pID0+IGAgICR7YCR7a2V5fTpgLnBhZEVuZChtYXhMZW5ndGggKyAxKX0gICR7dmFsdWV9YClcbiAgICAgICAgLmpvaW4oJ1xcbicpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXJyb3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/hex.js":
/*!***************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/hex.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEndOffset: () => (/* binding */ assertEndOffset),\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   assertStartOffset: () => (/* binding */ assertStartOffset),\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n\n/** @internal */\nfunction assertSize(hex, size_) {\n    if (_Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(hex) > size_)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SizeOverflowError({\n            givenSize: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(hex),\n            maxSize: size_,\n        });\n}\n/** @internal */\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value) - 1)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n}\n/** @internal */\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value) !== end - start) {\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n    }\n}\n/** @internal */\nfunction pad(hex_, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return hex_;\n    const hex = hex_.replace('0x', '');\n    if (hex.length > size * 2)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: Math.ceil(hex.length / 2),\n            targetSize: size,\n            type: 'Hex',\n        });\n    return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}`;\n}\n/** @internal */\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value.replace('0x', '');\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    if (data === '0')\n        return '0x';\n    if (dir === 'right' && data.length % 2 === 1)\n        return `0x${data}0`;\n    return `0x${data}`;\n}\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/version.js":
/*!**********************************************!*\
  !*** ./node_modules/ox/_esm/core/version.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/** @internal */\nconst version = '0.1.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxveFxcX2VzbVxcY29yZVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQGludGVybmFsICovXG5leHBvcnQgY29uc3QgdmVyc2lvbiA9ICcwLjEuMSc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/version.js\n");

/***/ })

};
;