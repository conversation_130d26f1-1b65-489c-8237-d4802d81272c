module.exports = {

"[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// biome-ignore lint/performance/noBarrelFile: entrypoint module
__turbopack_context__.s({});
;
;
;
;
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/viem/_esm/utils/rpc/socket.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSocketRpcClient": (()=>getSocketRpcClient),
    "socketClientCache": (()=>socketClientCache)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/request.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$createBatchScheduler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/promise/createBatchScheduler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withTimeout$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/promise/withTimeout.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$id$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/rpc/id.js [app-ssr] (ecmascript)");
;
;
;
;
const socketClientCache = /*#__PURE__*/ new Map();
async function getSocketRpcClient(parameters) {
    const { getSocket, keepAlive = true, key = 'socket', reconnect = true, url } = parameters;
    const { interval: keepAliveInterval = 30_000 } = typeof keepAlive === 'object' ? keepAlive : {};
    const { attempts = 5, delay = 2_000 } = typeof reconnect === 'object' ? reconnect : {};
    let socketClient = socketClientCache.get(`${key}:${url}`);
    // If the socket already exists, return it.
    if (socketClient) return socketClient;
    let reconnectCount = 0;
    const { schedule } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$createBatchScheduler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createBatchScheduler"])({
        id: `${key}:${url}`,
        fn: async ()=>{
            // Set up a cache for incoming "synchronous" requests.
            const requests = new Map();
            // Set up a cache for subscriptions (eth_subscribe).
            const subscriptions = new Map();
            let error;
            let socket;
            let keepAliveTimer;
            // Set up socket implementation.
            async function setup() {
                const result = await getSocket({
                    onClose () {
                        // Notify all requests and subscriptions of the closure error.
                        for (const request of requests.values())request.onError?.(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SocketClosedError"]({
                            url
                        }));
                        for (const subscription of subscriptions.values())subscription.onError?.(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SocketClosedError"]({
                            url
                        }));
                        // Attempt to reconnect.
                        if (reconnect && reconnectCount < attempts) setTimeout(async ()=>{
                            reconnectCount++;
                            await setup().catch(console.error);
                        }, delay);
                        else {
                            requests.clear();
                            subscriptions.clear();
                        }
                    },
                    onError (error_) {
                        error = error_;
                        // Notify all requests and subscriptions of the error.
                        for (const request of requests.values())request.onError?.(error);
                        for (const subscription of subscriptions.values())subscription.onError?.(error);
                        // Make sure socket is definitely closed.
                        socketClient?.close();
                        // Attempt to reconnect.
                        if (reconnect && reconnectCount < attempts) setTimeout(async ()=>{
                            reconnectCount++;
                            await setup().catch(console.error);
                        }, delay);
                        else {
                            requests.clear();
                            subscriptions.clear();
                        }
                    },
                    onOpen () {
                        error = undefined;
                        reconnectCount = 0;
                    },
                    onResponse (data) {
                        const isSubscription = data.method === 'eth_subscription';
                        const id = isSubscription ? data.params.subscription : data.id;
                        const cache = isSubscription ? subscriptions : requests;
                        const callback = cache.get(id);
                        if (callback) callback.onResponse(data);
                        if (!isSubscription) cache.delete(id);
                    }
                });
                socket = result;
                if (keepAlive) {
                    if (keepAliveTimer) clearInterval(keepAliveTimer);
                    keepAliveTimer = setInterval(()=>socket.ping?.(), keepAliveInterval);
                }
                return result;
            }
            await setup();
            error = undefined;
            // Create a new socket instance.
            socketClient = {
                close () {
                    keepAliveTimer && clearInterval(keepAliveTimer);
                    socket.close();
                    socketClientCache.delete(`${key}:${url}`);
                },
                get socket () {
                    return socket;
                },
                request ({ body, onError, onResponse }) {
                    if (error && onError) onError(error);
                    const id = body.id ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$id$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["idCache"].take();
                    const callback = (response)=>{
                        if (typeof response.id === 'number' && id !== response.id) return;
                        // If we are subscribing to a topic, we want to set up a listener for incoming
                        // messages.
                        if (body.method === 'eth_subscribe' && typeof response.result === 'string') subscriptions.set(response.result, {
                            onResponse: callback,
                            onError
                        });
                        // If we are unsubscribing from a topic, we want to remove the listener.
                        if (body.method === 'eth_unsubscribe') subscriptions.delete(body.params?.[0]);
                        onResponse(response);
                    };
                    requests.set(id, {
                        onResponse: callback,
                        onError
                    });
                    try {
                        socket.request({
                            body: {
                                jsonrpc: '2.0',
                                id,
                                ...body
                            }
                        });
                    } catch (error) {
                        onError?.(error);
                    }
                },
                requestAsync ({ body, timeout = 10_000 }) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withTimeout$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withTimeout"])(()=>new Promise((onResponse, onError)=>this.request({
                                body,
                                onError,
                                onResponse
                            })), {
                        errorInstance: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TimeoutError"]({
                            body,
                            url
                        }),
                        timeout
                    });
                },
                requests,
                subscriptions,
                url
            };
            socketClientCache.set(`${key}:${url}`, socketClient);
            return [
                socketClient
            ];
        }
    });
    const [_, [socketClient_]] = await schedule();
    return socketClient_;
} //# sourceMappingURL=socket.js.map
}}),
"[project]/node_modules/viem/_esm/utils/rpc/webSocket.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getWebSocketRpcClient": (()=>getWebSocketRpcClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/request.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/rpc/socket.js [app-ssr] (ecmascript)");
;
;
async function getWebSocketRpcClient(url, options = {}) {
    const { keepAlive, reconnect } = options;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSocketRpcClient"])({
        async getSocket ({ onClose, onError, onOpen, onResponse }) {
            const WebSocket = await __turbopack_context__.r("[project]/node_modules/isows/_esm/index.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i).then((module)=>module.WebSocket);
            const socket = new WebSocket(url);
            function onClose_() {
                socket.removeEventListener('close', onClose_);
                socket.removeEventListener('message', onMessage);
                socket.removeEventListener('error', onError);
                socket.removeEventListener('open', onOpen);
                onClose();
            }
            function onMessage({ data }) {
                try {
                    const _data = JSON.parse(data);
                    onResponse(_data);
                } catch (error) {
                    onError(error);
                }
            }
            // Setup event listeners for RPC & subscription responses.
            socket.addEventListener('close', onClose_);
            socket.addEventListener('message', onMessage);
            socket.addEventListener('error', onError);
            socket.addEventListener('open', onOpen);
            // Wait for the socket to open.
            if (socket.readyState === WebSocket.CONNECTING) {
                await new Promise((resolve, reject)=>{
                    if (!socket) return;
                    socket.onopen = resolve;
                    socket.onerror = reject;
                });
            }
            const { close: close_ } = socket;
            return Object.assign(socket, {
                close () {
                    close_.bind(socket)();
                    onClose_();
                },
                ping () {
                    try {
                        if (socket.readyState === socket.CLOSED || socket.readyState === socket.CLOSING) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketRequestError"]({
                            url: socket.url,
                            cause: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SocketClosedError"]({
                                url: socket.url
                            })
                        });
                        const body = {
                            jsonrpc: '2.0',
                            method: 'net_version',
                            params: []
                        };
                        socket.send(JSON.stringify(body));
                    } catch (error) {
                        onError(error);
                    }
                },
                request ({ body }) {
                    if (socket.readyState === socket.CLOSED || socket.readyState === socket.CLOSING) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebSocketRequestError"]({
                        body,
                        url: socket.url,
                        cause: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SocketClosedError"]({
                            url: socket.url
                        })
                    });
                    return socket.send(JSON.stringify(body));
                }
            });
        },
        keepAlive,
        reconnect,
        url
    });
} //# sourceMappingURL=webSocket.js.map
}}),
"[project]/node_modules/viem/_esm/utils/rpc/compat.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// TODO(v3): This file is here for backwards compatibility, and to prevent breaking changes.
// These APIs will be removed in v3.
__turbopack_context__.s({
    "getSocket": (()=>getSocket),
    "rpc": (()=>rpc)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$http$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/rpc/http.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$webSocket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/rpc/webSocket.js [app-ssr] (ecmascript)");
;
;
function webSocket(socketClient, { body, onError, onResponse }) {
    socketClient.request({
        body,
        onError,
        onResponse
    });
    return socketClient;
}
async function webSocketAsync(socketClient, { body, timeout = 10_000 }) {
    return socketClient.requestAsync({
        body,
        timeout
    });
}
async function getSocket(url) {
    const client = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$webSocket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketRpcClient"])(url);
    return Object.assign(client.socket, {
        requests: client.requests,
        subscriptions: client.subscriptions
    });
}
const rpc = {
    /**
     * @deprecated use `getHttpRpcClient` instead.
     *
     * ```diff
     * -import { rpc } from 'viem/utils'
     * +import { getHttpRpcClient } from 'viem/utils'
     *
     * -rpc.http(url, params)
     * +const httpClient = getHttpRpcClient(url)
     * +httpClient.request(params)
     * ```
     */ http (url, params) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$http$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getHttpRpcClient"])(url).request(params);
    },
    /**
     * @deprecated use `getWebSocketRpcClient` instead.
     *
     * ```diff
     * -import { rpc } from 'viem/utils'
     * +import { getWebSocketRpcClient } from 'viem/utils'
     *
     * -rpc.webSocket(url, params)
     * +const webSocketClient = getWebSocketRpcClient(url)
     * +webSocketClient.request(params)
     * ```
     */ webSocket,
    /**
     * @deprecated use `getWebSocketRpcClient` instead.
     *
     * ```diff
     * -import { rpc } from 'viem/utils'
     * +import { getWebSocketRpcClient } from 'viem/utils'
     *
     * -const response = await rpc.webSocketAsync(url, params)
     * +const webSocketClient = getWebSocketRpcClient(url)
     * +const response = await webSocketClient.requestAsync(params)
     * ```
     */ webSocketAsync
}; /* c8 ignore end */  //# sourceMappingURL=compat.js.map
}}),
"[project]/node_modules/@wagmi/core/dist/esm/connectors/mock.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mock": (()=>mock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/request.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/rpc.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$clients$2f$transports$2f$custom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/clients/transports/custom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$fromHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/encoding/fromHex.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/address/getAddress.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$hash$2f$keccak256$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/hash/keccak256.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/encoding/toHex.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$compat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/rpc/compat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/config.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/createConnector.js [app-ssr] (ecmascript)");
;
;
;
;
mock.type = 'mock';
function mock(parameters) {
    const transactionCache = new Map();
    const features = parameters.features ?? {
        defaultConnected: false
    };
    let connected = features.defaultConnected;
    let connectedChainId;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConnector"])((config)=>({
            id: 'mock',
            name: 'Mock Connector',
            type: mock.type,
            async setup () {
                connectedChainId = config.chains[0].id;
            },
            async connect ({ chainId } = {}) {
                if (features.connectError) {
                    if (typeof features.connectError === 'boolean') throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](new Error('Failed to connect.'));
                    throw features.connectError;
                }
                const provider = await this.getProvider();
                const accounts = await provider.request({
                    method: 'eth_requestAccounts'
                });
                let currentChainId = await this.getChainId();
                if (chainId && currentChainId !== chainId) {
                    const chain = await this.switchChain({
                        chainId
                    });
                    currentChainId = chain.id;
                }
                connected = true;
                return {
                    accounts: accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x)),
                    chainId: currentChainId
                };
            },
            async disconnect () {
                connected = false;
            },
            async getAccounts () {
                if (!connected) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConnectorNotConnectedError"]();
                const provider = await this.getProvider();
                const accounts = await provider.request({
                    method: 'eth_accounts'
                });
                return accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
            },
            async getChainId () {
                const provider = await this.getProvider();
                const hexChainId = await provider.request({
                    method: 'eth_chainId'
                });
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$fromHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromHex"])(hexChainId, 'number');
            },
            async isAuthorized () {
                if (!features.reconnect) return false;
                if (!connected) return false;
                const accounts = await this.getAccounts();
                return !!accounts.length;
            },
            async switchChain ({ chainId }) {
                const provider = await this.getProvider();
                const chain = config.chains.find((x)=>x.id === chainId);
                if (!chain) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainNotConfiguredError"]());
                await provider.request({
                    method: 'wallet_switchEthereumChain',
                    params: [
                        {
                            chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId)
                        }
                    ]
                });
                return chain;
            },
            onAccountsChanged (accounts) {
                if (accounts.length === 0) this.onDisconnect();
                else config.emitter.emit('change', {
                    accounts: accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x))
                });
            },
            onChainChanged (chain) {
                const chainId = Number(chain);
                config.emitter.emit('change', {
                    chainId
                });
            },
            async onDisconnect (_error) {
                config.emitter.emit('disconnect');
                connected = false;
            },
            async getProvider ({ chainId } = {}) {
                const chain = config.chains.find((x)=>x.id === chainId) ?? config.chains[0];
                const url = chain.rpcUrls.default.http[0];
                const request = async ({ method, params })=>{
                    // eth methods
                    if (method === 'eth_chainId') return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(connectedChainId);
                    if (method === 'eth_requestAccounts') return parameters.accounts;
                    if (method === 'eth_signTypedData_v4') {
                        if (features.signTypedDataError) {
                            if (typeof features.signTypedDataError === 'boolean') throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](new Error('Failed to sign typed data.'));
                            throw features.signTypedDataError;
                        }
                    }
                    // wallet methods
                    if (method === 'wallet_switchEthereumChain') {
                        if (features.switchChainError) {
                            if (typeof features.switchChainError === 'boolean') throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](new Error('Failed to switch chain.'));
                            throw features.switchChainError;
                        }
                        connectedChainId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$fromHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromHex"])(params[0].chainId, 'number');
                        this.onChainChanged(connectedChainId.toString());
                        return;
                    }
                    if (method === 'wallet_watchAsset') {
                        if (features.watchAssetError) {
                            if (typeof features.watchAssetError === 'boolean') throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](new Error('Failed to switch chain.'));
                            throw features.watchAssetError;
                        }
                        return connected;
                    }
                    if (method === 'wallet_getCapabilities') return {
                        '0x2105': {
                            paymasterService: {
                                supported: params[0] === '******************************************'
                            },
                            sessionKeys: {
                                supported: true
                            }
                        },
                        '0x14A34': {
                            paymasterService: {
                                supported: params[0] === '******************************************'
                            }
                        }
                    };
                    if (method === 'wallet_sendCalls') {
                        const hashes = [];
                        const calls = params[0].calls;
                        for (const call of calls){
                            const { result, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$compat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["rpc"].http(url, {
                                body: {
                                    method: 'eth_sendTransaction',
                                    params: [
                                        call
                                    ]
                                }
                            });
                            if (error) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RpcRequestError"]({
                                body: {
                                    method,
                                    params
                                },
                                error,
                                url
                            });
                            hashes.push(result);
                        }
                        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$hash$2f$keccak256$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keccak256"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stringToHex"])(JSON.stringify(calls)));
                        transactionCache.set(id, hashes);
                        return {
                            id
                        };
                    }
                    if (method === 'wallet_getCallsStatus') {
                        const hashes = transactionCache.get(params[0]);
                        if (!hashes) return {
                            atomic: false,
                            chainId: '0x1',
                            id: params[0],
                            status: 100,
                            receipts: [],
                            version: '2.0.0'
                        };
                        const receipts = await Promise.all(hashes.map(async (hash)=>{
                            const { result, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$compat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["rpc"].http(url, {
                                body: {
                                    method: 'eth_getTransactionReceipt',
                                    params: [
                                        hash
                                    ],
                                    id: 0
                                }
                            });
                            if (error) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RpcRequestError"]({
                                body: {
                                    method,
                                    params
                                },
                                error,
                                url
                            });
                            if (!result) return null;
                            return {
                                blockHash: result.blockHash,
                                blockNumber: result.blockNumber,
                                gasUsed: result.gasUsed,
                                logs: result.logs,
                                status: result.status,
                                transactionHash: result.transactionHash
                            };
                        }));
                        const receipts_ = receipts.filter((x)=>x !== null);
                        if (receipts_.length === 0) return {
                            atomic: false,
                            chainId: '0x1',
                            id: params[0],
                            status: 100,
                            receipts: [],
                            version: '2.0.0'
                        };
                        return {
                            atomic: false,
                            chainId: '0x1',
                            id: params[0],
                            status: 200,
                            receipts: receipts_,
                            version: '2.0.0'
                        };
                    }
                    if (method === 'wallet_showCallsStatus') return;
                    // other methods
                    if (method === 'personal_sign') {
                        if (features.signMessageError) {
                            if (typeof features.signMessageError === 'boolean') throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](new Error('Failed to sign message.'));
                            throw features.signMessageError;
                        }
                        // Change `personal_sign` to `eth_sign` and swap params
                        method = 'eth_sign';
                        params = [
                            params[1],
                            params[0]
                        ];
                    }
                    const body = {
                        method,
                        params
                    };
                    const { error, result } = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$rpc$2f$compat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["rpc"].http(url, {
                        body
                    });
                    if (error) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RpcRequestError"]({
                        body,
                        error,
                        url
                    });
                    return result;
                };
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$clients$2f$transports$2f$custom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["custom"])({
                    request
                })({
                    retryCount: 0
                });
            }
        }));
} //# sourceMappingURL=mock.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "coinbaseWallet": (()=>coinbaseWallet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/config.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/createConnector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/rpc.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/address/getAddress.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/encoding/toHex.js [app-ssr] (ecmascript)");
;
;
coinbaseWallet.type = 'coinbaseWallet';
function coinbaseWallet(parameters = {}) {
    if (parameters.version === '3' || parameters.headlessMode) return version3(parameters);
    return version4(parameters);
}
function version4(parameters) {
    let walletProvider;
    let accountsChanged;
    let chainChanged;
    let disconnect;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConnector"])((config)=>({
            id: 'coinbaseWalletSDK',
            name: 'Coinbase Wallet',
            rdns: 'com.coinbase.wallet',
            type: coinbaseWallet.type,
            async connect ({ chainId, ...rest } = {}) {
                try {
                    const provider = await this.getProvider();
                    const accounts = (await provider.request({
                        method: 'eth_requestAccounts',
                        params: 'instantOnboarding' in rest && rest.instantOnboarding ? [
                            {
                                onboarding: 'instant'
                            }
                        ] : []
                    })).map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
                    if (!accountsChanged) {
                        accountsChanged = this.onAccountsChanged.bind(this);
                        provider.on('accountsChanged', accountsChanged);
                    }
                    if (!chainChanged) {
                        chainChanged = this.onChainChanged.bind(this);
                        provider.on('chainChanged', chainChanged);
                    }
                    if (!disconnect) {
                        disconnect = this.onDisconnect.bind(this);
                        provider.on('disconnect', disconnect);
                    }
                    // Switch to chain if provided
                    let currentChainId = await this.getChainId();
                    if (chainId && currentChainId !== chainId) {
                        const chain = await this.switchChain({
                            chainId
                        }).catch((error)=>{
                            if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"].code) throw error;
                            return {
                                id: currentChainId
                            };
                        });
                        currentChainId = chain?.id ?? currentChainId;
                    }
                    return {
                        accounts,
                        chainId: currentChainId
                    };
                } catch (error) {
                    if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message)) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    throw error;
                }
            },
            async disconnect () {
                const provider = await this.getProvider();
                if (accountsChanged) {
                    provider.removeListener('accountsChanged', accountsChanged);
                    accountsChanged = undefined;
                }
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
                provider.disconnect();
                provider.close?.();
            },
            async getAccounts () {
                const provider = await this.getProvider();
                return (await provider.request({
                    method: 'eth_accounts'
                })).map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
            },
            async getChainId () {
                const provider = await this.getProvider();
                const chainId = await provider.request({
                    method: 'eth_chainId'
                });
                return Number(chainId);
            },
            async getProvider () {
                if (!walletProvider) {
                    const preference = (()=>{
                        if (typeof parameters.preference === 'string') return {
                            options: parameters.preference
                        };
                        return {
                            ...parameters.preference,
                            options: parameters.preference?.options ?? 'all'
                        };
                    })();
                    const { createCoinbaseWalletSDK } = await __turbopack_context__.r("[project]/node_modules/@coinbase/wallet-sdk/dist/index.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                    const sdk = createCoinbaseWalletSDK({
                        ...parameters,
                        appChainIds: config.chains.map((x)=>x.id),
                        preference
                    });
                    walletProvider = sdk.getProvider();
                }
                return walletProvider;
            },
            async isAuthorized () {
                try {
                    const accounts = await this.getAccounts();
                    return !!accounts.length;
                } catch  {
                    return false;
                }
            },
            async switchChain ({ addEthereumChainParameter, chainId }) {
                const chain = config.chains.find((chain)=>chain.id === chainId);
                if (!chain) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainNotConfiguredError"]());
                const provider = await this.getProvider();
                try {
                    await provider.request({
                        method: 'wallet_switchEthereumChain',
                        params: [
                            {
                                chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chain.id)
                            }
                        ]
                    });
                    return chain;
                } catch (error) {
                    // Indicates chain is not added to provider
                    if (error.code === 4902) {
                        try {
                            let blockExplorerUrls;
                            if (addEthereumChainParameter?.blockExplorerUrls) blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;
                            else blockExplorerUrls = chain.blockExplorers?.default.url ? [
                                chain.blockExplorers?.default.url
                            ] : [];
                            let rpcUrls;
                            if (addEthereumChainParameter?.rpcUrls?.length) rpcUrls = addEthereumChainParameter.rpcUrls;
                            else rpcUrls = [
                                chain.rpcUrls.default?.http[0] ?? ''
                            ];
                            const addEthereumChain = {
                                blockExplorerUrls,
                                chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId),
                                chainName: addEthereumChainParameter?.chainName ?? chain.name,
                                iconUrls: addEthereumChainParameter?.iconUrls,
                                nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,
                                rpcUrls
                            };
                            await provider.request({
                                method: 'wallet_addEthereumChain',
                                params: [
                                    addEthereumChain
                                ]
                            });
                            return chain;
                        } catch (error) {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                        }
                    }
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](error);
                }
            },
            onAccountsChanged (accounts) {
                if (accounts.length === 0) this.onDisconnect();
                else config.emitter.emit('change', {
                    accounts: accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x))
                });
            },
            onChainChanged (chain) {
                const chainId = Number(chain);
                config.emitter.emit('change', {
                    chainId
                });
            },
            async onDisconnect (_error) {
                config.emitter.emit('disconnect');
                const provider = await this.getProvider();
                if (accountsChanged) {
                    provider.removeListener('accountsChanged', accountsChanged);
                    accountsChanged = undefined;
                }
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
            }
        }));
}
function version3(parameters) {
    const reloadOnDisconnect = false;
    let sdk;
    let walletProvider;
    let accountsChanged;
    let chainChanged;
    let disconnect;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConnector"])((config)=>({
            id: 'coinbaseWalletSDK',
            name: 'Coinbase Wallet',
            rdns: 'com.coinbase.wallet',
            type: coinbaseWallet.type,
            async connect ({ chainId } = {}) {
                try {
                    const provider = await this.getProvider();
                    const accounts = (await provider.request({
                        method: 'eth_requestAccounts'
                    })).map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
                    if (!accountsChanged) {
                        accountsChanged = this.onAccountsChanged.bind(this);
                        provider.on('accountsChanged', accountsChanged);
                    }
                    if (!chainChanged) {
                        chainChanged = this.onChainChanged.bind(this);
                        provider.on('chainChanged', chainChanged);
                    }
                    if (!disconnect) {
                        disconnect = this.onDisconnect.bind(this);
                        provider.on('disconnect', disconnect);
                    }
                    // Switch to chain if provided
                    let currentChainId = await this.getChainId();
                    if (chainId && currentChainId !== chainId) {
                        const chain = await this.switchChain({
                            chainId
                        }).catch((error)=>{
                            if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"].code) throw error;
                            return {
                                id: currentChainId
                            };
                        });
                        currentChainId = chain?.id ?? currentChainId;
                    }
                    return {
                        accounts,
                        chainId: currentChainId
                    };
                } catch (error) {
                    if (/(user closed modal|accounts received is empty|user denied account)/i.test(error.message)) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    throw error;
                }
            },
            async disconnect () {
                const provider = await this.getProvider();
                if (accountsChanged) {
                    provider.removeListener('accountsChanged', accountsChanged);
                    accountsChanged = undefined;
                }
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
                provider.disconnect();
                provider.close();
            },
            async getAccounts () {
                const provider = await this.getProvider();
                return (await provider.request({
                    method: 'eth_accounts'
                })).map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
            },
            async getChainId () {
                const provider = await this.getProvider();
                const chainId = await provider.request({
                    method: 'eth_chainId'
                });
                return Number(chainId);
            },
            async getProvider () {
                if (!walletProvider) {
                    // Unwrapping import for Vite compatibility.
                    // See: https://github.com/vitejs/vite/issues/9703
                    const CoinbaseWalletSDK = await (async ()=>{
                        const { default: SDK } = await __turbopack_context__.r("[project]/node_modules/cbw-sdk/dist/index.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                        if (typeof SDK !== 'function' && typeof SDK.default === 'function') return SDK.default;
                        return SDK;
                    })();
                    sdk = new CoinbaseWalletSDK({
                        ...parameters,
                        reloadOnDisconnect
                    });
                    // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.
                    const walletExtensionChainId = sdk.walletExtension?.getChainId();
                    const chain = config.chains.find((chain)=>parameters.chainId ? chain.id === parameters.chainId : chain.id === walletExtensionChainId) || config.chains[0];
                    const chainId = parameters.chainId || chain?.id;
                    const jsonRpcUrl = parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0];
                    walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId);
                }
                return walletProvider;
            },
            async isAuthorized () {
                try {
                    const accounts = await this.getAccounts();
                    return !!accounts.length;
                } catch  {
                    return false;
                }
            },
            async switchChain ({ addEthereumChainParameter, chainId }) {
                const chain = config.chains.find((chain)=>chain.id === chainId);
                if (!chain) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainNotConfiguredError"]());
                const provider = await this.getProvider();
                try {
                    await provider.request({
                        method: 'wallet_switchEthereumChain',
                        params: [
                            {
                                chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chain.id)
                            }
                        ]
                    });
                    return chain;
                } catch (error) {
                    // Indicates chain is not added to provider
                    if (error.code === 4902) {
                        try {
                            let blockExplorerUrls;
                            if (addEthereumChainParameter?.blockExplorerUrls) blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;
                            else blockExplorerUrls = chain.blockExplorers?.default.url ? [
                                chain.blockExplorers?.default.url
                            ] : [];
                            let rpcUrls;
                            if (addEthereumChainParameter?.rpcUrls?.length) rpcUrls = addEthereumChainParameter.rpcUrls;
                            else rpcUrls = [
                                chain.rpcUrls.default?.http[0] ?? ''
                            ];
                            const addEthereumChain = {
                                blockExplorerUrls,
                                chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId),
                                chainName: addEthereumChainParameter?.chainName ?? chain.name,
                                iconUrls: addEthereumChainParameter?.iconUrls,
                                nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,
                                rpcUrls
                            };
                            await provider.request({
                                method: 'wallet_addEthereumChain',
                                params: [
                                    addEthereumChain
                                ]
                            });
                            return chain;
                        } catch (error) {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                        }
                    }
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](error);
                }
            },
            onAccountsChanged (accounts) {
                if (accounts.length === 0) this.onDisconnect();
                else config.emitter.emit('change', {
                    accounts: accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x))
                });
            },
            onChainChanged (chain) {
                const chainId = Number(chain);
                config.emitter.emit('change', {
                    chainId
                });
            },
            async onDisconnect (_error) {
                config.emitter.emit('disconnect');
                const provider = await this.getProvider();
                if (accountsChanged) {
                    provider.removeListener('accountsChanged', accountsChanged);
                    accountsChanged = undefined;
                }
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
            }
        }));
} //# sourceMappingURL=coinbaseWallet.js.map
}}),
"[project]/node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractRpcUrls": (()=>extractRpcUrls)
});
function extractRpcUrls(parameters) {
    const { chain } = parameters;
    const fallbackUrl = chain.rpcUrls.default.http[0];
    if (!parameters.transports) return [
        fallbackUrl
    ];
    const transport = parameters.transports?.[chain.id]?.({
        chain
    });
    const transports = transport?.value?.transports || [
        transport
    ];
    return transports.map(({ value })=>value?.url || fallbackUrl);
} //# sourceMappingURL=extractRpcUrls.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/metaMask.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "metaMask": (()=>metaMask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/config.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/connector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/createConnector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$utils$2f$extractRpcUrls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/rpc.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/address/getAddress.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$fromHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/encoding/fromHex.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/encoding/toHex.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withRetry$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/promise/withRetry.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withTimeout$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/promise/withTimeout.js [app-ssr] (ecmascript)");
;
;
metaMask.type = 'metaMask';
function metaMask(parameters = {}) {
    let sdk;
    let provider;
    let providerPromise;
    let accountsChanged;
    let chainChanged;
    let connect;
    let displayUri;
    let disconnect;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConnector"])((config)=>({
            id: 'metaMaskSDK',
            name: 'MetaMask',
            rdns: [
                'io.metamask',
                'io.metamask.mobile'
            ],
            type: metaMask.type,
            async setup () {
                const provider = await this.getProvider();
                if (provider?.on) {
                    if (!connect) {
                        connect = this.onConnect.bind(this);
                        provider.on('connect', connect);
                    }
                    // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).
                    // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.
                    if (!accountsChanged) {
                        accountsChanged = this.onAccountsChanged.bind(this);
                        provider.on('accountsChanged', accountsChanged);
                    }
                }
            },
            async connect ({ chainId, isReconnecting } = {}) {
                const provider = await this.getProvider();
                if (!displayUri) {
                    displayUri = this.onDisplayUri;
                    provider.on('display_uri', displayUri);
                }
                let accounts = [];
                if (isReconnecting) accounts = await this.getAccounts().catch(()=>[]);
                try {
                    let signResponse;
                    let connectWithResponse;
                    if (!accounts?.length) {
                        if (parameters.connectAndSign || parameters.connectWith) {
                            if (parameters.connectAndSign) signResponse = await sdk.connectAndSign({
                                msg: parameters.connectAndSign
                            });
                            else if (parameters.connectWith) connectWithResponse = await sdk.connectWith({
                                method: parameters.connectWith.method,
                                params: parameters.connectWith.params
                            });
                            accounts = await this.getAccounts();
                        } else {
                            const requestedAccounts = await sdk.connect();
                            accounts = requestedAccounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
                        }
                    }
                    // Switch to chain if provided
                    let currentChainId = await this.getChainId();
                    if (chainId && currentChainId !== chainId) {
                        const chain = await this.switchChain({
                            chainId
                        }).catch((error)=>{
                            if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"].code) throw error;
                            return {
                                id: currentChainId
                            };
                        });
                        currentChainId = chain?.id ?? currentChainId;
                    }
                    if (displayUri) {
                        provider.removeListener('display_uri', displayUri);
                        displayUri = undefined;
                    }
                    if (signResponse) provider.emit('connectAndSign', {
                        accounts,
                        chainId: currentChainId,
                        signResponse
                    });
                    else if (connectWithResponse) provider.emit('connectWith', {
                        accounts,
                        chainId: currentChainId,
                        connectWithResponse
                    });
                    // Manage EIP-1193 event listeners
                    // https://eips.ethereum.org/EIPS/eip-1193#events
                    if (connect) {
                        provider.removeListener('connect', connect);
                        connect = undefined;
                    }
                    if (!accountsChanged) {
                        accountsChanged = this.onAccountsChanged.bind(this);
                        provider.on('accountsChanged', accountsChanged);
                    }
                    if (!chainChanged) {
                        chainChanged = this.onChainChanged.bind(this);
                        provider.on('chainChanged', chainChanged);
                    }
                    if (!disconnect) {
                        disconnect = this.onDisconnect.bind(this);
                        provider.on('disconnect', disconnect);
                    }
                    return {
                        accounts,
                        chainId: currentChainId
                    };
                } catch (err) {
                    const error = err;
                    if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"].code) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ResourceUnavailableRpcError"].code) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ResourceUnavailableRpcError"](error);
                    throw error;
                }
            },
            async disconnect () {
                const provider = await this.getProvider();
                // Manage EIP-1193 event listeners
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
                if (!connect) {
                    connect = this.onConnect.bind(this);
                    provider.on('connect', connect);
                }
                await sdk.terminate();
            },
            async getAccounts () {
                const provider = await this.getProvider();
                const accounts = await provider.request({
                    method: 'eth_accounts'
                });
                return accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
            },
            async getChainId () {
                const provider = await this.getProvider();
                const chainId = provider.getChainId() || await provider?.request({
                    method: 'eth_chainId'
                });
                return Number(chainId);
            },
            async getProvider () {
                async function initProvider() {
                    // Unwrapping import for Vite compatibility.
                    // See: https://github.com/vitejs/vite/issues/9703
                    const MetaMaskSDK = await (async ()=>{
                        const { default: SDK } = await __turbopack_context__.r("[project]/node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                        if (typeof SDK !== 'function' && typeof SDK.default === 'function') return SDK.default;
                        return SDK;
                    })();
                    const readonlyRPCMap = {};
                    for (const chain of config.chains)readonlyRPCMap[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chain.id)] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$utils$2f$extractRpcUrls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractRpcUrls"])({
                        chain,
                        transports: config.transports
                    })?.[0];
                    sdk = new MetaMaskSDK({
                        _source: 'wagmi',
                        forceDeleteProvider: false,
                        forceInjectProvider: false,
                        injectProvider: false,
                        // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`
                        ...parameters,
                        readonlyRPCMap,
                        dappMetadata: {
                            ...parameters.dappMetadata,
                            // Test if name and url are set AND not empty
                            name: parameters.dappMetadata?.name ? parameters.dappMetadata?.name : 'wagmi',
                            url: parameters.dappMetadata?.url ? parameters.dappMetadata?.url : typeof window !== 'undefined' ? window.location.origin : 'https://wagmi.sh'
                        },
                        useDeeplink: parameters.useDeeplink ?? true
                    });
                    const result = await sdk.init();
                    // On initial load, sometimes `sdk.getProvider` does not return provider.
                    // https://github.com/wevm/wagmi/issues/4367
                    // Use result of `init` call if available.
                    const provider = (()=>{
                        if (result?.activeProvider) return result.activeProvider;
                        return sdk.getProvider();
                    })();
                    if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                    return provider;
                }
                if (!provider) {
                    if (!providerPromise) providerPromise = initProvider();
                    provider = await providerPromise;
                }
                return provider;
            },
            async isAuthorized () {
                try {
                    // MetaMask mobile provider sometimes fails to immediately resolve
                    // JSON-RPC requests on page load
                    const timeout = 200;
                    const accounts = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withRetry$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withRetry"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withTimeout$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withTimeout"])(()=>this.getAccounts(), {
                            timeout
                        }), {
                        delay: timeout + 1,
                        retryCount: 3
                    });
                    return !!accounts.length;
                } catch  {
                    return false;
                }
            },
            async switchChain ({ addEthereumChainParameter, chainId }) {
                const provider = await this.getProvider();
                const chain = config.chains.find((x)=>x.id === chainId);
                if (!chain) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainNotConfiguredError"]());
                try {
                    await provider.request({
                        method: 'wallet_switchEthereumChain',
                        params: [
                            {
                                chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId)
                            }
                        ]
                    });
                    // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.
                    // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.
                    // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via
                    // this callback or an externally emitted `'chainChanged'` event.
                    // https://github.com/MetaMask/metamask-extension/issues/24247
                    await waitForChainIdToSync();
                    await sendAndWaitForChangeEvent(chainId);
                    return chain;
                } catch (err) {
                    const error = err;
                    if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"].code) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    // Indicates chain is not added to provider
                    if (error.code === 4902 || // Unwrapping for MetaMask Mobile
                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719
                    error?.data?.originalError?.code === 4902) {
                        try {
                            await provider.request({
                                method: 'wallet_addEthereumChain',
                                params: [
                                    {
                                        blockExplorerUrls: (()=>{
                                            const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};
                                            if (addEthereumChainParameter?.blockExplorerUrls) return addEthereumChainParameter.blockExplorerUrls;
                                            if (blockExplorer) return [
                                                blockExplorer.url,
                                                ...Object.values(blockExplorers).map((x)=>x.url)
                                            ];
                                            return;
                                        })(),
                                        chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId),
                                        chainName: addEthereumChainParameter?.chainName ?? chain.name,
                                        iconUrls: addEthereumChainParameter?.iconUrls,
                                        nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,
                                        rpcUrls: (()=>{
                                            if (addEthereumChainParameter?.rpcUrls?.length) return addEthereumChainParameter.rpcUrls;
                                            return [
                                                chain.rpcUrls.default?.http[0] ?? ''
                                            ];
                                        })()
                                    }
                                ]
                            });
                            await waitForChainIdToSync();
                            await sendAndWaitForChangeEvent(chainId);
                            return chain;
                        } catch (err) {
                            const error = err;
                            if (error.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"].code) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](error);
                        }
                    }
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](error);
                }
                async function waitForChainIdToSync() {
                    // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.
                    // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withRetry$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withRetry"])(async ()=>{
                        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$fromHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hexToNumber"])(await provider.request({
                            method: 'eth_chainId'
                        }));
                        // `value` doesn't match expected `chainId`, throw to trigger retry
                        if (value !== chainId) throw new Error('User rejected switch after adding network.');
                        return value;
                    }, {
                        delay: 50,
                        retryCount: 20
                    });
                }
                async function sendAndWaitForChangeEvent(chainId) {
                    await new Promise((resolve)=>{
                        const listener = (data)=>{
                            if ('chainId' in data && data.chainId === chainId) {
                                config.emitter.off('change', listener);
                                resolve();
                            }
                        };
                        config.emitter.on('change', listener);
                        config.emitter.emit('change', {
                            chainId
                        });
                    });
                }
            },
            async onAccountsChanged (accounts) {
                // Disconnect if there are no accounts
                if (accounts.length === 0) {
                    // ... and using browser extension
                    if (sdk.isExtensionActive()) this.onDisconnect();
                    else return;
                } else if (config.emitter.listenerCount('connect')) {
                    const chainId = (await this.getChainId()).toString();
                    this.onConnect({
                        chainId
                    });
                } else config.emitter.emit('change', {
                    accounts: accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x))
                });
            },
            onChainChanged (chain) {
                const chainId = Number(chain);
                config.emitter.emit('change', {
                    chainId
                });
            },
            async onConnect (connectInfo) {
                const accounts = await this.getAccounts();
                if (accounts.length === 0) return;
                const chainId = Number(connectInfo.chainId);
                config.emitter.emit('connect', {
                    accounts,
                    chainId
                });
                const provider = await this.getProvider();
                if (connect) {
                    provider.removeListener('connect', connect);
                    connect = undefined;
                }
                if (!accountsChanged) {
                    accountsChanged = this.onAccountsChanged.bind(this);
                    provider.on('accountsChanged', accountsChanged);
                }
                if (!chainChanged) {
                    chainChanged = this.onChainChanged.bind(this);
                    provider.on('chainChanged', chainChanged);
                }
                if (!disconnect) {
                    disconnect = this.onDisconnect.bind(this);
                    provider.on('disconnect', disconnect);
                }
            },
            async onDisconnect (error) {
                const provider = await this.getProvider();
                // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting
                // https://github.com/MetaMask/providers/pull/120
                if (error && error.code === 1013) {
                    if (provider && !!(await this.getAccounts()).length) return;
                }
                config.emitter.emit('disconnect');
                // Manage EIP-1193 event listeners
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
                if (!connect) {
                    connect = this.onConnect.bind(this);
                    provider.on('connect', connect);
                }
            },
            onDisplayUri (uri) {
                config.emitter.emit('message', {
                    type: 'display_uri',
                    data: uri
                });
            }
        }));
} //# sourceMappingURL=metaMask.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/safe.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "safe": (()=>safe)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/connector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/createConnector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/address/getAddress.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withTimeout$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/promise/withTimeout.js [app-ssr] (ecmascript)");
;
;
safe.type = 'safe';
function safe(parameters = {}) {
    const { shimDisconnect = false } = parameters;
    let provider_;
    let disconnect;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConnector"])((config)=>({
            id: 'safe',
            name: 'Safe',
            type: safe.type,
            async connect () {
                const provider = await this.getProvider();
                if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                const accounts = await this.getAccounts();
                const chainId = await this.getChainId();
                if (!disconnect) {
                    disconnect = this.onDisconnect.bind(this);
                    provider.on('disconnect', disconnect);
                }
                // Remove disconnected shim if it exists
                if (shimDisconnect) await config.storage?.removeItem('safe.disconnected');
                return {
                    accounts,
                    chainId
                };
            },
            async disconnect () {
                const provider = await this.getProvider();
                if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
                // Add shim signalling connector is disconnected
                if (shimDisconnect) await config.storage?.setItem('safe.disconnected', true);
            },
            async getAccounts () {
                const provider = await this.getProvider();
                if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                return (await provider.request({
                    method: 'eth_accounts'
                })).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"]);
            },
            async getProvider () {
                // Only allowed in iframe context
                const isIframe = typeof window !== 'undefined' && window?.parent !== window;
                if (!isIframe) return;
                if (!provider_) {
                    const { default: SDK } = await __turbopack_context__.r("[project]/node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                    const sdk = new SDK(parameters);
                    // `getInfo` hangs when not used in Safe App iFrame
                    // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********
                    const safe = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$promise$2f$withTimeout$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withTimeout"])(()=>sdk.safe.getInfo(), {
                        timeout: parameters.unstable_getInfoTimeout ?? 10
                    });
                    if (!safe) throw new Error('Could not load Safe information');
                    // Unwrapping import for Vite compatibility.
                    // See: https://github.com/vitejs/vite/issues/9703
                    const SafeAppProvider = await (async ()=>{
                        const Provider = await __turbopack_context__.r("[project]/node_modules/@safe-global/safe-apps-provider/dist/index.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                        if (typeof Provider.SafeAppProvider !== 'function' && typeof Provider.default.SafeAppProvider === 'function') return Provider.default.SafeAppProvider;
                        return Provider.SafeAppProvider;
                    })();
                    provider_ = new SafeAppProvider(safe, sdk);
                }
                return provider_;
            },
            async getChainId () {
                const provider = await this.getProvider();
                if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                return Number(provider.chainId);
            },
            async isAuthorized () {
                try {
                    const isDisconnected = shimDisconnect && await config.storage?.getItem('safe.disconnected');
                    if (isDisconnected) return false;
                    const accounts = await this.getAccounts();
                    return !!accounts.length;
                } catch  {
                    return false;
                }
            },
            onAccountsChanged () {
            // Not relevant for Safe because changing account requires app reload.
            },
            onChainChanged () {
            // Not relevant for Safe because Safe smart contract wallets only exist on single chain.
            },
            onDisconnect () {
                config.emitter.emit('disconnect');
            }
        }));
} //# sourceMappingURL=safe.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/walletConnect.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "walletConnect": (()=>walletConnect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/config.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/errors/connector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/createConnector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$utils$2f$extractRpcUrls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/errors/rpc.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/address/getAddress.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/encoding/toHex.js [app-ssr] (ecmascript)");
;
;
walletConnect.type = 'walletConnect';
function walletConnect(parameters) {
    const isNewChainsStale = parameters.isNewChainsStale ?? true;
    let provider_;
    let providerPromise;
    const NAMESPACE = 'eip155';
    let accountsChanged;
    let chainChanged;
    let connect;
    let displayUri;
    let sessionDelete;
    let disconnect;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$createConnector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConnector"])((config)=>({
            id: 'walletConnect',
            name: 'WalletConnect',
            type: walletConnect.type,
            async setup () {
                const provider = await this.getProvider().catch(()=>null);
                if (!provider) return;
                if (!connect) {
                    connect = this.onConnect.bind(this);
                    provider.on('connect', connect);
                }
                if (!sessionDelete) {
                    sessionDelete = this.onSessionDelete.bind(this);
                    provider.on('session_delete', sessionDelete);
                }
            },
            async connect ({ chainId, ...rest } = {}) {
                try {
                    const provider = await this.getProvider();
                    if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                    if (!displayUri) {
                        displayUri = this.onDisplayUri;
                        provider.on('display_uri', displayUri);
                    }
                    let targetChainId = chainId;
                    if (!targetChainId) {
                        const state = await config.storage?.getItem('state') ?? {};
                        const isChainSupported = config.chains.some((x)=>x.id === state.chainId);
                        if (isChainSupported) targetChainId = state.chainId;
                        else targetChainId = config.chains[0]?.id;
                    }
                    if (!targetChainId) throw new Error('No chains found on connector.');
                    const isChainsStale = await this.isChainsStale();
                    // If there is an active session with stale chains, disconnect current session.
                    if (provider.session && isChainsStale) await provider.disconnect();
                    // If there isn't an active session or chains are stale, connect.
                    if (!provider.session || isChainsStale) {
                        const optionalChains = config.chains.filter((chain)=>chain.id !== targetChainId).map((optionalChain)=>optionalChain.id);
                        await provider.connect({
                            optionalChains: [
                                targetChainId,
                                ...optionalChains
                            ],
                            ...'pairingTopic' in rest ? {
                                pairingTopic: rest.pairingTopic
                            } : {}
                        });
                        this.setRequestedChainsIds(config.chains.map((x)=>x.id));
                    }
                    // If session exists and chains are authorized, enable provider for required chain
                    const accounts = (await provider.enable()).map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
                    const currentChainId = await this.getChainId();
                    if (displayUri) {
                        provider.removeListener('display_uri', displayUri);
                        displayUri = undefined;
                    }
                    if (connect) {
                        provider.removeListener('connect', connect);
                        connect = undefined;
                    }
                    if (!accountsChanged) {
                        accountsChanged = this.onAccountsChanged.bind(this);
                        provider.on('accountsChanged', accountsChanged);
                    }
                    if (!chainChanged) {
                        chainChanged = this.onChainChanged.bind(this);
                        provider.on('chainChanged', chainChanged);
                    }
                    if (!disconnect) {
                        disconnect = this.onDisconnect.bind(this);
                        provider.on('disconnect', disconnect);
                    }
                    if (!sessionDelete) {
                        sessionDelete = this.onSessionDelete.bind(this);
                        provider.on('session_delete', sessionDelete);
                    }
                    return {
                        accounts,
                        chainId: currentChainId
                    };
                } catch (error) {
                    if (/(user rejected|connection request reset)/i.test(error?.message)) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    }
                    throw error;
                }
            },
            async disconnect () {
                const provider = await this.getProvider();
                try {
                    await provider?.disconnect();
                } catch (error) {
                    if (!/No matching key/i.test(error.message)) throw error;
                } finally{
                    if (chainChanged) {
                        provider?.removeListener('chainChanged', chainChanged);
                        chainChanged = undefined;
                    }
                    if (disconnect) {
                        provider?.removeListener('disconnect', disconnect);
                        disconnect = undefined;
                    }
                    if (!connect) {
                        connect = this.onConnect.bind(this);
                        provider?.on('connect', connect);
                    }
                    if (accountsChanged) {
                        provider?.removeListener('accountsChanged', accountsChanged);
                        accountsChanged = undefined;
                    }
                    if (sessionDelete) {
                        provider?.removeListener('session_delete', sessionDelete);
                        sessionDelete = undefined;
                    }
                    this.setRequestedChainsIds([]);
                }
            },
            async getAccounts () {
                const provider = await this.getProvider();
                return provider.accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x));
            },
            async getProvider ({ chainId } = {}) {
                async function initProvider() {
                    const optionalChains = config.chains.map((x)=>x.id);
                    if (!optionalChains.length) return;
                    const { EthereumProvider } = await __turbopack_context__.r("[project]/node_modules/@walletconnect/ethereum-provider/dist/index.es.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                    return await EthereumProvider.init({
                        ...parameters,
                        disableProviderPing: true,
                        optionalChains,
                        projectId: parameters.projectId,
                        rpcMap: Object.fromEntries(config.chains.map((chain)=>{
                            const [url] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$utils$2f$extractRpcUrls$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractRpcUrls"])({
                                chain,
                                transports: config.transports
                            });
                            return [
                                chain.id,
                                url
                            ];
                        })),
                        showQrModal: parameters.showQrModal ?? true
                    });
                }
                if (!provider_) {
                    if (!providerPromise) providerPromise = initProvider();
                    provider_ = await providerPromise;
                    provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY);
                }
                if (chainId) await this.switchChain?.({
                    chainId
                });
                return provider_;
            },
            async getChainId () {
                const provider = await this.getProvider();
                return provider.chainId;
            },
            async isAuthorized () {
                try {
                    const [accounts, provider] = await Promise.all([
                        this.getAccounts(),
                        this.getProvider()
                    ]);
                    // If an account does not exist on the session, then the connector is unauthorized.
                    if (!accounts.length) return false;
                    // If the chains are stale on the session, then the connector is unauthorized.
                    const isChainsStale = await this.isChainsStale();
                    if (isChainsStale && provider.session) {
                        await provider.disconnect().catch(()=>{});
                        return false;
                    }
                    return true;
                } catch  {
                    return false;
                }
            },
            async switchChain ({ addEthereumChainParameter, chainId }) {
                const provider = await this.getProvider();
                if (!provider) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$connector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProviderNotFoundError"]();
                const chain = config.chains.find((x)=>x.id === chainId);
                if (!chain) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SwitchChainError"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$errors$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainNotConfiguredError"]());
                try {
                    await Promise.all([
                        new Promise((resolve)=>{
                            const listener = ({ chainId: currentChainId })=>{
                                if (currentChainId === chainId) {
                                    config.emitter.off('change', listener);
                                    resolve();
                                }
                            };
                            config.emitter.on('change', listener);
                        }),
                        provider.request({
                            method: 'wallet_switchEthereumChain',
                            params: [
                                {
                                    chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId)
                                }
                            ]
                        })
                    ]);
                    const requestedChains = await this.getRequestedChainsIds();
                    this.setRequestedChainsIds([
                        ...requestedChains,
                        chainId
                    ]);
                    return chain;
                } catch (err) {
                    const error = err;
                    if (/(user rejected)/i.test(error.message)) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    // Indicates chain is not added to provider
                    try {
                        let blockExplorerUrls;
                        if (addEthereumChainParameter?.blockExplorerUrls) blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;
                        else blockExplorerUrls = chain.blockExplorers?.default.url ? [
                            chain.blockExplorers?.default.url
                        ] : [];
                        let rpcUrls;
                        if (addEthereumChainParameter?.rpcUrls?.length) rpcUrls = addEthereumChainParameter.rpcUrls;
                        else rpcUrls = [
                            ...chain.rpcUrls.default.http
                        ];
                        const addEthereumChain = {
                            blockExplorerUrls,
                            chainId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$encoding$2f$toHex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["numberToHex"])(chainId),
                            chainName: addEthereumChainParameter?.chainName ?? chain.name,
                            iconUrls: addEthereumChainParameter?.iconUrls,
                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,
                            rpcUrls
                        };
                        await provider.request({
                            method: 'wallet_addEthereumChain',
                            params: [
                                addEthereumChain
                            ]
                        });
                        const requestedChains = await this.getRequestedChainsIds();
                        this.setRequestedChainsIds([
                            ...requestedChains,
                            chainId
                        ]);
                        return chain;
                    } catch (error) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$errors$2f$rpc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UserRejectedRequestError"](error);
                    }
                }
            },
            onAccountsChanged (accounts) {
                if (accounts.length === 0) this.onDisconnect();
                else config.emitter.emit('change', {
                    accounts: accounts.map((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$address$2f$getAddress$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAddress"])(x))
                });
            },
            onChainChanged (chain) {
                const chainId = Number(chain);
                config.emitter.emit('change', {
                    chainId
                });
            },
            async onConnect (connectInfo) {
                const chainId = Number(connectInfo.chainId);
                const accounts = await this.getAccounts();
                config.emitter.emit('connect', {
                    accounts,
                    chainId
                });
            },
            async onDisconnect (_error) {
                this.setRequestedChainsIds([]);
                config.emitter.emit('disconnect');
                const provider = await this.getProvider();
                if (accountsChanged) {
                    provider.removeListener('accountsChanged', accountsChanged);
                    accountsChanged = undefined;
                }
                if (chainChanged) {
                    provider.removeListener('chainChanged', chainChanged);
                    chainChanged = undefined;
                }
                if (disconnect) {
                    provider.removeListener('disconnect', disconnect);
                    disconnect = undefined;
                }
                if (sessionDelete) {
                    provider.removeListener('session_delete', sessionDelete);
                    sessionDelete = undefined;
                }
                if (!connect) {
                    connect = this.onConnect.bind(this);
                    provider.on('connect', connect);
                }
            },
            onDisplayUri (uri) {
                config.emitter.emit('message', {
                    type: 'display_uri',
                    data: uri
                });
            },
            onSessionDelete () {
                this.onDisconnect();
            },
            getNamespaceChainsIds () {
                if (!provider_) return [];
                const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map((account)=>Number.parseInt(account.split(':')[1] || ''));
                return chainIds ?? [];
            },
            async getRequestedChainsIds () {
                return await config.storage?.getItem(this.requestedChainsStorageKey) ?? [];
            },
            /**
         * Checks if the target chains match the chains that were
         * initially requested by the connector for the WalletConnect session.
         * If there is a mismatch, this means that the chains on the connector
         * are considered stale, and need to be revalidated at a later point (via
         * connection).
         *
         * There may be a scenario where a dapp adds a chain to the
         * connector later on, however, this chain will not have been approved or rejected
         * by the wallet. In this case, the chain is considered stale.
         */ async isChainsStale () {
                if (!isNewChainsStale) return false;
                const connectorChains = config.chains.map((x)=>x.id);
                const namespaceChains = this.getNamespaceChainsIds();
                if (namespaceChains.length && !namespaceChains.some((id)=>connectorChains.includes(id))) return false;
                const requestedChains = await this.getRequestedChainsIds();
                return !connectorChains.every((id)=>requestedChains.includes(id));
            },
            async setRequestedChainsIds (chains) {
                await config.storage?.setItem(this.requestedChainsStorageKey, chains);
            },
            get requestedChainsStorageKey () {
                return `${this.id}.requestedChains`;
            }
        }));
} //# sourceMappingURL=walletConnect.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/version.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "version": (()=>version)
});
const version = '5.8.3'; //# sourceMappingURL=version.js.map
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "coinbaseWallet": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$coinbaseWallet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["coinbaseWallet"]),
    "injected": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$injected$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["injected"]),
    "metaMask": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$metaMask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["metaMask"]),
    "mock": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$mock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mock"]),
    "safe": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$safe$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["safe"]),
    "version": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]),
    "walletConnect": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$walletConnect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["walletConnect"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$injected$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/injected.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$core$2f$dist$2f$esm$2f$connectors$2f$mock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/core/dist/esm/connectors/mock.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$coinbaseWallet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$metaMask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/metaMask.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$safe$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/safe.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$walletConnect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/walletConnect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "coinbaseWallet": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["coinbaseWallet"]),
    "injected": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["injected"]),
    "metaMask": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["metaMask"]),
    "mock": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["mock"]),
    "safe": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["safe"]),
    "version": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["version"]),
    "walletConnect": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["walletConnect"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wagmi$2f$connectors$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=node_modules_7f416510._.js.map