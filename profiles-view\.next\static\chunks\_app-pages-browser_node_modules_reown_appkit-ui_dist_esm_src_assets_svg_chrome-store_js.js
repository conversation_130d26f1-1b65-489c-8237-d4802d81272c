"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_chrome-store_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chromeStoreSvg: () => (/* binding */ chromeStoreSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst chromeStoreSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"36\" height=\"36\" fill=\"none\">\n  <path\n    fill=\"#fff\"\n    fill-opacity=\".05\"\n    d=\"M0 14.94c0-5.55 0-8.326 1.182-10.4a9 9 0 0 1 3.359-3.358C6.614 0 9.389 0 14.94 0h6.12c5.55 0 8.326 0 10.4 1.182a9 9 0 0 1 3.358 3.359C36 6.614 36 9.389 36 14.94v6.12c0 5.55 0 8.326-1.182 10.4a9 9 0 0 1-3.359 3.358C29.386 36 26.611 36 21.06 36h-6.12c-5.55 0-8.326 0-10.4-1.182a9 9 0 0 1-3.358-3.359C0 29.386 0 26.611 0 21.06v-6.12Z\"\n  />\n  <path\n    stroke=\"#fff\"\n    stroke-opacity=\".05\"\n    d=\"M14.94.5h6.12c2.785 0 4.84 0 6.46.146 1.612.144 2.743.43 3.691.97a8.5 8.5 0 0 1 3.172 3.173c.541.948.826 2.08.971 3.692.145 1.62.146 3.675.146 6.459v6.12c0 2.785 0 4.84-.146 6.46-.145 1.612-.43 2.743-.97 3.691a8.5 8.5 0 0 1-3.173 3.172c-.948.541-2.08.826-3.692.971-1.62.145-3.674.146-6.459.146h-6.12c-2.784 0-4.84 0-6.46-.146-1.612-.145-2.743-.43-3.691-.97a8.5 8.5 0 0 1-3.172-3.173c-.541-.948-.827-2.08-.971-3.692C.5 25.9.5 23.845.5 21.06v-6.12c0-2.784 0-4.84.146-6.46.144-1.612.43-2.743.97-3.691A8.5 8.5 0 0 1 4.79 1.617C5.737 1.076 6.869.79 8.48.646 10.1.5 12.156.5 14.94.5Z\"\n  />\n  <path\n    fill=\"url(#a)\"\n    d=\"M17.998 10.8h12.469a14.397 14.397 0 0 0-24.938.001l6.234 10.798.006-.001a7.19 7.19 0 0 1 6.23-10.799Z\"\n  />\n  <path\n    fill=\"url(#b)\"\n    d=\"m24.237 21.598-6.234 10.798A14.397 14.397 0 0 0 30.47 10.798H18.002l-.002.006a7.191 7.191 0 0 1 6.237 10.794Z\"\n  />\n  <path\n    fill=\"url(#c)\"\n    d=\"M11.765 21.601 5.531 10.803A14.396 14.396 0 0 0 18.001 32.4l6.235-10.798-.004-.004a7.19 7.19 0 0 1-12.466.004Z\"\n  />\n  <path fill=\"#fff\" d=\"M18 25.2a7.2 7.2 0 1 0 0-14.4 7.2 7.2 0 0 0 0 14.4Z\" />\n  <path fill=\"#1A73E8\" d=\"M18 23.7a5.7 5.7 0 1 0 0-11.4 5.7 5.7 0 0 0 0 11.4Z\" />\n  <defs>\n    <linearGradient\n      id=\"a\"\n      x1=\"6.294\"\n      x2=\"41.1\"\n      y1=\"5.995\"\n      y2=\"5.995\"\n      gradientUnits=\"userSpaceOnUse\"\n    >\n      <stop stop-color=\"#D93025\" />\n      <stop offset=\"1\" stop-color=\"#EA4335\" />\n    </linearGradient>\n    <linearGradient\n      id=\"b\"\n      x1=\"20.953\"\n      x2=\"37.194\"\n      y1=\"32.143\"\n      y2=\"2.701\"\n      gradientUnits=\"userSpaceOnUse\"\n    >\n      <stop stop-color=\"#FCC934\" />\n      <stop offset=\"1\" stop-color=\"#FBBC04\" />\n    </linearGradient>\n    <linearGradient\n      id=\"c\"\n      x1=\"25.873\"\n      x2=\"9.632\"\n      y1=\"31.2\"\n      y2=\"1.759\"\n      gradientUnits=\"userSpaceOnUse\"\n    >\n      <stop stop-color=\"#1E8E3E\" />\n      <stop offset=\"1\" stop-color=\"#34A853\" />\n    </linearGradient>\n  </defs>\n</svg>`;\n//# sourceMappingURL=chrome-store.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js\n"));

/***/ })

}]);