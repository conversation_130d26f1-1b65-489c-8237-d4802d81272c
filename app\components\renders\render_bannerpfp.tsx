'use client';

import { useState, useEffect, useRef } from 'react';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';
import DecryptedText from '@/components/ui/DecryptedText';

interface BannerPfpMetadata {
  bannerUrl: string;
  bannerScale: number;
  bannerPosition: { x: number; y: number };
  bannerNaturalSize: { width: number; height: number } | null;
  profileUrl: string;
  profileScale: number;
  profilePosition: { x: number; y: number };
  profileNaturalSize: { width: number; height: number } | null;
  profileShape?: string;
  profileHorizontalPosition?: number; // 0-100 for horizontal position percentage
  profileName?: string;
  profileBio?: string;
  profileNameStyle?: {
    fontSize: string;
    fontWeight: string;
    fontColor: string;
    effect: 'none' | 'typewriter' | 'decrypted';
  };
}

interface RenderBannerPfpProps {
  address: string;
  componentData: {
    address: string;
    chain: string;
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    fontColor?: string | null;
  };
  showPositionLabel?: boolean;
  profileName?: string;
  profileBio?: string;
}

export default function RenderBannerPfp({
  address,
  componentData,
  showPositionLabel = false,
  profileName: propProfileName,
  profileBio: propProfileBio
}: RenderBannerPfpProps) {
  // State for component data
  const [bannerPfpMetadata, setBannerPfpMetadata] = useState<BannerPfpMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileName, setProfileName] = useState<string | null>(null);
  const [profileBio, setProfileBio] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [profileHorizontalPosition, setProfileHorizontalPosition] = useState<number>(50); // Default to center (50%)
  const [profileNameHorizontalPosition, setProfileNameHorizontalPosition] = useState<number>(50); // Default to center (50%)
  const [profileShape, setProfileShape] = useState<string>('circular');

  // Refs for dragging
  const containerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startPositionRef = useRef<number>(50);

  // We'll initialize profileName in the main useEffect to prioritize bannerpfp data

  // Determine the border radius based on the shape
  const getBorderRadiusClass = () => {
    switch (profileShape) {
      case 'rectangular':
        return 'rounded-none'; // Completely rectangular (no rounded corners)
      case 'squarish':
        return 'rounded-md'; // Slightly rounded corners
      case 'circular':
      default:
        return 'rounded-full';
    }
  };

  useEffect(() => {

    const loadBannerPfpMetadata = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch bannerpfp metadata from API
        const response = await fetch(`/api/bannerpfp/${address}`);

        if (!response.ok) {
          throw new Error('Failed to load banner and profile picture data');
        }

        const data = await response.json();
        // API response data received

        if (data) {
          setBannerPfpMetadata(data);

          // Set profile shape
          if (data.profileShape) {
            setProfileShape(data.profileShape);
          }

          // Set horizontal positions
          if (data.profileHorizontalPosition !== undefined) {
            setProfileHorizontalPosition(data.profileHorizontalPosition);
          }

          // Set profile name horizontal position
          if (data.profileNameHorizontalPosition !== undefined) {
            setProfileNameHorizontalPosition(data.profileNameHorizontalPosition);
          }

          // Always use the profile name from the bannerpfp component data
          if (data.profileName) {
            setProfileName(data.profileName);
          } else if (propProfileName) {
            setProfileName(propProfileName);
          } else if (!profileName) {
            // Fallback to a default name if nothing is available
            setProfileName(address.substring(0, 8));
          }

          if (!propProfileBio && data.profileBio) {
            setProfileBio(data.profileBio);
          }
        } else {
          setError('No banner and profile picture found');
        }
      } catch (error) {
        console.error('Error loading banner and profile picture:', error);
        setError('Failed to load banner and profile picture');
      } finally {
        setIsLoading(false);
      }
    };

    if (address) {
      loadBannerPfpMetadata();
    }
  }, [address, propProfileName, propProfileBio]);

  // Direct handler for profile picture horizontal position
  const handleProfilePositionChange = (e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>, isInitialDrag: boolean = false) => {
    if (!containerRef.current) return;

    // Get container dimensions
    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;

    // Get the x position relative to the container
    let clientX;
    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0].clientX;
    } else {
      // Mouse event
      clientX = e.clientX;
    }

    // Calculate position as percentage
    const offsetX = clientX - rect.left;
    const percentage = (offsetX / containerWidth) * 100;

    // Constrain between 10% and 90%
    const newPosition = Math.max(10, Math.min(90, percentage));

    // Update state
    setProfileHorizontalPosition(newPosition);

    // Only set dragging state on initial drag, not during move
    if (isInitialDrag) {
      setIsDragging(true);
    }
  };

  // Direct handler for profile name horizontal position
  const handleProfileNamePositionChange = (e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>, isInitialDrag: boolean = false) => {
    if (!containerRef.current) return;

    // Get container dimensions
    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;

    // Get the x position relative to the container
    let clientX;
    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0].clientX;
    } else {
      // Mouse event
      clientX = e.clientX;
    }

    // Calculate position as percentage
    const offsetX = clientX - rect.left;
    const percentage = (offsetX / containerWidth) * 100;

    // Constrain between 10% and 90%
    const newPosition = Math.max(10, Math.min(90, percentage));

    // Update state
    setProfileNameHorizontalPosition(newPosition);

    // Only set dragging state on initial drag, not during move
    if (isInitialDrag) {
      setIsDragging(true);
    }
  };

  // Handle mouse down for profile picture dragging
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault(); // Prevent text selection

    // Initial position update
    handleProfilePositionChange(e, true);

    // Set up mouse move handler
    const handleMouseMove = (moveEvent: MouseEvent) => {
      // Convert to React event-like object
      const syntheticEvent = {
        clientX: moveEvent.clientX,
        preventDefault: () => moveEvent.preventDefault()
      } as unknown as React.MouseEvent<HTMLDivElement>;

      handleProfilePositionChange(syntheticEvent, false);
    };

    // Set up mouse up handler
    const handleMouseUp = async () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Save the new position to the server
      try {
        const response = await fetch(`/api/bannerpfp/${address}/position`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profileHorizontalPosition
          }),
        });

        if (!response.ok) {
          console.error('Failed to save profile position');
        }
      } catch (error) {
        console.error('Error saving profile position:', error);
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle touch start for profile picture dragging
  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // Initial position update
    handleProfilePositionChange(e, true);

    // Set up touch move handler
    const handleTouchMove = (moveEvent: TouchEvent) => {
      moveEvent.preventDefault(); // Prevent scrolling

      // Convert to React event-like object
      const syntheticEvent = {
        touches: moveEvent.touches,
        preventDefault: () => moveEvent.preventDefault()
      } as unknown as React.TouchEvent<HTMLDivElement>;

      handleProfilePositionChange(syntheticEvent, false);
    };

    // Set up touch end handler
    const handleTouchEnd = async () => {
      setIsDragging(false);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      // Save the new position to the server
      try {
        const response = await fetch(`/api/bannerpfp/${address}/position`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profileHorizontalPosition
          }),
        });

        if (!response.ok) {
          console.error('Failed to save profile position');
        }
      } catch (error) {
        console.error('Error saving profile position:', error);
      }
    };

    // Add event listeners
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  // Handle mouse down for profile name dragging
  const handleProfileNameMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault(); // Prevent text selection

    // Initial position update
    handleProfileNamePositionChange(e, true);

    // Set up mouse move handler
    const handleMouseMove = (moveEvent: MouseEvent) => {
      // Convert to React event-like object
      const syntheticEvent = {
        clientX: moveEvent.clientX,
        preventDefault: () => moveEvent.preventDefault()
      } as unknown as React.MouseEvent<HTMLDivElement>;

      handleProfileNamePositionChange(syntheticEvent, false);
    };

    // Set up mouse up handler
    const handleMouseUp = async () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Save the new position to the server
      try {
        const response = await fetch(`/api/bannerpfp/${address}/profilename-position`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profileNameHorizontalPosition
          }),
        });

        if (!response.ok) {
          console.error('Failed to save profile name position');
        }
      } catch (error) {
        console.error('Error saving profile name position:', error);
      }
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle touch start for profile name dragging
  const handleProfileNameTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // Initial position update
    handleProfileNamePositionChange(e, true);

    // Set up touch move handler
    const handleTouchMove = (moveEvent: TouchEvent) => {
      moveEvent.preventDefault(); // Prevent scrolling

      // Convert to React event-like object
      const syntheticEvent = {
        touches: moveEvent.touches,
        preventDefault: () => moveEvent.preventDefault()
      } as unknown as React.TouchEvent<HTMLDivElement>;

      handleProfileNamePositionChange(syntheticEvent, false);
    };

    // Set up touch end handler
    const handleTouchEnd = async () => {
      setIsDragging(false);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      // Save the new position to the server
      try {
        const response = await fetch(`/api/bannerpfp/${address}/profilename-position`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profileNameHorizontalPosition
          }),
        });

        if (!response.ok) {
          console.error('Failed to save profile name position');
        }
      } catch (error) {
        console.error('Error saving profile name position:', error);
      }
    };

    // Add event listeners
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  if (isLoading) {
    return (
      <div className="w-full h-48 md:h-64 bg-transparent animate-pulse"></div>
    );
  }

  if (error || !bannerPfpMetadata) {
    return (
      <div className="w-full h-48 md:h-64 bg-transparent flex items-center justify-center">
        <p className="text-neutral-500">Banner and profile picture not available</p>
      </div>
    );
  }

  return (
    <div
      className="relative border border-neutral-800 overflow-hidden w-full min-w-full"
    >
      {/* Part 1: Banner, Profile Picture, and Name (with background color) */}
      {/* Background color container - covers from top to profile name */}
      <div style={{ backgroundColor: componentData.backgroundColor, width: '100%', minWidth: '100%', boxSizing: 'border-box', paddingBottom: '0.5rem' }} className="w-full min-w-full">
        {/* Container with relative positioning to hold both banner and profile picture */}
        <div className="relative w-full" style={{
          marginBottom: profileShape === 'rectangular' ? '9rem' : '8rem',
          width: '100%'
        }}>
          {/* Banner container with fixed height */}
          <div
            className="w-full h-48 md:h-64 relative overflow-hidden"
            ref={containerRef}
            style={{ width: '100%', minWidth: '100%' }}
          >
            {bannerPfpMetadata.bannerUrl && (
              <div
                className="absolute inset-0"
                style={{
                  backgroundImage: `url(${bannerPfpMetadata.bannerUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  transform: `translate(${bannerPfpMetadata.bannerPosition?.x || 0}px, ${bannerPfpMetadata.bannerPosition?.y || 0}px) scale(${bannerPfpMetadata.bannerScale || 1})`,
                  transformOrigin: 'center',
              }}
            />
          )}

          {/* Position indicator line - only shown when editing */}
          {showPositionLabel && (
            <div
              className="absolute bottom-0 w-0.5 h-6 bg-blue-500 opacity-50"
              style={{
                left: `${profileHorizontalPosition}%`,
                transform: 'translateX(-50%)'
              }}
            />
          )}
          </div>

          {/* Profile Picture Section - Positioned at the bottom of the banner */}
          <div
            className="absolute opacity-100"
            style={{
              left: `${profileHorizontalPosition}%`,
              bottom: profileShape === 'rectangular' ? '-72px' : '-64px', /* Position so exactly half overlaps the banner */
              transform: `translateX(-50%)`, /* No vertical translation needed */
              zIndex: 10
          }}
        >
          {/* Removed drag handle indicator */}

          {/* Percentage indicator - hidden */}

          {/* Position indicator - the profile picture itself with percentage overlay - only shown when editing */}
          {showPositionLabel && isDragging && (
            <div
              className="absolute"
              style={{
                left: `${profileHorizontalPosition}%`,
                bottom: profileShape === 'rectangular' ? '-72px' : '-64px', /* Position so exactly half overlaps the banner */
                transform: `translateX(-50%)`, /* No vertical translation needed */
                zIndex: 20,
                pointerEvents: 'none' // Prevent this from interfering with mouse events
              }}
            >
              <div className={`${profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative`}>
                {bannerPfpMetadata.profileUrl ? (
                  <img
                    src={bannerPfpMetadata.profileUrl}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-neutral-800 flex items-center justify-center">
                    <span className="text-neutral-400 text-xs">No image</span>
                  </div>
                )}
                {/* Percentage overlay - hidden */}
              </div>
            </div>
          )}
          <div
            className={`${profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative`}
          >

            {bannerPfpMetadata.profileUrl ? (
              <img
                src={bannerPfpMetadata.profileUrl}
                alt="Profile"
                className="w-full h-full object-cover"
                style={{
                  transform: `scale(${bannerPfpMetadata.profileScale || 1})`,
                  transformOrigin: 'center',
                }}
              />
            ) : (
              <div className="w-full h-full bg-neutral-800 flex items-center justify-center">
                <span className="text-neutral-400 text-xs">No Image</span>
              </div>
            )}
          </div>
        </div>
        </div>

        {/* Profile Name Section - Positioned in line with the profile picture */}
        <div className="relative w-full" style={{ height: '2rem', marginTop: '-8.5rem', marginBottom: '3rem' }}>
          {/* Profile Name - Using profileNameHorizontalPosition from database if available */}
          <div
            className="absolute"
            style={{
              left: `${profileNameHorizontalPosition}%`,
              top: '0px',
              transform: `translateX(-50%)`,
              zIndex: 10
          }}
        >
            {/* Profile Name */}
            <div className="text-center px-0 py-0 bg-transparent whitespace-nowrap inline-block" style={{ paddingTop: '1.75rem' }}>
              {profileName && (
                <div>
                  {bannerPfpMetadata.profileNameStyle?.effect === 'typewriter' ? (
                    <HeaderTypewriterEffect
                      words={[{
                        text: profileName,
                        className: ''
                      }]}
                      className="inline-flex"
                      cursorClassName="text-white"
                      style={{
                        color: componentData.fontColor || '#ffffff',
                        fontSize: '1.5rem',
                        fontWeight: 'bold',
                        lineHeight: 1
                      }}
                    />
                  ) : bannerPfpMetadata.profileNameStyle?.effect === 'decrypted' ? (
                    <DecryptedText
                      text={profileName}
                      animateOn="view"
                      speed={50}
                      maxIterations={15}
                      revealDirection="start"
                      sequential={true}
                      className=""
                      style={{
                        color: componentData.fontColor || '#ffffff',
                        fontSize: '1.5rem',
                        fontWeight: 'bold',
                        lineHeight: 1
                      }}
                    />
                  ) : (
                    <div style={{
                      color: componentData.fontColor || '#ffffff',
                      fontSize: '1.5rem',
                      fontWeight: 'bold',
                      lineHeight: 1
                    }}>
                      {profileName}
                    </div>
                  )}
              </div>
            )}
          </div>
        </div>
        </div>
      </div>

      {/* Part 2: Additional information (without background color) */}
      {showPositionLabel && (
        <div className="absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
          BannerPfp
        </div>
      )}
    </div>
  );
}
