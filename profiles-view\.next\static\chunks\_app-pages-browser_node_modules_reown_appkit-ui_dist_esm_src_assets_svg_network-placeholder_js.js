"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_network-placeholder_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   networkPlaceholderSvg: () => (/* binding */ networkPlaceholderSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst networkPlaceholderSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 22 20\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M16.32 13.62a3.14 3.14 0 1 1-.99 1.72l-1.6-.93a3.83 3.83 0 0 1-3.71 1 3.66 3.66 0 0 1-1.74-1l-1.6.94a3.14 3.14 0 1 1-1-1.73l1.6-.94a3.7 3.7 0 0 1 0-2 3.81 3.81 0 0 1 1.8-2.33c.29-.17.6-.3.92-.38V6.1a3.14 3.14 0 1 1 2 0l-.01.02v1.85H12a3.82 3.82 0 0 1 2.33 1.8 3.7 3.7 0 0 1 .39 2.91l1.6.93ZM2.6 16.54a1.14 1.14 0 0 0 1.98-1.14 1.14 1.14 0 0 0-1.98 1.14ZM11 2.01a1.14 1.14 0 1 0 0 2.28 1.14 1.14 0 0 0 0-2.28Zm1.68 10.45c.08-.19.14-.38.16-.58v-.05l.02-.13v-.13a1.92 1.92 0 0 0-.24-.8l-.11-.15a1.89 1.89 0 0 0-.74-.6 1.86 1.86 0 0 0-.77-.17h-.19a1.97 1.97 0 0 0-.89.34 1.98 1.98 0 0 0-.61.74 1.99 1.99 0 0 0-.16.9v.05a1.87 1.87 0 0 0 .24.74l.1.15c.**********.42.42l.**********.04.02a1.84 1.84 0 0 0 .76.17h.17a2 2 0 0 0 .91-.35 1.78 1.78 0 0 0 .52-.58l.03-.05a.84.84 0 0 0 .05-.11Zm5.15 4.5a1.14 1.14 0 0 0 1.14-1.97 1.13 1.13 0 0 0-1.55.41c-.32.55-.13 1.25.41 1.56Z\"\n    clip-rule=\"evenodd\"\n  />\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M4.63 9.43a1.5 1.5 0 1 0 1.5-2.6 1.5 1.5 0 0 0-1.5 2.6Zm.32-1.55a.5.5 0 0 1 .68-.19.5.5 0 0 1 .********* 0 0 1-.********* 0 0 1-.18-.68ZM17.94 8.88a1.5 1.5 0 1 1-2.6-1.5 1.5 1.5 0 1 1 2.6 1.5ZM16.9 7.69a.5.5 0 0 0-.********* 0 0 0 .********* 0 0 0 .68-.19.5.5 0 0 0-.18-.68ZM9.75 17.75a1.5 1.5 0 1 1 2.6 1.5 1.5 1.5 0 1 1-2.6-1.5Zm1.05 1.18a.5.5 0 0 0 .68-.18.5.5 0 0 0-.18-.68.5.5 0 0 0-.********* 0 0 0 .18.68Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=network-placeholder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL25ldHdvcmstcGxhY2Vob2xkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsOEJBQThCLHdDQUFHO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHJlb3duXFxhcHBraXQtdWlcXGRpc3RcXGVzbVxcc3JjXFxhc3NldHNcXHN2Z1xcbmV0d29yay1wbGFjZWhvbGRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IG5ldHdvcmtQbGFjZWhvbGRlclN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjIgMjBcIj5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBmaWxsLXJ1bGU9XCJldmVub2RkXCJcbiAgICBkPVwiTTE2LjMyIDEzLjYyYTMuMTQgMy4xNCAwIDEgMS0uOTkgMS43MmwtMS42LS45M2EzLjgzIDMuODMgMCAwIDEtMy43MSAxIDMuNjYgMy42NiAwIDAgMS0xLjc0LTFsLTEuNi45NGEzLjE0IDMuMTQgMCAxIDEtMS0xLjczbDEuNi0uOTRhMy43IDMuNyAwIDAgMSAwLTIgMy44MSAzLjgxIDAgMCAxIDEuOC0yLjMzYy4yOS0uMTcuNi0uMy45Mi0uMzhWNi4xYTMuMTQgMy4xNCAwIDEgMSAyIDBsLS4wMS4wMnYxLjg1SDEyYTMuODIgMy44MiAwIDAgMSAyLjMzIDEuOCAzLjcgMy43IDAgMCAxIC4zOSAyLjkxbDEuNi45M1pNMi42IDE2LjU0YTEuMTQgMS4xNCAwIDAgMCAxLjk4LTEuMTQgMS4xNCAxLjE0IDAgMCAwLTEuOTggMS4xNFpNMTEgMi4wMWExLjE0IDEuMTQgMCAxIDAgMCAyLjI4IDEuMTQgMS4xNCAwIDAgMCAwLTIuMjhabTEuNjggMTAuNDVjLjA4LS4xOS4xNC0uMzguMTYtLjU4di0uMDVsLjAyLS4xM3YtLjEzYTEuOTIgMS45MiAwIDAgMC0uMjQtLjhsLS4xMS0uMTVhMS44OSAxLjg5IDAgMCAwLS43NC0uNiAxLjg2IDEuODYgMCAwIDAtLjc3LS4xN2gtLjE5YTEuOTcgMS45NyAwIDAgMC0uODkuMzQgMS45OCAxLjk4IDAgMCAwLS42MS43NCAxLjk5IDEuOTkgMCAwIDAtLjE2Ljl2LjA1YTEuODcgMS44NyAwIDAgMCAuMjQuNzRsLjEuMTVjLjEyLjE2LjI2LjMuNDIuNDJsLjE2LjEuMTMuMDcuMDQuMDJhMS44NCAxLjg0IDAgMCAwIC43Ni4xN2guMTdhMiAyIDAgMCAwIC45MS0uMzUgMS43OCAxLjc4IDAgMCAwIC41Mi0uNThsLjAzLS4wNWEuODQuODQgMCAwIDAgLjA1LS4xMVptNS4xNSA0LjVhMS4xNCAxLjE0IDAgMCAwIDEuMTQtMS45NyAxLjEzIDEuMTMgMCAwIDAtMS41NS40MWMtLjMyLjU1LS4xMyAxLjI1LjQxIDEuNTZaXCJcbiAgICBjbGlwLXJ1bGU9XCJldmVub2RkXCJcbiAgLz5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBmaWxsLXJ1bGU9XCJldmVub2RkXCJcbiAgICBkPVwiTTQuNjMgOS40M2ExLjUgMS41IDAgMSAwIDEuNS0yLjYgMS41IDEuNSAwIDAgMC0xLjUgMi42Wm0uMzItMS41NWEuNS41IDAgMCAxIC42OC0uMTkuNS41IDAgMCAxIC4xOC42OC41LjUgMCAwIDEtLjY4LjE5LjUuNSAwIDAgMS0uMTgtLjY4Wk0xNy45NCA4Ljg4YTEuNSAxLjUgMCAxIDEtMi42LTEuNSAxLjUgMS41IDAgMSAxIDIuNiAxLjVaTTE2LjkgNy42OWEuNS41IDAgMCAwLS42OC4xOS41LjUgMCAwIDAgLjE4LjY4LjUuNSAwIDAgMCAuNjgtLjE5LjUuNSAwIDAgMC0uMTgtLjY4Wk05Ljc1IDE3Ljc1YTEuNSAxLjUgMCAxIDEgMi42IDEuNSAxLjUgMS41IDAgMSAxLTIuNi0xLjVabTEuMDUgMS4xOGEuNS41IDAgMCAwIC42OC0uMTguNS41IDAgMCAwLS4xOC0uNjguNS41IDAgMCAwLS42OC4xOC41LjUgMCAwIDAgLjE4LjY4WlwiXG4gICAgY2xpcC1ydWxlPVwiZXZlbm9kZFwiXG4gIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmV0d29yay1wbGFjZWhvbGRlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js\n"));

/***/ })

}]);