import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Reset database script
 * This script will:
 * 1. Drop all existing tables
 * WARNING: This will delete all data in the database!
 */
async function resetDatabase() {
  try {
    console.log('Starting database reset...');

    // Drop all tables
    console.log('Dropping all tables...');

    // Drop tables with foreign keys first
    // Drop profileReferrals table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS profileReferrals`);

    // Drop profileLikes table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS profileLikes`);

    // Drop componentImages table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS componentImages`);

    // Drop componentPositions table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS componentPositions`);

    // Drop waitingList table
    await db.execute(sql`DROP TABLE IF EXISTS waitingList`);

    // Drop web3Profile table
    await db.execute(sql`DROP TABLE IF EXISTS web3Profile`);

    // Drop systemSettings table
    await db.execute(sql`DROP TABLE IF EXISTS system_settings`);

    console.log('All tables dropped successfully');

    // Create tables using SQL statements
    console.log('Creating tables using SQL statements...');

    // Create systemSettings table
    await db.execute(sql`
      CREATE TABLE system_settings (
        id VARCHAR(50) PRIMARY KEY,
        value JSON NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create web3Profile table
    await db.execute(sql`
      CREATE TABLE web3Profile (
        address VARCHAR(255) PRIMARY KEY,
        chain VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        theme JSON,
        role VARCHAR(50) NOT NULL DEFAULT 'user',
        status VARCHAR(50) NOT NULL DEFAULT 'new',
        expiry_date TIMESTAMP NULL,
        transaction_hash VARCHAR(255) NULL,
        referral_code VARCHAR(8) NULL,
        referred_by VARCHAR(8) NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY referral_code_idx (referral_code)
      )
    `);

    // Create waitingList table
    await db.execute(sql`
      CREATE TABLE waitingList (
        address VARCHAR(255) PRIMARY KEY,
        chain VARCHAR(255) NOT NULL,
        xHandle TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create componentPositions table
    await db.execute(sql`
      CREATE TABLE componentPositions (
        address VARCHAR(255) NOT NULL,
        chain VARCHAR(255) NOT NULL,
        component_type VARCHAR(50) NOT NULL,
        \`order\` VARCHAR(10) NOT NULL,
        hidden VARCHAR(1) NOT NULL DEFAULT 'N',
        details JSON NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (address, component_type),
        FOREIGN KEY (address) REFERENCES web3Profile(address) ON DELETE CASCADE
      )
    `);

    // Create componentImages table
    await db.execute(sql`
      CREATE TABLE componentImages (
        id VARCHAR(36) PRIMARY KEY NOT NULL,
        address VARCHAR(255) NOT NULL,
        component_type VARCHAR(50) NOT NULL,
        section VARCHAR(50) DEFAULT '0',
        image_data LONGTEXT NULL,
        scale DECIMAL(20,16) DEFAULT 1,
        position_x INT DEFAULT 0,
        position_y INT DEFAULT 0,
        natural_width INT NULL,
        natural_height INT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (address) REFERENCES web3Profile(address) ON DELETE CASCADE
      )
    `);

    // Create profileLikes table
    await db.execute(sql`
      CREATE TABLE profileLikes (
        id VARCHAR(36) PRIMARY KEY NOT NULL,
        liker_address VARCHAR(255) NOT NULL,
        liked_address VARCHAR(255) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (liker_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        FOREIGN KEY (liked_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        UNIQUE KEY unique_like_idx (liker_address, liked_address)
      )
    `);

    // Create profileReferrals table
    await db.execute(sql`
      CREATE TABLE profileReferrals (
        id VARCHAR(36) PRIMARY KEY NOT NULL,
        referrer_address VARCHAR(255) NOT NULL,
        referred_address VARCHAR(255) NOT NULL,
        referral_code VARCHAR(8) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (referrer_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        FOREIGN KEY (referred_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        UNIQUE KEY unique_referral_idx (referred_address)
      )
    `);

    console.log('All tables created successfully');

    // Run additional setup scripts
    console.log('Running additional setup scripts...');

    // Import and run add-system-settings
    const { default: addSystemSettings } = await import('./add-system-settings');
    const settingsAdded = await addSystemSettings();

    if (!settingsAdded) {
      throw new Error('Failed to add system settings');
    }

    // Ensure no test profiles are created
    console.log('Database reset completed with clean tables - no test profiles created');
    console.log('If you need a test profile, run: npm run create-test-profile');

    console.log('Database reset completed successfully');
  } catch (error) {
    console.error('Database reset failed:', error);
    process.exit(1);
  }
}

// Run the reset function
resetDatabase()
  .then(() => {
    console.log('Database reset completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during database reset:', error);
    process.exit(1);
  });
