{"name": "profiles-view", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@reown/appkit": "^1.7.7", "@reown/appkit-adapter-solana": "^1.7.7", "@reown/appkit-adapter-wagmi": "^1.7.7", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.74.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "framer-motion": "^12.9.7", "gl-matrix": "^3.4.3", "lucide-react": "^0.501.0", "motion": "^12.10.0", "mysql2": "^3.14.0", "next": "15.3.0", "next-themes": "^0.4.6", "pino": "^8.16.0", "pino-pretty": "^10.2.3", "postprocessing": "^6.37.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "swapy": "^1.0.5", "tailwind-merge": "^3.2.0", "three": "^0.175.0", "viem": "^2.27.3", "wagmi": "^2.14.16", "zod": "^3.24.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.175.0", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.0", "tailwindcss": "^4", "tsx": "^4.19.3", "typescript": "^5"}}