'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';
import ProfileCard from './components/ProfileCard';

import { Loader2, ArrowUpDown, X, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { getFeaturedProfileName } from '@/app/utils/systemSettings';

interface Profile {
  address: string;
  chain: string;
  name: string;
  role?: string;
  createdAt: string;
  updatedAt: string;
  likeCount?: number;
}

interface ComponentPosition {
  componentType: string;
  order: string;
  hidden: string;
}

interface ProfileData {
  address: string;
  chain: string;
  components: ComponentPosition[];
}

export default function DiscoverPage() {
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProfileData, setUserProfileData] = useState<ProfileData | null>(null);
  const [isLoadingUserProfile, setIsLoadingUserProfile] = useState(false);
  const [profileExists, setProfileExists] = useState<boolean | null>(null);
  const [featuredProfileName, setFeaturedProfileName] = useState<string>('');

  // Filter and sort state
  const [sortBy, setSortBy] = useState<'createdAt' | 'likes' | 'name'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [nameFilter, setNameFilter] = useState<string>('');

  // Load user profile data when wallet is connected
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!address) return;

      try {
        setIsLoadingUserProfile(true);

        // Check if profile exists
        try {
          const checkResponse = await fetch(`/api/admin/users/${address}`);

          if (checkResponse.status === 404) {
            // Profile doesn't exist
            setProfileExists(false);
            setIsLoadingUserProfile(false);
            return;
          } else if (!checkResponse.ok) {
            console.error('Failed to check if profile exists');
            // Assume profile doesn't exist if we can't verify
            setProfileExists(false);
            setIsLoadingUserProfile(false);
            return;
          }
        } catch (error) {
          console.error('Error checking if profile exists:', error);
          // Assume profile doesn't exist if we can't verify
          setProfileExists(false);
          setIsLoadingUserProfile(false);
          return;
        }

        // Profile exists
        setProfileExists(true);

        // Fetch the profile data
        const response = await fetch(`/api/profile/${address}`);

        if (!response.ok) {
          console.error('Failed to load user profile data');
          return;
        }

        // Get the profile data with components
        const data = await response.json();

        // Filter out hidden components
        if (data.components && Array.isArray(data.components)) {
          data.components = data.components
            .filter((c: ComponentPosition) => c.hidden !== 'Y')
            .sort((a: ComponentPosition, b: ComponentPosition) =>
              parseInt(a.order) - parseInt(b.order)
            );
        }

        setUserProfileData(data);
      } catch (error) {
        console.error('Error loading user profile data:', error);
      } finally {
        setIsLoadingUserProfile(false);
      }
    };

    if (isConnected && address) {
      loadUserProfile();
    }
  }, [address, isConnected]);

  // State for filtered profiles
  const [filteredProfiles, setFilteredProfiles] = useState<Profile[]>([]);
  const [featuredProfile, setFeaturedProfile] = useState<Profile | null>(null);

  // Load all profiles - but only if the user has a profile and we have a chain
  useEffect(() => {
    const loadProfiles = async () => {
      // Don't load profiles if we know the user doesn't have a profile
      if (profileExists === false) {
        setIsLoading(false);
        return;
      }

      // Don't load profiles if we don't have a chain ID
      if (!chainId) {
        console.log('No chain ID available, skipping profile fetch');
        setProfiles([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch profiles filtered by current chain
        const response = await fetch(`/api/profile?chain=${chainId}`);

        if (!response.ok) {
          throw new Error('Failed to load profiles');
        }

        const data = await response.json();

        // Log profiles received from API
        console.log(`Profiles received from API for chain ${chainId}:`, data.map((p: Profile) => ({ address: p.address, name: p.name, likeCount: p.likeCount })));

        // Store all profiles
        setProfiles(data);
      } catch (error) {
        console.error('Error loading profiles:', error);
        setError('An error occurred while loading profiles');
      } finally {
        setIsLoading(false);
      }
    };

    // Only load profiles if we've checked if the user has a profile
    // or if the user is not connected (in which case we show all profiles)
    // and we have a chain ID
    if ((profileExists !== null || !isConnected) && chainId) {
      loadProfiles();
    }
  }, [profileExists, isConnected, chainId]); // Re-fetch when chain changes

  // Set sort order to descending when 'likes' is selected
  useEffect(() => {
    if (sortBy === 'likes') {
      setSortOrder('desc');
    }
  }, [sortBy]);

  // Load featured profile name and set featured profile when profiles are loaded
  useEffect(() => {
    const loadFeaturedProfileName = async () => {
      try {
        const profileName = await getFeaturedProfileName();
        setFeaturedProfileName(profileName);
      } catch (error) {
        console.error('Error loading featured profile name:', error);
        // Keep the default value if there's an error
      }
    };

    loadFeaturedProfileName();
  }, []);

  // Set featured profile when profiles are loaded and featured profile name is available
  useEffect(() => {
    if (profiles.length > 0 && featuredProfileName) {
      const featured = profiles.find(p => p.name === featuredProfileName);
      setFeaturedProfile(featured || null);
    }
  }, [profiles, featuredProfileName]);

  // Apply filtering and sorting on the client side
  useEffect(() => {
    if (profiles.length === 0) return;

    // Start with all profiles
    let result = [...profiles];

    // Apply name filter if provided
    if (nameFilter) {
      result = result.filter(profile =>
        profile.name && profile.name.toLowerCase().includes(nameFilter.toLowerCase())
      );
    }

    // Apply sorting, but always put featured profile at the top
    result.sort((a, b) => {
      // First check if either profile is the featured profile
      if (featuredProfileName && a.name === featuredProfileName) return -1;
      if (featuredProfileName && b.name === featuredProfileName) return 1;

      if (sortBy === 'likes') {
        // Sort by like count
        const likesA = a.likeCount || 0;
        const likesB = b.likeCount || 0;
        // For 'likes', we always want descending order by default (most likes first)
        return sortOrder === 'asc' ? likesA - likesB : likesB - likesA;
      } else if (sortBy === 'name') {
        // Sort by name
        if (!a.name) return sortOrder === 'asc' ? -1 : 1;
        if (!b.name) return sortOrder === 'asc' ? 1 : -1;
        return sortOrder === 'asc'
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else {
        // Default sort by creation date
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      }
    });

    // Update filtered profiles
    setFilteredProfiles(result);
  }, [profiles, nameFilter, sortBy, sortOrder, featuredProfileName]);

  return (
    <main className="min-h-screen">
      <div className="container max-w-6xl mx-auto px-4 pt-2 pb-8">
        <div className="text-center mb-6">
          <div className="h-16 sm:h-20 md:h-24 flex items-center justify-center">
            <HeaderTypewriterEffect words={[
              { text: "Discover" },
              { text: "Web3" },
              { text: "Profiles", className: "text-blue-500 dark:text-blue-500" },
            ]} />
          </div>
          <p className="text-neutral-500 text-sm max-w-lg mx-auto mt-1 mb-6">
            Explore and connect with other Web3 profiles
          </p>

          {/* Featured Profile Card */}
          {featuredProfile && (
            <div className="mb-8">
              <div className="text-center mb-2">
                <div className="inline-flex items-center">
                  <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                  <span className="text-sm text-yellow-500 font-medium">Featured Profile</span>
                </div>
              </div>
              <div className="flex justify-center">
                <div className="w-[160px] relative">
                  <div className="absolute inset-0 rounded-lg z-0 border-moving-light"></div>
                  <div className="relative z-10 p-[2px]">
                    <ProfileCard
                      profile={featuredProfile}
                      currentUserAddress={address}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Filter and Sort Controls */}
          <div className="flex gap-2 justify-center items-center mb-6 max-w-3xl mx-auto px-2">
            {/* Name Filter */}
            <div className="relative flex-1 min-w-0 max-w-[50%]">
              <Input
                type="text"
                placeholder="Filter by name"
                value={nameFilter}
                onChange={(e) => setNameFilter(e.target.value)}
                className="pr-8 h-9 text-xs"
              />
              {nameFilter && (
                <button
                  onClick={() => setNameFilter('')}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-200"
                >
                  <X size={12} />
                </button>
              )}
            </div>

            {/* Sort By Dropdown */}
            <div className="flex-1 min-w-0 max-w-[40%]">
              <Select
                value={sortBy}
                onValueChange={(value) => setSortBy(value as 'createdAt' | 'likes' | 'name')}
              >
                <SelectTrigger className="w-full h-9 text-xs px-2">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">Newly Created</SelectItem>
                  <SelectItem value="likes">Most Liked</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort Order Toggle */}
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              title={sortOrder === 'asc' ? 'Ascending' : 'Descending'}
              className="h-9 w-9 flex-shrink-0"
            >
              <ArrowUpDown size={12} className={sortOrder === 'asc' ? 'rotate-0' : 'rotate-180'} />
            </Button>
          </div>
        </div>

        {/* First check if user is connected and has no profile */}
          {isConnected && address && !isLoadingUserProfile && profileExists === false ? (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 rounded-lg p-6 text-center mb-8">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">Profile Not Found</h3>
              <p className="text-yellow-600 dark:text-yellow-300 mb-4">
                You don't have a profile yet. Go to the Create Content page to create your profile.
              </p>
              <button
                onClick={() => window.location.href = '/createpage'}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
              >
                Go to Create Content
              </button>
            </div>
          ) : isConnected && address && !isLoadingUserProfile && userProfileData && userProfileData.components.length === 0 ? (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 rounded-lg p-6 text-center mb-8">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">No Components Found</h3>
              <p className="text-yellow-600 dark:text-yellow-300 mb-4">
                Your profile doesn't have any components yet. Go to the Create Content page to add components.
              </p>
              <button
                onClick={() => window.location.href = '/createpage'}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
              >
                Go to Create Content
              </button>
            </div>
          ) : (
            /* Only show profiles section if user has a profile or is not connected */
            (profileExists !== false || !isConnected) && (
              <>
                {isLoading ? (
                  <div className="flex justify-center items-center min-h-[400px]">
                    <div className="flex flex-col items-center gap-4">
                      <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
                      <p className="text-neutral-400">Loading profiles...</p>
                    </div>
                  </div>
                ) : error ? (
                  <div className="flex justify-center items-center min-h-[400px]">
                    <div className="bg-red-900/20 border border-red-900/50 rounded-lg p-6 max-w-md">
                      <h3 className="text-red-400 font-medium mb-2">Error Loading Profiles</h3>
                      <p className="text-neutral-300 mb-4">{error}</p>
                      <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors"
                      >
                        Retry
                      </button>
                    </div>
                  </div>
                ) : filteredProfiles && filteredProfiles.length > 0 ? (
                  <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-7 xl:grid-cols-8 gap-2 sm:gap-3 md:gap-4">
                    {filteredProfiles.map((profile) => (
                      <ProfileCard
                        key={profile.address}
                        profile={profile}
                        currentUserAddress={address}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center min-h-[200px]">
                    <div className="bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 max-w-md text-center">
                      <h3 className="text-yellow-400 font-medium mb-2">No Profiles Found</h3>
                      {nameFilter ? (
                        <p className="text-neutral-300">
                          No profiles match the filter "{nameFilter}". Try a different search term.
                        </p>
                      ) : (
                        <p className="text-neutral-300">There are no profiles available at the moment.</p>
                      )}
                    </div>
                  </div>
                )}
              </>
            )
          )}
      </div>
    </main>
  );
}
