"use client";

import { useEffect, useState, useRef, ReactNode } from 'react'
import { motion, HTMLMotionProps } from 'framer-motion'

const styles = {
    wrapper: {
        display: 'inline-block',
        whiteSpace: 'pre-wrap',
    },
    srOnly: {
        position: 'absolute' as 'absolute',
        width: '1px',
        height: '1px',
        padding: 0,
        margin: '-1px',
        overflow: 'hidden',
        clip: 'rect(0,0,0,0)',
        border: 0,
    },
}

interface DecryptedTextProps extends HTMLMotionProps<'span'> {
    text: string
    speed?: number
    maxIterations?: number
    sequential?: boolean
    revealDirection?: 'start' | 'end' | 'center'
    useOriginalCharsOnly?: boolean
    characters?: string
    className?: string
    parentClassName?: string
    encryptedClassName?: string
    animateOn?: 'view' | 'hover'
    style?: React.CSSProperties
}

export default function DecryptedText({
                                          text,
                                          speed = 50,
                                          maxIterations = 10,
                                          sequential = false,
                                          revealDirection = 'start',
                                          useOriginalCharsOnly = false,
                                          characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*()_+',
                                          className = '',
                                          parentClassName = '',
                                          encryptedClassName = '',
                                          animateOn = 'hover',
                                          style,
                                          ...props
                                      }: DecryptedTextProps) {
    const [displayText, setDisplayText] = useState<string>(text)
    const [isHovering, setIsHovering] = useState<boolean>(false)
    const [isScrambling, setIsScrambling] = useState<boolean>(false)
    const [revealedIndices, setRevealedIndices] = useState<Set<number>>(new Set())
    const [hasAnimated, setHasAnimated] = useState<boolean>(false)
    const containerRef = useRef<HTMLSpanElement>(null)

    const getRandomChar = (originalChar: string): string => {
        if (originalChar === ' ') return ' '
        if (useOriginalCharsOnly) {
            const uniqueChars = Array.from(new Set(text.split(''))).filter(char => char !== ' ')
            return uniqueChars[Math.floor(Math.random() * uniqueChars.length)] || originalChar
        }
        return characters[Math.floor(Math.random() * characters.length)]
    }

    const scrambleText = (): void => {
        if (isScrambling) return

        setIsScrambling(true)
        setRevealedIndices(new Set())

        const textArray = text.split('')
        const iterations = Array(textArray.length).fill(0)
        let completedChars = 0

        const interval = setInterval(() => {
            setDisplayText(
                textArray
                    .map((char, index) => {
                        if (char === ' ') return ' '

                        if (iterations[index] < maxIterations) {
                            iterations[index]++
                            return getRandomChar(char)
                        }

                        if (!revealedIndices.has(index)) {
                            setRevealedIndices(prev => new Set(prev).add(index))
                            completedChars++
                        }

                        return char
                    })
                    .join('')
            )

            if (completedChars >= textArray.filter(char => char !== ' ').length) {
                clearInterval(interval)
                setIsScrambling(false)
                setHasAnimated(true)
            }
        }, speed)
    }

    useEffect(() => {
        if (animateOn === 'view' && !hasAnimated) {
            const observer = new IntersectionObserver(
                ([entry]) => {
                    if (entry.isIntersecting) {
                        scrambleText()
                        observer.disconnect()
                    }
                },
                { threshold: 0.1 }
            )

            if (containerRef.current) {
                observer.observe(containerRef.current)
            }

            return () => observer.disconnect()
        }
    }, [animateOn, hasAnimated])

    const handleMouseEnter = (): void => {
        if (animateOn === 'hover') {
            setIsHovering(true)
            scrambleText()
        }
    }

    const handleMouseLeave = (): void => {
        if (animateOn === 'hover') {
            setIsHovering(false)
        }
    }

    return (
        <span className={parentClassName}>
            <span style={styles.srOnly}>{text}</span>
            <motion.span
                ref={containerRef}
                style={{ ...styles.wrapper, ...style }}
                className={`${className} ${isScrambling ? encryptedClassName : ''}`}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                aria-hidden="true"
                {...props}
            >
                {displayText}
            </motion.span>
        </span>
    )
}
