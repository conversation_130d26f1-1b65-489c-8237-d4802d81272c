import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    console.log(`[hero API] Fetching hero data for address: ${address}`);

    // Get hero component data
    const heroComponent = await db
      .select()
      .from(componentPositions)
      .where(
        and(
          eq(componentPositions.address, address),
          eq(componentPositions.componentType, 'hero')
        )
      );

    if (heroComponent.length === 0) {
      console.log(`[hero API] No hero component found for address: ${address}`);
      return Response.json(
        { error: 'Hero component not found' },
        { status: 404 }
      );
    }

    const component = heroComponent[0];
    const details = component.details as any || {};

    console.log(`[hero API] Found hero component:`, details);

    // Return hero content
    const responseData = {
      heroContent: details.heroContent || [],
      backgroundColor: details.backgroundColor || 'transparent',
      fontColor: details.fontColor || '#ffffff'
    };

    console.log(`[hero API] Returning hero data for address: ${address}`);
    return Response.json(responseData);

  } catch (error: any) {
    console.error('[hero API] Error fetching hero data:', error);
    
    return Response.json(
      { error: 'Failed to fetch hero data' },
      { status: 500 }
    );
  }
}
