"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-rpc-engine";
exports.ids = ["vendor-chunks/json-rpc-engine"];
exports.modules = {

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js":
/*!************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/JsonRpcEngine.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.JsonRpcEngine = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js\"));\nconst eth_rpc_errors_1 = __webpack_require__(/*! eth-rpc-errors */ \"(ssr)/./node_modules/eth-rpc-errors/dist/index.js\");\n/**\n * A JSON-RPC request and response processor.\n * Give it a stack of middleware, pass it requests, and get back responses.\n */\nclass JsonRpcEngine extends safe_event_emitter_1.default {\n    constructor() {\n        super();\n        this._middleware = [];\n    }\n    /**\n     * Add a middleware function to the engine's middleware stack.\n     *\n     * @param middleware - The middleware function to add.\n     */\n    push(middleware) {\n        this._middleware.push(middleware);\n    }\n    handle(req, cb) {\n        if (cb && typeof cb !== 'function') {\n            throw new Error('\"callback\" must be a function if provided.');\n        }\n        if (Array.isArray(req)) {\n            if (cb) {\n                return this._handleBatch(req, cb);\n            }\n            return this._handleBatch(req);\n        }\n        if (cb) {\n            return this._handle(req, cb);\n        }\n        return this._promiseHandle(req);\n    }\n    /**\n     * Returns this engine as a middleware function that can be pushed to other\n     * engines.\n     *\n     * @returns This engine as a middleware function.\n     */\n    asMiddleware() {\n        return async (req, res, next, end) => {\n            try {\n                const [middlewareError, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n                if (isComplete) {\n                    await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    return end(middlewareError);\n                }\n                return next(async (handlerCallback) => {\n                    try {\n                        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    }\n                    catch (error) {\n                        return handlerCallback(error);\n                    }\n                    return handlerCallback();\n                });\n            }\n            catch (error) {\n                return end(error);\n            }\n        };\n    }\n    async _handleBatch(reqs, cb) {\n        // The order here is important\n        try {\n            // 2. Wait for all requests to finish, or throw on some kind of fatal\n            // error\n            const responses = await Promise.all(\n            // 1. Begin executing each request in the order received\n            reqs.map(this._promiseHandle.bind(this)));\n            // 3. Return batch response\n            if (cb) {\n                return cb(null, responses);\n            }\n            return responses;\n        }\n        catch (error) {\n            if (cb) {\n                return cb(error);\n            }\n            throw error;\n        }\n    }\n    /**\n     * A promise-wrapped _handle.\n     */\n    _promiseHandle(req) {\n        return new Promise((resolve) => {\n            this._handle(req, (_err, res) => {\n                // There will always be a response, and it will always have any error\n                // that is caught and propagated.\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Ensures that the request object is valid, processes it, and passes any\n     * error and the response object to the given callback.\n     *\n     * Does not reject.\n     */\n    async _handle(callerReq, cb) {\n        if (!callerReq ||\n            Array.isArray(callerReq) ||\n            typeof callerReq !== 'object') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Requests must be plain objects. Received: ${typeof callerReq}`, { request: callerReq });\n            return cb(error, { id: undefined, jsonrpc: '2.0', error });\n        }\n        if (typeof callerReq.method !== 'string') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Must specify a string method. Received: ${typeof callerReq.method}`, { request: callerReq });\n            return cb(error, { id: callerReq.id, jsonrpc: '2.0', error });\n        }\n        const req = Object.assign({}, callerReq);\n        const res = {\n            id: req.id,\n            jsonrpc: req.jsonrpc,\n        };\n        let error = null;\n        try {\n            await this._processRequest(req, res);\n        }\n        catch (_error) {\n            // A request handler error, a re-thrown middleware error, or something\n            // unexpected.\n            error = _error;\n        }\n        if (error) {\n            // Ensure no result is present on an errored response\n            delete res.result;\n            if (!res.error) {\n                res.error = eth_rpc_errors_1.serializeError(error);\n            }\n        }\n        return cb(error, res);\n    }\n    /**\n     * For the given request and response, runs all middleware and their return\n     * handlers, if any, and ensures that internal request processing semantics\n     * are satisfied.\n     */\n    async _processRequest(req, res) {\n        const [error, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n        // Throw if \"end\" was not called, or if the response has neither a result\n        // nor an error.\n        JsonRpcEngine._checkForCompletion(req, res, isComplete);\n        // The return handlers should run even if an error was encountered during\n        // middleware processing.\n        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n        // Now we re-throw the middleware processing error, if any, to catch it\n        // further up the call chain.\n        if (error) {\n            throw error;\n        }\n    }\n    /**\n     * Serially executes the given stack of middleware.\n     *\n     * @returns An array of any error encountered during middleware execution,\n     * a boolean indicating whether the request was completed, and an array of\n     * middleware-defined return handlers.\n     */\n    static async _runAllMiddleware(req, res, middlewareStack) {\n        const returnHandlers = [];\n        let error = null;\n        let isComplete = false;\n        // Go down stack of middleware, call and collect optional returnHandlers\n        for (const middleware of middlewareStack) {\n            [error, isComplete] = await JsonRpcEngine._runMiddleware(req, res, middleware, returnHandlers);\n            if (isComplete) {\n                break;\n            }\n        }\n        return [error, isComplete, returnHandlers.reverse()];\n    }\n    /**\n     * Runs an individual middleware.\n     *\n     * @returns An array of any error encountered during middleware exection,\n     * and a boolean indicating whether the request should end.\n     */\n    static _runMiddleware(req, res, middleware, returnHandlers) {\n        return new Promise((resolve) => {\n            const end = (err) => {\n                const error = err || res.error;\n                if (error) {\n                    res.error = eth_rpc_errors_1.serializeError(error);\n                }\n                // True indicates that the request should end\n                resolve([error, true]);\n            };\n            const next = (returnHandler) => {\n                if (res.error) {\n                    end(res.error);\n                }\n                else {\n                    if (returnHandler) {\n                        if (typeof returnHandler !== 'function') {\n                            end(new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: \"next\" return handlers must be functions. ` +\n                                `Received \"${typeof returnHandler}\" for request:\\n${jsonify(req)}`, { request: req }));\n                        }\n                        returnHandlers.push(returnHandler);\n                    }\n                    // False indicates that the request should not end\n                    resolve([null, false]);\n                }\n            };\n            try {\n                middleware(req, res, next, end);\n            }\n            catch (error) {\n                end(error);\n            }\n        });\n    }\n    /**\n     * Serially executes array of return handlers. The request and response are\n     * assumed to be in their scope.\n     */\n    static async _runReturnHandlers(handlers) {\n        for (const handler of handlers) {\n            await new Promise((resolve, reject) => {\n                handler((err) => (err ? reject(err) : resolve()));\n            });\n        }\n    }\n    /**\n     * Throws an error if the response has neither a result nor an error, or if\n     * the \"isComplete\" flag is falsy.\n     */\n    static _checkForCompletion(req, res, isComplete) {\n        if (!('result' in res) && !('error' in res)) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Response has no error or result for request:\\n${jsonify(req)}`, { request: req });\n        }\n        if (!isComplete) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Nothing ended request:\\n${jsonify(req)}`, { request: req });\n        }\n    }\n}\nexports.JsonRpcEngine = JsonRpcEngine;\nfunction jsonify(request) {\n    return JSON.stringify(request, null, 2);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js":
/*!********************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createAsyncMiddleware = void 0;\n/**\n * JsonRpcEngine only accepts callback-based middleware directly.\n * createAsyncMiddleware exists to enable consumers to pass in async middleware\n * functions.\n *\n * Async middleware have no \"end\" function. Instead, they \"end\" if they return\n * without calling \"next\". Rather than passing in explicit return handlers,\n * async middleware can simply await \"next\", and perform operations on the\n * response object when execution resumes.\n *\n * To accomplish this, createAsyncMiddleware passes the async middleware a\n * wrapped \"next\" function. That function calls the internal JsonRpcEngine\n * \"next\" function with a return handler that resolves a promise when called.\n *\n * The return handler will always be called. Its resolution of the promise\n * enables the control flow described above.\n */\nfunction createAsyncMiddleware(asyncMiddleware) {\n    return async (req, res, next, end) => {\n        // nextPromise is the key to the implementation\n        // it is resolved by the return handler passed to the\n        // \"next\" function\n        let resolveNextPromise;\n        const nextPromise = new Promise((resolve) => {\n            resolveNextPromise = resolve;\n        });\n        let returnHandlerCallback = null;\n        let nextWasCalled = false;\n        // This will be called by the consumer's async middleware.\n        const asyncNext = async () => {\n            nextWasCalled = true;\n            // We pass a return handler to next(). When it is called by the engine,\n            // the consumer's async middleware will resume executing.\n            // eslint-disable-next-line node/callback-return\n            next((runReturnHandlersCallback) => {\n                // This callback comes from JsonRpcEngine._runReturnHandlers\n                returnHandlerCallback = runReturnHandlersCallback;\n                resolveNextPromise();\n            });\n            await nextPromise;\n        };\n        try {\n            await asyncMiddleware(req, res, asyncNext);\n            if (nextWasCalled) {\n                await nextPromise; // we must wait until the return handler is called\n                returnHandlerCallback(null);\n            }\n            else {\n                end(null);\n            }\n        }\n        catch (error) {\n            if (returnHandlerCallback) {\n                returnHandlerCallback(error);\n            }\n            else {\n                end(error);\n            }\n        }\n    };\n}\nexports.createAsyncMiddleware = createAsyncMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlQXN5bmNNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZUFzeW5jTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFnQkE7Ozs7Ozs7Ozs7Ozs7Ozs7R0FnQkc7QUFDSCxTQUFnQixxQkFBcUIsQ0FDbkMsZUFBNkM7SUFFN0MsT0FBTyxLQUFLLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLEVBQUU7UUFDbkMsK0NBQStDO1FBQy9DLHFEQUFxRDtRQUNyRCxrQkFBa0I7UUFDbEIsSUFBSSxrQkFBOEIsQ0FBQztRQUNuQyxNQUFNLFdBQVcsR0FBRyxJQUFJLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxFQUFFO1lBQzFDLGtCQUFrQixHQUFHLE9BQU8sQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILElBQUkscUJBQXFCLEdBQVksSUFBSSxDQUFDO1FBQzFDLElBQUksYUFBYSxHQUFHLEtBQUssQ0FBQztRQUUxQiwwREFBMEQ7UUFDMUQsTUFBTSxTQUFTLEdBQUcsS0FBSyxJQUFJLEVBQUU7WUFDM0IsYUFBYSxHQUFHLElBQUksQ0FBQztZQUVyQix1RUFBdUU7WUFDdkUseURBQXlEO1lBQ3pELGdEQUFnRDtZQUNoRCxJQUFJLENBQUMsQ0FBQyx5QkFBeUIsRUFBRSxFQUFFO2dCQUNqQyw0REFBNEQ7Z0JBQzVELHFCQUFxQixHQUFHLHlCQUF5QixDQUFDO2dCQUNsRCxrQkFBa0IsRUFBRSxDQUFDO1lBQ3ZCLENBQUMsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxXQUFXLENBQUM7UUFDcEIsQ0FBQyxDQUFDO1FBRUYsSUFBSTtZQUNGLE1BQU0sZUFBZSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFFM0MsSUFBSSxhQUFhLEVBQUU7Z0JBQ2pCLE1BQU0sV0FBVyxDQUFDLENBQUMsa0RBQWtEO2dCQUNwRSxxQkFBK0MsQ0FBQyxJQUFJLENBQUMsQ0FBQzthQUN4RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDWDtTQUNGO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDZCxJQUFJLHFCQUFxQixFQUFFO2dCQUN4QixxQkFBK0MsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN6RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7YUFDWjtTQUNGO0lBQ0gsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQS9DRCxzREErQ0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js":
/*!***********************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createScaffoldMiddleware = void 0;\nfunction createScaffoldMiddleware(handlers) {\n    return (req, res, next, end) => {\n        const handler = handlers[req.method];\n        // if no handler, return\n        if (handler === undefined) {\n            return next();\n        }\n        // if handler is fn, call as middleware\n        if (typeof handler === 'function') {\n            return handler(req, res, next, end);\n        }\n        // if handler is some other value, use as result\n        res.result = handler;\n        return end();\n    };\n}\nexports.createScaffoldMiddleware = createScaffoldMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlU2NhZmZvbGRNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZVNjYWZmb2xkTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFJQSxTQUFnQix3QkFBd0IsQ0FBQyxRQUV4QztJQUNDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsRUFBRTtRQUM3QixNQUFNLE9BQU8sR0FBRyxRQUFRLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ3JDLHdCQUF3QjtRQUN4QixJQUFJLE9BQU8sS0FBSyxTQUFTLEVBQUU7WUFDekIsT0FBTyxJQUFJLEVBQUUsQ0FBQztTQUNmO1FBQ0QsdUNBQXVDO1FBQ3ZDLElBQUksT0FBTyxPQUFPLEtBQUssVUFBVSxFQUFFO1lBQ2pDLE9BQU8sT0FBTyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQ3JDO1FBQ0QsZ0RBQWdEO1FBQy9DLEdBQStCLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQztRQUNsRCxPQUFPLEdBQUcsRUFBRSxDQUFDO0lBQ2YsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQWpCRCw0REFpQkMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js":
/*!**********************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/getUniqueId.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getUniqueId = void 0;\n// uint32 (two's complement) max\n// more conservative than Number.MAX_SAFE_INTEGER\nconst MAX = 4294967295;\nlet idCounter = Math.floor(Math.random() * MAX);\nfunction getUniqueId() {\n    idCounter = (idCounter + 1) % MAX;\n    return idCounter;\n}\nexports.getUniqueId = getUniqueId;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ2V0VW5pcXVlSWQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvZ2V0VW5pcXVlSWQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsZ0NBQWdDO0FBQ2hDLGlEQUFpRDtBQUNqRCxNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7QUFDdkIsSUFBSSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUM7QUFFaEQsU0FBZ0IsV0FBVztJQUN6QixTQUFTLEdBQUcsQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ2xDLE9BQU8sU0FBUyxDQUFDO0FBQ25CLENBQUM7QUFIRCxrQ0FHQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvZ2V0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkIsMkNBQTJDIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXGpzb24tcnBjLWVuZ2luZVxcZGlzdFxcZ2V0VW5pcXVlSWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmdldFVuaXF1ZUlkID0gdm9pZCAwO1xuLy8gdWludDMyICh0d28ncyBjb21wbGVtZW50KSBtYXhcbi8vIG1vcmUgY29uc2VydmF0aXZlIHRoYW4gTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVJcbmNvbnN0IE1BWCA9IDQyOTQ5NjcyOTU7XG5sZXQgaWRDb3VudGVyID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogTUFYKTtcbmZ1bmN0aW9uIGdldFVuaXF1ZUlkKCkge1xuICAgIGlkQ291bnRlciA9IChpZENvdW50ZXIgKyAxKSAlIE1BWDtcbiAgICByZXR1cm4gaWRDb3VudGVyO1xufVxuZXhwb3J0cy5nZXRVbmlxdWVJZCA9IGdldFVuaXF1ZUlkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2Jhc2U2NCxleUoyWlhKemFXOXVJam96TENKbWFXeGxJam9pWjJWMFZXNXBjWFZsU1dRdWFuTWlMQ0p6YjNWeVkyVlNiMjkwSWpvaUlpd2ljMjkxY21ObGN5STZXeUl1TGk5emNtTXZaMlYwVlc1cGNYVmxTV1F1ZEhNaVhTd2libUZ0WlhNaU9sdGRMQ0p0WVhCd2FXNW5jeUk2SWpzN08wRkJRVUVzWjBOQlFXZERPMEZCUTJoRExHbEVRVUZwUkR0QlFVTnFSQ3hOUVVGTkxFZEJRVWNzUjBGQlJ5eFZRVUZWTEVOQlFVTTdRVUZEZGtJc1NVRkJTU3hUUVVGVExFZEJRVWNzU1VGQlNTeERRVUZETEV0QlFVc3NRMEZCUXl4SlFVRkpMRU5CUVVNc1RVRkJUU3hGUVVGRkxFZEJRVWNzUjBGQlJ5eERRVUZETEVOQlFVTTdRVUZGYUVRc1UwRkJaMElzVjBGQlZ6dEpRVU42UWl4VFFVRlRMRWRCUVVjc1EwRkJReXhUUVVGVExFZEJRVWNzUTBGQlF5eERRVUZETEVkQlFVY3NSMEZCUnl4RFFVRkRPMGxCUTJ4RExFOUJRVThzVTBGQlV5eERRVUZETzBGQlEyNUNMRU5CUVVNN1FVRklSQ3hyUTBGSFF5SjkiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js":
/*!****************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/idRemapMiddleware.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createIdRemapMiddleware = void 0;\nconst getUniqueId_1 = __webpack_require__(/*! ./getUniqueId */ \"(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\");\nfunction createIdRemapMiddleware() {\n    return (req, res, next, _end) => {\n        const originalId = req.id;\n        const newId = getUniqueId_1.getUniqueId();\n        req.id = newId;\n        res.id = newId;\n        next((done) => {\n            req.id = originalId;\n            res.id = originalId;\n            done();\n        });\n    };\n}\nexports.createIdRemapMiddleware = createIdRemapMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaWRSZW1hcE1pZGRsZXdhcmUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaWRSZW1hcE1pZGRsZXdhcmUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsK0NBQTRDO0FBRzVDLFNBQWdCLHVCQUF1QjtJQUNyQyxPQUFPLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLEVBQUU7UUFDOUIsTUFBTSxVQUFVLEdBQUcsR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUMxQixNQUFNLEtBQUssR0FBRyx5QkFBVyxFQUFFLENBQUM7UUFDNUIsR0FBRyxDQUFDLEVBQUUsR0FBRyxLQUFLLENBQUM7UUFDZixHQUFHLENBQUMsRUFBRSxHQUFHLEtBQUssQ0FBQztRQUNmLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ1osR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsSUFBSSxFQUFFLENBQUM7UUFDVCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQztBQUNKLENBQUM7QUFaRCwwREFZQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./idRemapMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createAsyncMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createScaffoldMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./getUniqueId */ \"(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\"), exports);\n__exportStar(__webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\"), exports);\n__exportStar(__webpack_require__(/*! ./mergeMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js\"), exports);\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsc0RBQW9DO0FBQ3BDLDBEQUF3QztBQUN4Qyw2REFBMkM7QUFDM0MsZ0RBQThCO0FBQzlCLGtEQUFnQztBQUNoQyxvREFBa0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js":
/*!**************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/mergeMiddleware.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.mergeMiddleware = void 0;\nconst JsonRpcEngine_1 = __webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\");\nfunction mergeMiddleware(middlewareStack) {\n    const engine = new JsonRpcEngine_1.JsonRpcEngine();\n    middlewareStack.forEach((middleware) => engine.push(middleware));\n    return engine.asMiddleware();\n}\nexports.mergeMiddleware = mergeMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWVyZ2VNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL21lcmdlTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSxtREFBbUU7QUFFbkUsU0FBZ0IsZUFBZSxDQUFDLGVBQXNEO0lBQ3BGLE1BQU0sTUFBTSxHQUFHLElBQUksNkJBQWEsRUFBRSxDQUFDO0lBQ25DLGVBQWUsQ0FBQyxPQUFPLENBQUMsQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztJQUNqRSxPQUFPLE1BQU0sQ0FBQyxZQUFZLEVBQUUsQ0FBQztBQUMvQixDQUFDO0FBSkQsMENBSUMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvbWVyZ2VNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2Qix3QkFBd0IsbUJBQU8sQ0FBQyxtRkFBaUI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QiwyQ0FBMkMiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcanNvbi1ycGMtZW5naW5lXFxkaXN0XFxtZXJnZU1pZGRsZXdhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLm1lcmdlTWlkZGxld2FyZSA9IHZvaWQgMDtcbmNvbnN0IEpzb25ScGNFbmdpbmVfMSA9IHJlcXVpcmUoXCIuL0pzb25ScGNFbmdpbmVcIik7XG5mdW5jdGlvbiBtZXJnZU1pZGRsZXdhcmUobWlkZGxld2FyZVN0YWNrKSB7XG4gICAgY29uc3QgZW5naW5lID0gbmV3IEpzb25ScGNFbmdpbmVfMS5Kc29uUnBjRW5naW5lKCk7XG4gICAgbWlkZGxld2FyZVN0YWNrLmZvckVhY2goKG1pZGRsZXdhcmUpID0+IGVuZ2luZS5wdXNoKG1pZGRsZXdhcmUpKTtcbiAgICByZXR1cm4gZW5naW5lLmFzTWlkZGxld2FyZSgpO1xufVxuZXhwb3J0cy5tZXJnZU1pZGRsZXdhcmUgPSBtZXJnZU1pZGRsZXdhcmU7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kYXRhOmFwcGxpY2F0aW9uL2pzb247YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0ptYVd4bElqb2liV1Z5WjJWTmFXUmtiR1YzWVhKbExtcHpJaXdpYzI5MWNtTmxVbTl2ZENJNklpSXNJbk52ZFhKalpYTWlPbHNpTGk0dmMzSmpMMjFsY21kbFRXbGtaR3hsZDJGeVpTNTBjeUpkTENKdVlXMWxjeUk2VzEwc0ltMWhjSEJwYm1keklqb2lPenM3UVVGQlFTeHRSRUZCYlVVN1FVRkZia1VzVTBGQlowSXNaVUZCWlN4RFFVRkRMR1ZCUVhORU8wbEJRM0JHTEUxQlFVMHNUVUZCVFN4SFFVRkhMRWxCUVVrc05rSkJRV0VzUlVGQlJTeERRVUZETzBsQlEyNURMR1ZCUVdVc1EwRkJReXhQUVVGUExFTkJRVU1zUTBGQlF5eFZRVUZWTEVWQlFVVXNSVUZCUlN4RFFVRkRMRTFCUVUwc1EwRkJReXhKUVVGSkxFTkJRVU1zVlVGQlZTeERRVUZETEVOQlFVTXNRMEZCUXp0SlFVTnFSU3hQUVVGUExFMUJRVTBzUTBGQlF5eFpRVUZaTEVWQlFVVXNRMEZCUXp0QlFVTXZRaXhEUVVGRE8wRkJTa1FzTUVOQlNVTWlmUT09Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nfunction safeApply(handler, context, args) {\n    try {\n        Reflect.apply(handler, context, args);\n    }\n    catch (err) {\n        // Throw error after timeout so as not to interrupt the stack\n        setTimeout(() => {\n            throw err;\n        });\n    }\n}\nfunction arrayClone(arr) {\n    const n = arr.length;\n    const copy = new Array(n);\n    for (let i = 0; i < n; i += 1) {\n        copy[i] = arr[i];\n    }\n    return copy;\n}\nclass SafeEventEmitter extends events_1.EventEmitter {\n    emit(type, ...args) {\n        let doError = type === 'error';\n        const events = this._events;\n        if (events !== undefined) {\n            doError = doError && events.error === undefined;\n        }\n        else if (!doError) {\n            return false;\n        }\n        // If there is no 'error' event listener then throw.\n        if (doError) {\n            let er;\n            if (args.length > 0) {\n                [er] = args;\n            }\n            if (er instanceof Error) {\n                // Note: The comments on the `throw` lines are intentional, they show\n                // up in Node's output if this results in an unhandled exception.\n                throw er; // Unhandled 'error' event\n            }\n            // At least give some kind of context to the user\n            const err = new Error(`Unhandled error.${er ? ` (${er.message})` : ''}`);\n            err.context = er;\n            throw err; // Unhandled 'error' event\n        }\n        const handler = events[type];\n        if (handler === undefined) {\n            return false;\n        }\n        if (typeof handler === 'function') {\n            safeApply(handler, this, args);\n        }\n        else {\n            const len = handler.length;\n            const listeners = arrayClone(handler);\n            for (let i = 0; i < len; i += 1) {\n                safeApply(listeners[i], this, args);\n            }\n        }\n        return true;\n    }\n}\nexports[\"default\"] = SafeEventEmitter;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js\n");

/***/ })

};
;