"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_telegram_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   telegramSvg: () => (/* binding */ telegramSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst telegramSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"32\" height=\"32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\">\n  <g clip-path=\"url(#a)\">\n    <path fill=\"url(#b)\" d=\"M0 0h32v32H0z\"/>\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.034 15.252c4.975-2.167 8.293-3.596 9.953-4.287 4.74-1.971 5.725-2.314 6.366-2.325.142-.002.457.033.662.*************.33.243.463.022.132.05.435.028.671-.257 2.7-1.368 9.248-1.933 12.27-.24 1.28-.71 1.708-1.167 1.75-.99.091-1.743-.655-2.703-1.284-1.502-.985-2.351-1.598-3.81-2.558-1.684-1.11-.592-1.721.368-2.718.252-.261 4.619-4.233 4.703-4.594.01-.045.02-.213-.08-.301-.1-.09-.246-.059-.353-.035-.15.034-2.55 1.62-7.198 4.758-.682.468-1.298.696-1.851.684-.61-.013-1.782-.344-2.653-.628-1.069-.347-1.918-.53-1.845-1.12.039-.308.462-.623 1.27-.944Z\" fill=\"#fff\"/>\n  </g>\n  <path d=\"M.5 16C.5 7.44 7.44.5 16 .5 24.56.5 31.5 7.44 31.5 16c0 8.56-6.94 15.5-15.5 15.5C7.44 31.5.5 24.56.5 16Z\" stroke=\"#141414\" stroke-opacity=\".05\"/>\n  <defs>\n    <linearGradient id=\"b\" x1=\"1600\" y1=\"0\" x2=\"1600\" y2=\"3176.27\" gradientUnits=\"userSpaceOnUse\">\n      <stop stop-color=\"#2AABEE\"/>\n      <stop offset=\"1\" stop-color=\"#229ED9\"/>\n    </linearGradient>\n    <clipPath id=\"a\">\n      <path d=\"M0 16C0 7.163 7.163 0 16 0s16 7.163 16 16-7.163 16-16 16S0 24.837 0 16Z\" fill=\"#fff\"/>\n    </clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=telegram.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js\n"));

/***/ })

}]);