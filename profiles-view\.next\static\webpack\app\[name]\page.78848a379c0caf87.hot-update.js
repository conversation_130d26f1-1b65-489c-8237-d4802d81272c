"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[name]/page",{

/***/ "(app-pages-browser)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit/react */ \"(app-pages-browser)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(app-pages-browser)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/profileStatus */ \"(app-pages-browser)/./lib/profileStatus.ts\");\n/* harmony import */ var _app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/components/renders/render_hero */ \"(app-pages-browser)/./app/components/renders/render_hero.tsx\");\n/* harmony import */ var _app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/renders/render_sociallinks */ \"(app-pages-browser)/./app/components/renders/render_sociallinks.tsx\");\n/* harmony import */ var _app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/renders/render_bannerpfp */ \"(app-pages-browser)/./app/components/renders/render_bannerpfp.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ClientPage(param) {\n    let { name } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statusChecked, setStatusChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = (0,_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata)();\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // First check profile status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            async function checkStatus() {\n                try {\n                    console.log('[ClientPage] Checking status for:', name);\n                    const statusResult = await (0,_lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__.checkProfileStatus)(name);\n                    console.log('[ClientPage] Status result:', statusResult);\n                    // Check if this is the user's own profile\n                    const isOwnProfile = address && statusResult.address && address.toLowerCase() === statusResult.address.toLowerCase();\n                    console.log('[ClientPage] Is own profile:', isOwnProfile);\n                    // If profile is not approved, redirect to error page\n                    // We no longer make an exception for the profile owner\n                    if (!statusResult.isApproved && statusResult.status !== 'not-found') {\n                        console.log('[ClientPage] Redirecting to error page for status:', statusResult.status);\n                        // Use the name parameter instead of address\n                        const nameParam = statusResult.name ? \"&name=\".concat(statusResult.name) : \"&name=\".concat(name);\n                        window.location.href = \"/profile-error?status=\".concat(statusResult.status).concat(nameParam);\n                        return;\n                    }\n                    setStatusChecked(true);\n                } catch (error) {\n                    console.error('[ClientPage] Error checking profile status:', error);\n                    setStatusChecked(true); // Continue anyway if status check fails\n                }\n            }\n            checkStatus();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        address\n    ]);\n    // Then fetch profile data once status is checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            if (!statusChecked) return; // Wait until status check is complete\n            async function fetchProfileData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('Fetching profile for name:', name);\n                    console.log('Current pathname:', pathname);\n                    // Fetch profile data by name\n                    // Always treat the parameter as a name, not an address\n                    const response = await fetch(\"/api/profile/\".concat(name));\n                    if (!response.ok) {\n                        // Handle errors with more specific messages\n                        const errorResponse = await response.json();\n                        if (response.status === 404) {\n                            // For 404 errors, show a user-friendly message\n                            if (errorResponse.error === 'No profiles exist in the database yet') {\n                                setError('No profiles have been created yet. Be the first to create one!');\n                            } else if (errorResponse.error === 'Profile components not properly initialized') {\n                                setError('Profile system is not properly initialized. Please contact support.');\n                            } else {\n                                setError('Profile \"'.concat(name, '\" not found'));\n                            }\n                            setLoading(false);\n                            return; // Exit early without throwing an error\n                        }\n                        // For other errors, log and throw\n                        console.error('Failed to fetch profile:', errorResponse);\n                        throw new Error(errorResponse.error || 'Failed to load profile');\n                    }\n                    const data = await response.json();\n                    setProfileData(data);\n                    // Fetch bannerpfp metadata\n                    if (data.address) {\n                        try {\n                            // Fetch bannerpfp metadata first\n                            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);\n                            if (bannerPfpMeta) {\n                                setBannerPfpMetadata(bannerPfpMeta);\n                                // Now fetch banner and profile picture metadata which will use the bannerpfp data\n                                const bannerMeta = await fetchBannerMetadata(data.address);\n                                if (bannerMeta) {\n                                    setBannerMetadata(bannerMeta);\n                                }\n                                const profilePicMeta = await fetchProfilePictureMetadata(data.address);\n                                if (profilePicMeta) {\n                                    setProfilePictureMetadata(profilePicMeta);\n                                }\n                            }\n                        } catch (metadataError) {\n                            console.error('Error fetching component metadata:', metadataError);\n                        // Continue showing the profile even if metadata fetch fails\n                        }\n                    }\n                } catch (err) {\n                    console.error('Error loading profile:', err);\n                    setError(err.message || 'An error occurred while loading the profile');\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchProfileData();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        fetchBannerMetadata,\n        fetchProfilePictureMetadata,\n        statusChecked\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen flex flex-col items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-4xl mx-auto px-4 sm:px-6 pb-8 z-10 relative\",\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center min-h-[300px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 157,\n                columnNumber: 11\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-red-400 font-medium mb-2\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this) : profileData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-0 border border-neutral-700 overflow-hidden\",\n                children: profileData.components.filter((c)=>c.hidden !== 'Y').sort((a, b)=>parseInt(a.order) - parseInt(b.order)).map((component, index)=>{\n                    // Skip old component types\n                    if (component.componentType === 'banner' || component.componentType === 'profilePicture') {\n                        return null;\n                    } else if (component.componentType === 'details') {\n                        // Skip rendering deprecated 'details' component\n                        return null;\n                    } else if (component.componentType === 'hero') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 23\n                            }, this)\n                        }, \"hero-\".concat(component.order), false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'socialLinks') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                profileData: {\n                                    address: profileData.address,\n                                    chain: profileData.chain,\n                                    name: profileData.name || '',\n                                    bio: ''\n                                },\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 23\n                            }, this)\n                        }, \"socialLinks-\".concat(component.order), false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'bannerpfp') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false,\n                                profileName: profileData.name || '',\n                                profileBio: ''\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 23\n                            }, this)\n                        }, \"bannerpfp-\".concat(component.order), false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 21\n                        }, this);\n                    }\n                    return null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 172,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-yellow-400 font-medium mb-2\",\n                        children: \"Profile Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: [\n                            'The profile \"',\n                            name,\n                            \"\\\" doesn't exist or has been removed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 237,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientPage, \"y6NMnj1DDI8C4j2dYu33SjONjvw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount,\n        _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata\n    ];\n});\n_c = ClientPage;\nvar _c;\n$RefreshReg$(_c, \"ClientPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[name]/ClientPage.tsx\n"));

/***/ })

});