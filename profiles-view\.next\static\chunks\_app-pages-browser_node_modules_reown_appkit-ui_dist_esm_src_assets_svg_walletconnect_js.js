"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnectBrownSvg: () => (/* binding */ walletConnectBrownSvg),\n/* harmony export */   walletConnectLightBrownSvg: () => (/* binding */ walletConnectLightBrownSvg),\n/* harmony export */   walletConnectSvg: () => (/* binding */ walletConnectSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst walletConnectSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 96 67\">\n  <path\n    fill=\"currentColor\"\n    d=\"M25.32 18.8a32.56 32.56 0 0 1 45.36 0l1.5 1.47c.63.62.63 1.61 0 2.22l-5.15 5.05c-.31.3-.82.3-1.14 0l-2.07-2.03a22.71 22.71 0 0 0-31.64 0l-2.22 2.18c-.31.3-.82.3-1.14 0l-5.15-5.05a1.55 1.55 0 0 1 0-2.22l1.65-1.62Zm56.02 10.44 4.59 4.5c.63.6.63 1.6 0 2.21l-20.7 20.26c-.62.61-1.63.61-2.26 0L48.28 41.83a.4.4 0 0 0-.56 0L33.03 56.21c-.63.61-1.64.61-2.27 0L10.07 35.95a1.55 1.55 0 0 1 0-2.22l4.59-4.5a1.63 1.63 0 0 1 2.27 0L31.6 43.63a.4.4 0 0 0 .57 0l14.69-14.38a1.63 1.63 0 0 1 2.26 0l14.69 14.38a.4.4 0 0 0 .57 0l14.68-14.38a1.63 1.63 0 0 1 2.27 0Z\"\n  />\n  <path\n    stroke=\"#000\"\n    stroke-opacity=\".1\"\n    d=\"M25.67 19.15a32.06 32.06 0 0 1 44.66 0l1.5 1.48c.43.42.43 1.09 0 1.5l-5.15 5.05a.31.31 0 0 1-.44 0l-2.07-2.03a23.21 23.21 0 0 0-32.34 0l-2.22 2.18a.31.31 0 0 1-.44 0l-5.15-5.05a1.05 1.05 0 0 1 0-1.5l1.65-1.63ZM81 29.6l4.6 4.5c.42.41.42 1.09 0 1.5l-20.7 20.26c-.43.43-1.14.43-1.57 0L48.63 41.47a.9.9 0 0 0-1.26 0L32.68 55.85c-.43.43-1.14.43-1.57 0L10.42 35.6a1.05 1.05 0 0 1 0-1.5l4.59-4.5a1.13 1.13 0 0 1 1.57 0l14.68 14.38a.9.9 0 0 0 1.27 0l-.35-.35.35.35L47.22 29.6a1.13 1.13 0 0 1 1.56 0l14.7 14.38a.9.9 0 0 0 1.26 0L79.42 29.6a1.13 1.13 0 0 1 1.57 0Z\"\n  />\n</svg>`;\nconst walletConnectLightBrownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_22274_4692)\">\n<path d=\"M0 6.64C0 4.17295 0 2.93942 0.525474 2.01817C0.880399 1.39592 1.39592 0.880399 2.01817 0.525474C2.93942 0 4.17295 0 6.64 0H9.36C11.8271 0 13.0606 0 13.9818 0.525474C14.6041 0.880399 15.1196 1.39592 15.4745 2.01817C16 2.93942 16 4.17295 16 6.64V9.36C16 11.8271 16 13.0606 15.4745 13.9818C15.1196 14.6041 14.6041 15.1196 13.9818 15.4745C13.0606 16 11.8271 16 9.36 16H6.64C4.17295 16 2.93942 16 2.01817 15.4745C1.39592 15.1196 0.880399 14.6041 0.525474 13.9818C0 13.0606 0 11.8271 0 9.36V6.64Z\" fill=\"#C7B994\"/>\n<path d=\"M4.49038 5.76609C6.42869 3.86833 9.5713 3.86833 11.5096 5.76609L11.7429 5.99449C11.8398 6.08938 11.8398 6.24323 11.7429 6.33811L10.9449 7.11942C10.8964 7.16686 10.8179 7.16686 10.7694 7.11942L10.4484 6.80512C9.09617 5.48119 6.90381 5.48119 5.5516 6.80512L5.20782 7.14171C5.15936 7.18915 5.08079 7.18915 5.03234 7.14171L4.23434 6.3604C4.13742 6.26552 4.13742 6.11167 4.23434 6.01678L4.49038 5.76609ZM13.1599 7.38192L13.8702 8.07729C13.9671 8.17217 13.9671 8.32602 13.8702 8.4209L10.6677 11.5564C10.5708 11.6513 10.4137 11.6513 10.3168 11.5564L8.04388 9.33105C8.01965 9.30733 7.98037 9.30733 7.95614 9.33105L5.6833 11.5564C5.58638 11.6513 5.42925 11.6513 5.33234 11.5564L2.12982 8.42087C2.0329 8.32598 2.0329 8.17213 2.12982 8.07724L2.84004 7.38188C2.93695 7.28699 3.09408 7.28699 3.191 7.38188L5.46392 9.60726C5.48815 9.63098 5.52743 9.63098 5.55166 9.60726L7.82447 7.38188C7.92138 7.28699 8.07851 7.28699 8.17543 7.38187L10.4484 9.60726C10.4726 9.63098 10.5119 9.63098 10.5361 9.60726L12.809 7.38192C12.9059 7.28703 13.063 7.28703 13.1599 7.38192Z\" fill=\"#202020\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_22274_4692\">\n<path d=\"M0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8Z\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n`;\nconst walletConnectBrownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<circle cx=\"11\" cy=\"11\" r=\"11\" transform=\"matrix(-1 0 0 1 23 1)\" fill=\"#202020\"/>\n<circle cx=\"11\" cy=\"11\" r=\"11.5\" transform=\"matrix(-1 0 0 1 23 1)\" stroke=\"#C7B994\" stroke-opacity=\"0.7\"/>\n<path d=\"M15.4523 11.0686L16.7472 9.78167C13.8205 6.87297 10.1838 6.87297 7.25708 9.78167L8.55201 11.0686C10.7779 8.85645 13.2279 8.85645 15.4538 11.0686H15.4523Z\" fill=\"#C7B994\"/>\n<path d=\"M15.0199 14.067L12 11.0656L8.98 14.067L5.96004 11.0656L4.66663 12.3511L8.98 16.6393L12 13.638L15.0199 16.6393L19.3333 12.3511L18.0399 11.0656L15.0199 14.067Z\" fill=\"#C7B994\"/>\n</svg>\n`;\n//# sourceMappingURL=walletconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js\n"));

/***/ })

}]);