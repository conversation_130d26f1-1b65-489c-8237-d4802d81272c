'use client';

import { useState, useEffect, useRef } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';
import Script from 'next/script';
import WalletValidator from '@/components/WalletValidator';
import { createProfile } from '@/app/actions/profileActions';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Save } from 'lucide-react';
import { createSwapy } from 'swapy';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';

// Import render components
// Old render components removed
import RenderHero from '@/app/components/renders/render_hero';
import RenderSocialLinks from '@/app/components/renders/render_sociallinks';
import RenderBannerPfp from '@/app/components/renders/render_bannerpfp';

interface ComponentPosition {
  componentType: string;
  order: string;
  hidden: string;
  address: string;
  chain: string;
  imageData?: string;
  scale?: string;
  positionX?: number;
  positionY?: number;
  naturalWidth?: number;
  naturalHeight?: number;
}

interface ProfileData {
  address: string;
  chain: string;
  name?: string; // Optional for backward compatibility
  profileName?: string; // New field
  bio?: string; // Optional for backward compatibility
  profileBio?: string; // New field
  socialLinks?: any;
  compPosition?: 'left' | 'center' | 'right';
  components: ComponentPosition[];
}

export default function LayoutPage() {
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const pathname = usePathname();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [components, setComponents] = useState<ComponentPosition[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Reference for the container element
  const containerRef = useRef<HTMLDivElement>(null);
  // Reference for the Swapy instance
  const swapyRef = useRef<any>(null);

  // Only attempt to load profile data when the wallet is connected
  useEffect(() => {
    const loadProfileData = async () => {
      // Only proceed if the wallet is connected and we have an address
      if (!isConnected || !address) {
        setLoading(false); // Make sure loading is false when not connected
        return;
      }

      try {
        setLoading(true);
        setError(null);
        // Create profile if it doesn't exist - require chainId
        if (!chainId) {
          setError('Please connect to a supported network');
          return;
        }
        await createProfile(address, chainId.toString());

        // Fetch the profile data
        const response = await fetch(`/api/profile/${address}`);
        // Profile API response received

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Failed to load profile data:', errorText);
          setError('Failed to load profile data');
          return;
        }

        // Get the profile data with components
        const data = await response.json();
        console.log('Profile data loaded:', data);
        console.log('Components received:', data.components ? data.components.length : 0);
        setProfileData(data);

        // Filter out hidden components and sort by order
        if (data.components && Array.isArray(data.components)) {
          console.log('Sorting and filtering components');
          const visibleComponents = data.components.filter((c: ComponentPosition) =>
            c.hidden !== 'Y' && c.componentType !== 'banner' && c.componentType !== 'profilePicture'
          );
          const sortedComponents = [...visibleComponents].sort(
            (a: ComponentPosition, b: ComponentPosition) => parseInt(a.order) - parseInt(b.order)
          );
          console.log('Visible components after sorting:', sortedComponents.length);
          setComponents(sortedComponents);
        } else {
          console.warn('No components array found in profile data or it is not an array');
        }
      } catch (err) {
        console.error('Error loading profile:', err);
        setError('An error occurred while loading the profile');
      } finally {
        setLoading(false);
      }
    };

    // Reset state when wallet is disconnected
    if (!isConnected) {
      setProfileData(null);
      setComponents([]);
      setError(null);
      setLoading(false);
    } else {
      // Only load profile data if connected
      loadProfileData();
    }
  }, [address, isConnected, pathname, chainId]);

  // Auto-initialize Swapy when components are loaded
  useEffect(() => {
    // Only try to initialize if we have components and a container
    if (containerRef.current && components.length > 0) {
      // Initialize Swapy

      // Wait a bit for the DOM to be fully rendered
      const timer = setTimeout(() => {
        // Simulate clicking the initialize button
        const initializeSwapyAutomatically = () => {
          if (containerRef.current) {
            try {
              // Clean up previous instance
              if (swapyRef.current) {
                try {
                  swapyRef.current.destroy();
                } catch (e) {
                  console.error('Error destroying Swapy:', e);
                }
              }

              // Use imported createSwapy
              console.log('Auto-initializing with createSwapy');
              const swapyInstance = createSwapy(containerRef.current, {
                animation: 'dynamic',
                autoScrollOnDrag: true
              });
              swapyRef.current = swapyInstance;

              swapyInstance.onSwap(() => {
                console.log('Swap detected');
                handleComponentMove();
              });

              console.log('Drag and drop auto-initialized!');
            } catch (error) {
              console.error('Failed to auto-initialize Swapy:', error);
              // Don't show error toast for auto-initialization
            }
          }
        };

        // Try to initialize
        initializeSwapyAutomatically();
      }, 1000); // Wait 1 second for DOM to be ready

      return () => clearTimeout(timer);
    }
  }, [components]); // Re-run when components change

  // Handle component move
  const handleComponentMove = () => {
    if (!swapyRef.current || !containerRef.current) return;

    try {
      // Get the current slot-item map from Swapy
      const slotItemMapResult = swapyRef.current.slotItemMap();
      console.log('Current slot-item map:', slotItemMapResult);

      // Use the asObject format for easier access
      const slotItemMap = slotItemMapResult.asObject;

      // Get all slots in the order they appear in the DOM
      const slots = containerRef.current.querySelectorAll('[data-swapy-slot]');

      // Create a new array of components with updated order
      const updatedComponents = [...components];

      // Map of slot indices to component types
      const slotComponentMap: Record<string, string> = {};

      // First, build a map of which component is in which slot
      Object.entries(slotItemMap).forEach(([slotName, itemName]) => {
        const componentType = (itemName as string).replace('item-', '');
        slotComponentMap[slotName] = componentType;
      });

      // Now update the order based on the DOM order
      Array.from(slots).forEach((slot, index) => {
        const slotName = slot.getAttribute('data-swapy-slot');
        if (!slotName) return;

        const componentType = slotComponentMap[slotName];
        if (!componentType) return;

        // Find the component in our array
        const componentIndex = updatedComponents.findIndex(c => c.componentType === componentType);
        if (componentIndex >= 0) {
          // Update its order
          updatedComponents[componentIndex] = {
            ...updatedComponents[componentIndex],
            order: (index + 1).toString()
          };
        }
      });

      // Sort the components by their new order
      updatedComponents.sort((a, b) => parseInt(a.order) - parseInt(b.order));

      // Update state
      setComponents(updatedComponents);
      setHasUnsavedChanges(true);

      console.log('Components reordered:', updatedComponents);
    } catch (error) {
      console.error('Error handling component move:', error);
      toast.error('Failed to update component order');
    }
  };

  // Save changes to database
  const handleSaveChanges = async () => {
    if (!address || !profileData) return;

    try {
      setIsSaving(true);

      // Prepare data for API
      const updateData = {
        address,
        components: components.map(component => ({
          componentType: component.componentType,
          order: component.order
        }))
      };

      // Send update to API
      const response = await fetch('/api/components/update-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to save changes');
      }

      setHasUnsavedChanges(false);
      toast.success('Layout saved successfully');
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error('Failed to save changes');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <main className="relative min-h-screen flex flex-col items-center">
      {/* Load Swapy script */}
      {/* No script needed - we're using the imported createSwapy directly */}
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 pt-2 pb-8 z-10 relative">
        <div className="text-center mb-2">
          <div className="h-16 sm:h-20 md:h-24 flex items-center justify-center">
            <HeaderTypewriterEffect words={[
              { text: "Arrange" },
              { text: "Your" },
              { text: "Profile" },
              { text: "Layout", className: "text-blue-500 dark:text-blue-500" },
            ]} />
          </div>
          <p className="text-neutral-500 text-sm max-w-lg mx-auto mt-1">
            Drag and drop components to create your perfect layout. Click Save when you're done.
          </p>
        </div>
        <WalletValidator>
          {/* The WalletValidator now has a standardized fallback message */}
          {/* Only show content when wallet is connected */}
          {loading ? (
            <div className="flex justify-center items-center p-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : error ? (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-800 p-6 text-center">
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">{error}</h3>
              <p className="text-red-600 dark:text-red-300 mb-4">
                There was an error loading your profile. Please try again or create your profile in the Create Content page.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4 mt-4">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={() => window.location.href = '/createpage'}
                  className="px-4 py-2 bg-red-600 text-white hover:bg-red-700 transition-colors"
                >
                  Go to Create Content
                </button>
              </div>
            </div>
          ) : profileData && components.length > 0 ? (
            <div className="space-y-0">
              {/* Save button */}
              <div className="flex justify-center mb-2">
                <Button
                  size="lg"
                  variant={hasUnsavedChanges ? "default" : "outline"}
                  onClick={handleSaveChanges}
                  className="w-full max-w-md flex items-center justify-center gap-2"
                  disabled={!hasUnsavedChanges || isSaving}
                >
                  <Save className="h-5 w-5" />
                  <span>{isSaving ? "Saving..." : hasUnsavedChanges ? "Save Changes" : "No Changes to Save"}</span>
                </Button>
              </div>

              {/* Draggable components container */}
              <div
                ref={containerRef}
                className="space-y-0 relative min-h-[500px] border border-neutral-700 overflow-hidden"
              >
                {components.map((component, index) => (
                  <div
                    key={`slot-${component.componentType}`}
                    data-swapy-slot={`slot-${index}`}
                    className="relative border-b border-neutral-700 last:border-b-0"
                  >
                    <div
                      data-swapy-item={`item-${component.componentType}`}
                      className="relative cursor-move w-full h-full"
                    >
                      {/* No drag handle needed - entire component is draggable */}

                      {/* Render the component based on its type */}
                      {/* Old component types (banner, profilePicture) are filtered out in the data loading phase */}

                      {component.componentType === 'bannerpfp' && (
                        <RenderBannerPfp
                          address={address as string}
                          componentData={component}
                          showPositionLabel={true}
                          profileName={profileData.profileName || profileData.name || address?.substring(0, 8) || 'Web3 User'}
                          profileBio={profileData.profileBio || profileData.bio || ''}
                        />
                      )}

                      {component.componentType === 'socialLinks' && (
                        <RenderSocialLinks
                          profileData={{
                            address: profileData.address,
                            chain: profileData.chain,
                            name: profileData.profileName || profileData.name || '',
                            bio: profileData.profileBio || profileData.bio || ''
                          }}
                          componentData={component}
                          showPositionLabel={true}
                        />
                      )}

                      {component.componentType === 'hero' && (
                        <RenderHero
                          address={address as string}
                          componentData={component}
                          showPositionLabel={true}
                        />
                      )}


                    </div>
                  </div>
                ))}
              </div>

              {/* Save button at bottom for convenience */}
              {hasUnsavedChanges && (
                <div className="flex justify-center mt-2">
                  <Button
                    size="lg"
                    variant="default"
                    onClick={handleSaveChanges}
                    className="w-full max-w-md flex items-center justify-center gap-2"
                    disabled={isSaving}
                  >
                    <Save className="h-5 w-5" />
                    <span>{isSaving ? "Saving..." : "Save Changes"}</span>
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 rounded-lg p-6 text-center">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">No Components Found</h3>
              <p className="text-yellow-600 dark:text-yellow-300 mb-4">
                Your profile doesn't have any components yet. Go to the Create Content page to add components.
              </p>
              <button
                onClick={() => window.location.href = '/createpage'}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
              >
                Go to Create Content
              </button>
            </div>
          )}
        </WalletValidator>
      </div>
    </main>
  );
}
