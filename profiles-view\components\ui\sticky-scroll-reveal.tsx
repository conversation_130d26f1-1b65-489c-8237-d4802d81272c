"use client";
import React, { useEffect, useRef, useState } from "react";
import { useMotionValueEvent, useScroll } from "framer-motion";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export const StickyScroll = ({
                                 content,
                                 contentClassName,
                                 fontColor,
                             }: {
    content: {
        title: string;
        description: string | React.ReactNode;
        content?: React.ReactNode | any;
        customBackground?: string | null;
        customScrollContainerClass?: string;
    }[];
    contentClassName?: string;
    fontColor?: string | null;
}) => {
    const [activeCard, setActiveCard] = React.useState(0);
    const ref = useRef<any>(null);
    const { scrollYProgress } = useScroll({
        container: ref,
        offset: ["start start", "end end"],
    });
    const cardLength = content.length;

    useMotionValueEvent(scrollYProgress, "change", (latest) => {
        const cardsBreakpoints = content.map((_, index) => index / cardLength);
        const closestBreakpointIndex = cardsBreakpoints.reduce(
            (acc, breakpoint, index) => {
                const distance = Math.abs(latest - breakpoint);
                if (distance < Math.abs(latest - cardsBreakpoints[acc])) {
                    return index;
                }
                return acc;
            },
            0
        );
        setActiveCard(closestBreakpointIndex);
    });

    const backgroundGradient = `linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))`;
    const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

    // Check if there's only one section
    const isSingleSection = content.length === 1;

    // For single section, use a completely different layout
    if (isSingleSection) {
        return (
            <motion.div
                animate={{}}
                className="relative flex flex-row h-[20rem] sm:h-[26rem] md:h-[28rem] justify-center space-x-0 sm:space-x-2 md:space-x-4 py-0 sm:py-6 md:py-10 px-0 sm:px-1 md:px-3 scroll-smooth w-full m-0 xs:mr-2 overflow-x-hidden overflow-y-scroll custom-scrollbar"
                style={{}}
                ref={ref}
            >
                <div className="div relative flex items-start px-2 sm:px-3 md:px-4 pr-4 sm:pr-4 md:pr-5 w-full m-0 overflow-visible text-container">
                    <div className="w-full max-w-[12rem] sm:max-w-[16rem] md:max-w-xs overflow-visible">
                        <div className="-mt-0 sm:-mt-12"></div>
                        <motion.h2
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="text-lg sm:text-xl md:text-2xl font-bold"
                            style={{ color: fontColor || '#ffffff' }}
                        >
                            {content[0].title}
                        </motion.h2>
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="text-xs sm:text-sm md:text-base mt-3 sm:mt-4 md:mt-6 max-w-full sm:max-w-[12rem] md:max-w-xs line-clamp-4 sm:line-clamp-5 md:line-clamp-6"
                            style={{ color: fontColor || '#ffffff' }}
                        >
                            {content[0].description}
                        </motion.div>
                    </div>
                </div>
                <div className="flex items-center justify-center sticky top-0 h-full overflow-visible pr-0 container-wrapper">
                    <div
                        style={{
                            background: content[0]?.customBackground || backgroundGradient
                        }}
                        className={cn(
                            "h-52 w-52 sm:h-64 sm:w-64 md:w-72 md:h-72 overflow-hidden bg-transparent xs:mr-2 content-container",
                            contentClassName,
                        )}
                    >
                        {content[0] && content[0].content ? content[0].content : null}
                    </div>
                </div>
            </motion.div>
        );
    }

    return (
        <motion.div
            animate={{}}
            className="relative flex flex-row h-[20rem] sm:h-[26rem] md:h-[28rem] justify-center space-x-0 sm:space-x-2 md:space-x-4 py-0 sm:py-6 md:py-10 px-0 sm:px-1 md:px-3 scroll-smooth w-full m-0 xs:mr-2 overflow-x-hidden overflow-y-scroll custom-scrollbar"
            style={{}}
            ref={ref}
        >
            <div className="div relative flex items-start px-2 sm:px-3 md:px-4 pr-4 sm:pr-4 md:pr-5 w-full m-0 overflow-visible text-container">
                <div className="w-full max-w-[12rem] sm:max-w-[16rem] md:max-w-xs overflow-visible">
                    <div className="-mt-0 sm:-mt-12"></div>

                    {content.map((item, index) => (
                        <div
                            key={item.title + index}
                            className="my-24 sm:my-24 md:my-32"
                            ref={(el: HTMLDivElement | null) => {
                                if (sectionRefs.current) {
                                    sectionRefs.current[index] = el;
                                }
                            }}
                        >
                            <motion.h2
                                initial={{
                                    opacity: 0,
                                }}
                                animate={{
                                    opacity: activeCard === index ? 1 : 0.3,
                                }}
                                className="text-lg sm:text-xl md:text-2xl font-bold"
                                style={{ color: fontColor || '#ffffff' }}
                            >
                                {item.title}
                            </motion.h2>
                            <motion.div
                                initial={{
                                    opacity: 0,
                                }}
                                animate={{
                                    opacity: activeCard === index ? 1 : 0.3,
                                }}
                                className="text-xs sm:text-sm md:text-base mt-3 sm:mt-4 md:mt-6 max-w-full sm:max-w-[12rem] md:max-w-xs line-clamp-4 sm:line-clamp-5 md:line-clamp-6"
                                style={{ color: fontColor || '#ffffff' }}
                            >
                                {item.description}
                            </motion.div>
                        </div>
                    ))}

                    <div className="h-[2rem] sm:h-[8rem]" />
                </div>
            </div>
            <div className="flex items-center justify-center sticky top-0 h-full overflow-visible pr-0 container-wrapper">
                <div
                    style={{
                        background: content[activeCard]?.customBackground || backgroundGradient
                    }}
                    className={cn(
                        "h-52 w-52 sm:h-64 sm:w-64 md:w-72 md:h-72 overflow-hidden bg-transparent xs:mr-2 content-container",
                        contentClassName,
                    )}
                >
                    {content[activeCard] && content[activeCard].content ? content[activeCard].content : null}
                </div>
            </div>
        </motion.div>
    );
};
