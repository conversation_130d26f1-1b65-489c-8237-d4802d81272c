import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq, and, or, inArray } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const addresses = searchParams.get('addresses');
    const chainFilter = searchParams.get('chain');

    if (!addresses) {
      return Response.json(
        { error: 'Addresses parameter is required' },
        { status: 400 }
      );
    }

    // Parse the comma-separated list of addresses
    const addressList = addresses.split(',');

    // Build query with optional chain filtering
    let whereCondition;

    // Add chain filter if provided
    if (chainFilter) {
      whereCondition = and(
        inArray(web3Profile.address, addressList),
        eq(web3Profile.chain, chainFilter)
      );
    } else {
      whereCondition = inArray(web3Profile.address, addressList);
    }

    // Get status for addresses (optionally filtered by chain)
    const profiles = await db
      .select({
        address: web3Profile.address,
        status: web3Profile.status,
        chain: web3Profile.chain,
      })
      .from(web3Profile)
      .where(whereCondition);

    // Create a map of address to status
    const statusMap: Record<string, string> = {};

    // Initialize all requested addresses as 'not-found'
    addressList.forEach(addr => {
      statusMap[addr.toLowerCase()] = 'not-found';
    });

    // Update with actual statuses from the database
    // Only include profiles that match the chain filter (if provided)
    profiles.forEach(profile => {
      statusMap[profile.address.toLowerCase()] = profile.status;
    });

    return Response.json(statusMap);
  } catch (error) {
    console.error('Failed to fetch profile statuses:', error);
    return Response.json(
      { error: 'Failed to fetch profile statuses' },
      { status: 500 }
    );
  }
}
