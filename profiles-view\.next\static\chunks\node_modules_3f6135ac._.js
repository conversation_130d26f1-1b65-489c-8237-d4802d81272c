(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/9de0f_@noble_08514a45._.js",
  "static/chunks/9de0f_@noble_curves_esm_secp256k1_bb652c78.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/embedded-wallet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_94d91aaf._.js",
  "static/chunks/node_modules_c395dca3._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_embedded-wallet_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/embedded-wallet.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/email.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_d5fe3bf9._.js",
  "static/chunks/node_modules_26d76838._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_email_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/email.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/socials.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_2e236f28._.js",
  "static/chunks/node_modules_1472c2a3._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_socials_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/socials.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/swaps.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_2afdbaac._.js",
  "static/chunks/node_modules_904e4b9b._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_swaps_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/swaps.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/send.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_d193df33._.js",
  "static/chunks/node_modules_6a6d452e._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_send_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/send.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/receive.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_8d2f0a31._.js",
  "static/chunks/node_modules_bb5ec138._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_receive_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/receive.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/onramp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_6deb94a0._.js",
  "static/chunks/node_modules_b7f3b7ac._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_onramp_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/onramp.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/transactions.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_fa74c466._.js",
  "static/chunks/node_modules_f2a4407e._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_transactions_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/transactions.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-pay/dist/esm/exports/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_922386f6._.js",
  "static/chunks/node_modules_b7e68bbb._.js",
  "static/chunks/node_modules_@reown_appkit-pay_dist_esm_exports_index_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-pay/dist/esm/exports/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_ea983d1d._.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_6ca6b3d7._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_0c1a2936._.js",
  "static/chunks/node_modules_7a4dfadf._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_index_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_828316b4._.js",
  "static/chunks/node_modules_5b20d2bf._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_w3m-modal_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/viem/_esm/utils/ccip.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_viem__esm_9956f058._.js",
  "static/chunks/node_modules_viem__esm_utils_ccip_bb652c78.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/viem/_esm/utils/ccip.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_44b2bcdf._.js",
  "static/chunks/node_modules_17dbeac7._.js",
  "static/chunks/node_modules_@wagmi_connectors_dist_esm_exports_index_e2a9ae0e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@wagmi/connectors/dist/esm/exports/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);