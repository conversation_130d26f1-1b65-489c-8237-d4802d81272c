'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import Link from 'next/link';

export default function TestStatusCheckPage() {
  const { address, isConnected } = useAppKitAccount();
  const [profileName, setProfileName] = useState('');
  const [statusResult, setStatusResult] = useState<any>(null);
  const [profileResult, setProfileResult] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);

  const checkStatus = async () => {
    if (!profileName) return;

    setIsChecking(true);
    try {
      // Call the check-status API
      const response = await fetch(`/api/profile/check-status?identifier=${profileName}`);
      const data = await response.json();
      setStatusResult(data);
    } catch (error) {
      console.error('Error checking status:', error);
      setStatusResult({ error: 'Failed to check status' });
    } finally {
      setIsChecking(false);
    }
  };

  const loadProfile = async () => {
    if (!profileName) return;

    setIsLoadingProfile(true);
    try {
      // Call the profile API
      const response = await fetch(`/api/profile/${profileName}`);
      const data = await response.json();
      setProfileResult(data);
    } catch (error) {
      console.error('Error loading profile:', error);
      setProfileResult({ error: 'Failed to load profile' });
    } finally {
      setIsLoadingProfile(false);
    }
  };

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-2xl font-bold mb-6">Profile Status Check Test</h1>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
        <div className="mb-4">
          <label htmlFor="profileName" className="block text-sm font-medium mb-2">
            Profile Name or Address
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              id="profileName"
              value={profileName}
              onChange={(e) => setProfileName(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
              placeholder="Enter profile name or address"
            />
            <button
              onClick={checkStatus}
              disabled={isChecking || !profileName}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isChecking ? 'Checking...' : 'Check Status'}
            </button>
            <button
              onClick={loadProfile}
              disabled={isLoadingProfile || !profileName}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isLoadingProfile ? 'Loading...' : 'Load Profile'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          {/* Status Result */}
          {statusResult && (
            <div className="p-4 border rounded-md">
              <h2 className="text-lg font-semibold mb-2">Status Check Result</h2>
              <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto text-xs">
                {JSON.stringify(statusResult, null, 2)}
              </pre>

              {statusResult.status && (
                <div className="mt-4">
                  <p className="mb-2">
                    <strong>Status:</strong>{' '}
                    <span className={`px-2 py-1 rounded ${
                      statusResult.isApproved ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                      'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                    }`}>
                      {statusResult.status}
                    </span>
                  </p>
                  <p className="mb-2"><strong>Approved:</strong> {statusResult.isApproved ? 'Yes' : 'No'}</p>
                  <p><strong>Message:</strong> {statusResult.message}</p>
                </div>
              )}
            </div>
          )}

          {/* Profile Result */}
          {profileResult && (
            <div className="p-4 border rounded-md">
              <h2 className="text-lg font-semibold mb-2">Profile API Result</h2>
              <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto">
                <p><strong>Address:</strong> {profileResult.address}</p>
                <p><strong>Name:</strong> {profileResult.name}</p>
                <p><strong>Components:</strong> {profileResult.components?.length || 0}</p>
              </div>

              <div className="mt-4">
                <Link
                  href={`/${profileName}`}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-block"
                >
                  Visit Profile
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
