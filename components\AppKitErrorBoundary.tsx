'use client';

import React from 'react';

interface AppKitErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface AppKitErrorBoundaryProps {
  children: React.ReactNode;
}

class AppKitErrorBoundary extends React.Component<AppKitErrorBoundaryProps, AppKitErrorBoundaryState> {
  constructor(props: AppKitErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): AppKitErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error for debugging
    console.error('AppKit Error:', error);
    console.error('Error Info:', errorInfo);
    
    // Check if it's a network-related error
    if (error.message.includes('403') || error.message.includes('HTTP status code')) {
      console.warn('Network connectivity issue detected. AppKit may have limited functionality.');
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI for AppKit errors
      return (
        <div className="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
              Wallet Connection Issue
            </h3>
            <p className="text-red-600 dark:text-red-300 mb-4">
              There was a temporary issue with the wallet connection service. Please try refreshing the page.
            </p>
            <button
              onClick={() => {
                this.setState({ hasError: false });
                window.location.reload();
              }}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AppKitErrorBoundary;
