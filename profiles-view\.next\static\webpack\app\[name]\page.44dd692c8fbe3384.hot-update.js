"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[name]/page",{

/***/ "(app-pages-browser)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit/react */ \"(app-pages-browser)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(app-pages-browser)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/profileStatus */ \"(app-pages-browser)/./lib/profileStatus.ts\");\n/* harmony import */ var _app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/components/renders/render_hero */ \"(app-pages-browser)/./app/components/renders/render_hero.tsx\");\n/* harmony import */ var _app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/renders/render_sociallinks */ \"(app-pages-browser)/./app/components/renders/render_sociallinks.tsx\");\n/* harmony import */ var _app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/renders/render_bannerpfp */ \"(app-pages-browser)/./app/components/renders/render_bannerpfp.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ClientPage(param) {\n    let { name } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statusChecked, setStatusChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = (0,_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata)();\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // First check profile status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            async function checkStatus() {\n                try {\n                    console.log('[ClientPage] Checking status for:', name);\n                    const statusResult = await (0,_lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__.checkProfileStatus)(name);\n                    console.log('[ClientPage] Status result:', statusResult);\n                    // Check if this is the user's own profile\n                    const isOwnProfile = address && statusResult.address && address.toLowerCase() === statusResult.address.toLowerCase();\n                    console.log('[ClientPage] Is own profile:', isOwnProfile);\n                    // If profile is not approved, redirect to error page\n                    // We no longer make an exception for the profile owner\n                    if (!statusResult.isApproved && statusResult.status !== 'not-found') {\n                        console.log('[ClientPage] Redirecting to error page for status:', statusResult.status);\n                        // Use the name parameter instead of address\n                        const nameParam = statusResult.name ? \"&name=\".concat(statusResult.name) : \"&name=\".concat(name);\n                        window.location.href = \"/profile-error?status=\".concat(statusResult.status).concat(nameParam);\n                        return;\n                    }\n                    setStatusChecked(true);\n                } catch (error) {\n                    console.error('[ClientPage] Error checking profile status:', error);\n                    setStatusChecked(true); // Continue anyway if status check fails\n                }\n            }\n            checkStatus();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        address\n    ]);\n    // Then fetch profile data once status is checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            if (!statusChecked) return; // Wait until status check is complete\n            async function fetchProfileData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('Fetching profile for name:', name);\n                    console.log('Current pathname:', pathname);\n                    // Fetch profile data by name\n                    // Always treat the parameter as a name, not an address\n                    const response = await fetch(\"/api/profile/\".concat(name));\n                    if (!response.ok) {\n                        // Handle errors with more specific messages\n                        const errorResponse = await response.json();\n                        if (response.status === 404) {\n                            // For 404 errors, show a user-friendly message\n                            if (errorResponse.error === 'No profiles exist in the database yet') {\n                                setError('No profiles have been created yet. Be the first to create one!');\n                            } else if (errorResponse.error === 'Profile components not properly initialized') {\n                                setError('Profile system is not properly initialized. Please contact support.');\n                            } else {\n                                setError('Profile \"'.concat(name, '\" not found'));\n                            }\n                            setLoading(false);\n                            return; // Exit early without throwing an error\n                        }\n                        // For other errors, log and throw\n                        console.error('Failed to fetch profile:', errorResponse);\n                        throw new Error(errorResponse.error || 'Failed to load profile');\n                    }\n                    const data = await response.json();\n                    setProfileData(data);\n                    // Fetch bannerpfp metadata\n                    if (data.address) {\n                        try {\n                            // Fetch bannerpfp metadata first\n                            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);\n                            if (bannerPfpMeta) {\n                                setBannerPfpMetadata(bannerPfpMeta);\n                                // Now fetch banner and profile picture metadata which will use the bannerpfp data\n                                const bannerMeta = await fetchBannerMetadata(data.address);\n                                if (bannerMeta) {\n                                    setBannerMetadata(bannerMeta);\n                                }\n                                const profilePicMeta = await fetchProfilePictureMetadata(data.address);\n                                if (profilePicMeta) {\n                                    setProfilePictureMetadata(profilePicMeta);\n                                }\n                            }\n                        } catch (metadataError) {\n                            console.error('Error fetching component metadata:', metadataError);\n                        // Continue showing the profile even if metadata fetch fails\n                        }\n                    }\n                } catch (err) {\n                    console.error('Error loading profile:', err);\n                    setError(err.message || 'An error occurred while loading the profile');\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchProfileData();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        fetchBannerMetadata,\n        fetchProfilePictureMetadata,\n        statusChecked\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen flex flex-col items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-4xl mx-auto px-4 sm:px-6 pt-24 pb-8 z-10 relative\",\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center min-h-[300px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 157,\n                columnNumber: 11\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-red-400 font-medium mb-2\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this) : profileData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-0 border border-neutral-700 overflow-hidden\",\n                children: profileData.components.filter((c)=>c.hidden !== 'Y').sort((a, b)=>parseInt(a.order) - parseInt(b.order)).map((component, index)=>{\n                    // Skip old component types\n                    if (component.componentType === 'banner' || component.componentType === 'profilePicture') {\n                        return null;\n                    } else if (component.componentType === 'details') {\n                        // Skip rendering deprecated 'details' component\n                        return null;\n                    } else if (component.componentType === 'hero') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 23\n                            }, this)\n                        }, \"hero-\".concat(component.order), false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'socialLinks') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                profileData: {\n                                    address: profileData.address,\n                                    chain: profileData.chain,\n                                    name: profileData.name || '',\n                                    bio: ''\n                                },\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 23\n                            }, this)\n                        }, \"socialLinks-\".concat(component.order), false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'bannerpfp' && bannerPfpMetadata) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false,\n                                profileName: profileData.name || '',\n                                profileBio: ''\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 23\n                            }, this)\n                        }, \"bannerpfp-\".concat(component.order), false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 21\n                        }, this);\n                    }\n                    return null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 172,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-yellow-400 font-medium mb-2\",\n                        children: \"Profile Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: [\n                            'The profile \"',\n                            name,\n                            \"\\\" doesn't exist or has been removed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 237,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientPage, \"y6NMnj1DDI8C4j2dYu33SjONjvw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount,\n        _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata\n    ];\n});\n_c = ClientPage;\nvar _c;\n$RefreshReg$(_c, \"ClientPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[name]/ClientPage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/renders/render_bannerpfp.tsx":
/*!*****************************************************!*\
  !*** ./app/components/renders/render_bannerpfp.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderBannerPfp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction RenderBannerPfp(param) {\n    let { address, componentData, showPositionLabel = false, profileName: propProfileName, profileBio: propProfileBio } = param;\n    var _bannerPfpMetadata_bannerPosition, _bannerPfpMetadata_bannerPosition1;\n    _s();\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileName, setProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileBio, setProfileBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileHorizontalPosition, setProfileHorizontalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [profileNameHorizontalPosition, setProfileNameHorizontalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [profileShape, setProfileShape] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('circular');\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const getBorderRadiusClass = ()=>{\n        switch(profileShape){\n            case 'rectangular':\n                return 'rounded-none';\n            case 'squarish':\n                return 'rounded-md';\n            case 'circular':\n            default:\n                return 'rounded-full';\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderBannerPfp.useEffect\": ()=>{\n            const loadBannerPfpData = {\n                \"RenderBannerPfp.useEffect.loadBannerPfpData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const response = await fetch(\"/api/bannerpfp/\".concat(address));\n                        if (!response.ok) {\n                            throw new Error('Failed to load banner/profile data');\n                        }\n                        const data = await response.json();\n                        setBannerPfpMetadata(data);\n                        // Set profile data from API response or props\n                        setProfileName(data.profileName || propProfileName || address.substring(0, 8));\n                        setProfileBio(data.profileBio || propProfileBio || '');\n                        setProfileHorizontalPosition(data.profileHorizontalPosition || 50);\n                        setProfileNameHorizontalPosition(data.profileNameHorizontalPosition || 50);\n                        setProfileShape(data.profileShape || 'circular');\n                    } catch (error) {\n                        console.error('Error loading banner/profile data:', error);\n                        setError('Failed to load banner/profile data');\n                        // Set fallback data\n                        setProfileName(propProfileName || address.substring(0, 8));\n                        setProfileBio(propProfileBio || '');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"RenderBannerPfp.useEffect.loadBannerPfpData\"];\n            if (address) {\n                loadBannerPfpData();\n            }\n        }\n    }[\"RenderBannerPfp.useEffect\"], [\n        address,\n        propProfileName,\n        propProfileBio\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !bannerPfpMetadata) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-neutral-400 p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: componentData.backgroundColor,\n                    width: '100%',\n                    minWidth: '100%',\n                    boxSizing: 'border-box',\n                    paddingBottom: '0.5rem'\n                },\n                className: \"w-full min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full\",\n                        style: {\n                            marginBottom: profileShape === 'rectangular' ? '9rem' : '8rem',\n                            width: '100%'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-48 md:h-64 relative overflow-hidden\",\n                                ref: containerRef,\n                                style: {\n                                    width: '100%',\n                                    minWidth: '100%'\n                                },\n                                children: (bannerPfpMetadata === null || bannerPfpMetadata === void 0 ? void 0 : bannerPfpMetadata.bannerUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"url(\".concat(bannerPfpMetadata.bannerUrl, \")\"),\n                                        backgroundSize: 'cover',\n                                        backgroundPosition: 'center',\n                                        transform: \"translate(\".concat(((_bannerPfpMetadata_bannerPosition = bannerPfpMetadata.bannerPosition) === null || _bannerPfpMetadata_bannerPosition === void 0 ? void 0 : _bannerPfpMetadata_bannerPosition.x) || 0, \"px, \").concat(((_bannerPfpMetadata_bannerPosition1 = bannerPfpMetadata.bannerPosition) === null || _bannerPfpMetadata_bannerPosition1 === void 0 ? void 0 : _bannerPfpMetadata_bannerPosition1.y) || 0, \"px) scale(\").concat(bannerPfpMetadata.bannerScale || 1, \")\"),\n                                        transformOrigin: 'center'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute flex justify-center\",\n                                style: {\n                                    bottom: profileShape === 'rectangular' ? '-4.5rem' : '-4rem',\n                                    left: \"\".concat(profileHorizontalPosition, \"%\"),\n                                    transform: 'translateX(-50%)',\n                                    zIndex: 10\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32', \" overflow-hidden \").concat(getBorderRadiusClass(), \" relative\"),\n                                    children: (bannerPfpMetadata === null || bannerPfpMetadata === void 0 ? void 0 : bannerPfpMetadata.profileUrl) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: bannerPfpMetadata.profileUrl,\n                                        alt: \"Profile\",\n                                        className: \"w-full h-full object-cover\",\n                                        style: {\n                                            transform: \"scale(\".concat(bannerPfpMetadata.profileScale || 1, \")\"),\n                                            transformOrigin: 'center'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-neutral-800 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 text-xs\",\n                                            children: \"No Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center w-full\",\n                        style: {\n                            left: \"\".concat(profileNameHorizontalPosition, \"%\"),\n                            transform: 'translateX(-50%)',\n                            position: 'relative'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center px-4\",\n                            children: [\n                                profileName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-2\",\n                                    style: {\n                                        color: componentData.fontColor || '#ffffff'\n                                    },\n                                    children: profileName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                profileBio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-300\",\n                                    style: {\n                                        color: componentData.fontColor || '#ffffff'\n                                    },\n                                    children: profileBio\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-purple-900/30 text-purple-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Banner/PFP\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(RenderBannerPfp, \"3tgZVMFkg09fo9zvpQN68Mcgi8Q=\");\n_c = RenderBannerPfp;\nvar _c;\n$RefreshReg$(_c, \"RenderBannerPfp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/renders/render_bannerpfp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/renders/render_hero.tsx":
/*!************************************************!*\
  !*** ./app/components/renders/render_hero.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderHero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction RenderHero(param) {\n    let { address, componentData, showPositionLabel = false } = param;\n    _s();\n    const [heroContent, setHeroContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderHero.useEffect\": ()=>{\n            const loadHeroContent = {\n                \"RenderHero.useEffect.loadHeroContent\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        // Fetch hero content from API\n                        const response = await fetch(\"/api/hero/\".concat(address));\n                        if (!response.ok) {\n                            throw new Error('Failed to load hero content');\n                        }\n                        const data = await response.json();\n                        if (data && data.heroContent && Array.isArray(data.heroContent)) {\n                            // Ensure all loaded content has textEffect field for backward compatibility\n                            const contentWithTextEffect = data.heroContent.map({\n                                \"RenderHero.useEffect.loadHeroContent.contentWithTextEffect\": (item)=>({\n                                        ...item,\n                                        textEffect: item.textEffect || 'decrypted'\n                                    })\n                            }[\"RenderHero.useEffect.loadHeroContent.contentWithTextEffect\"]);\n                            setHeroContent(contentWithTextEffect);\n                        } else {\n                            setError('No hero content found');\n                        }\n                    } catch (error) {\n                        console.error('Error loading hero content:', error);\n                        setError('Failed to load hero content');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"RenderHero.useEffect.loadHeroContent\"];\n            if (address) {\n                loadHeroContent();\n            }\n        }\n    }[\"RenderHero.useEffect\"], [\n        address\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || heroContent.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-neutral-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error || 'No hero content available'\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-0\",\n        style: {\n            backgroundColor: componentData.backgroundColor || 'transparent'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: heroContent.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 last:mb-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: item.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg mb-4\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: item.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            item.contentType === 'image' && item.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-64 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.imageUrl,\n                                    className: \"w-full h-full object-contain\",\n                                    alt: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            item.contentText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: item.contentText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Hero\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(RenderHero, \"faTeRaArO72jb9mjikVq6TYIWU4=\");\n_c = RenderHero;\nvar _c;\n$RefreshReg$(_c, \"RenderHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/renders/render_hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/renders/render_sociallinks.tsx":
/*!*******************************************************!*\
  !*** ./app/components/renders/render_sociallinks.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderSocialLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RenderSocialLinks(param) {\n    let { profileData, componentData, showPositionLabel = false } = param;\n    _s();\n    const [referralCode, setReferralCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [referralCount, setReferralCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderSocialLinks.useEffect\": ()=>{\n            const fetchReferralData = {\n                \"RenderSocialLinks.useEffect.fetchReferralData\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/referral/\".concat(profileData.address));\n                        if (response.ok) {\n                            const data = await response.json();\n                            setReferralCode(data.referralCode);\n                            setReferralCount(data.referralCount || 0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching referral data:', error);\n                    }\n                }\n            }[\"RenderSocialLinks.useEffect.fetchReferralData\"];\n            if (profileData.address) {\n                fetchReferralData();\n            }\n        }\n    }[\"RenderSocialLinks.useEffect\"], [\n        profileData.address\n    ]);\n    const copyReferralCode = async ()=>{\n        if (referralCode) {\n            try {\n                await navigator.clipboard.writeText(referralCode);\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (error) {\n                console.error('Failed to copy referral code:', error);\n            }\n        }\n    };\n    // Helper function to get social media icons\n    const getSocialIcon = (platform)=>{\n        // Simple text-based icons for view-only mode\n        const platformIcons = {\n            twitter: '🐦',\n            github: '🐙',\n            linkedin: '💼',\n            instagram: '📷',\n            website: '🌐',\n            email: '📧',\n            youtube: '📺',\n            twitch: '🎮',\n            telegram: '✈️',\n            discord: '💬',\n            facebook: '📘',\n            cro: '💎'\n        };\n        return platformIcons[platform.toLowerCase()] || '🔗';\n    };\n    // Format social links if they exist\n    const formatSocialLinks = ()=>{\n        if (!componentData.socialLinks) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-1 text-center py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-neutral-400\",\n                    children: \"No social links added yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this);\n        }\n        try {\n            const links = typeof componentData.socialLinks === 'string' ? JSON.parse(componentData.socialLinks) : componentData.socialLinks;\n            if (!links || Object.keys(links).length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this);\n            }\n            // Format links for display\n            const formattedLinks = Object.entries(links).filter((param)=>{\n                let [_, url] = param;\n                return url;\n            }) // Filter out empty URLs\n            .map((param)=>{\n                let [platform, url] = param;\n                const urlStr = String(url);\n                const isCustom = platform.startsWith('custom');\n                let customLabel = '';\n                let finalUrl = urlStr;\n                if (isCustom && urlStr.includes('|')) {\n                    const parts = urlStr.split('|');\n                    if (parts.length >= 2) {\n                        customLabel = parts[0].trim();\n                        finalUrl = parts.slice(1).join('|').trim();\n                    }\n                }\n                const displayName = isCustom ? customLabel || 'Custom Link' : platform;\n                const icon = getSocialIcon(platform);\n                return {\n                    platform,\n                    url: finalUrl,\n                    displayName,\n                    icon\n                };\n            });\n            if (formattedLinks.length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    style: {\n                        color: componentData.fontColor || undefined\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-4 p-4\",\n                style: {\n                    color: componentData.fontColor || undefined\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4\",\n                        children: formattedLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center gap-2 px-3 py-2 bg-neutral-800 hover:bg-neutral-700 rounded-lg transition-colors\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: link.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: link.displayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    referralCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-neutral-800/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-400 mb-2\",\n                                    children: \"Referral Code\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"px-3 py-1 bg-neutral-700 rounded text-sm font-mono\",\n                                            children: referralCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: copyReferralCode,\n                                            className: \"p-1 hover:bg-neutral-600 rounded transition-colors\",\n                                            title: \"Copy referral code\",\n                                            children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-4 h-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this),\n                                referralCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-500 mt-1\",\n                                    children: [\n                                        referralCount,\n                                        \" referral\",\n                                        referralCount !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this);\n        } catch (error) {\n            console.error('Error parsing social links:', error);\n            return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 w-full\",\n        style: {\n            backgroundColor: componentData.backgroundColor || 'transparent'\n        },\n        children: [\n            formatSocialLinks(),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-blue-900/30 text-blue-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Social Links\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(RenderSocialLinks, \"cy4LSJhrOGO6QACr8aVRkLnRPuU=\");\n_c = RenderSocialLinks;\nvar _c;\n$RefreshReg$(_c, \"RenderSocialLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL3JlbmRlcnMvcmVuZGVyX3NvY2lhbGxpbmtzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU0QztBQUNEO0FBc0I1QixTQUFTSSxrQkFBa0IsS0FJakI7UUFKaUIsRUFDeENDLFdBQVcsRUFDWEMsYUFBYSxFQUNiQyxvQkFBb0IsS0FBSyxFQUNGLEdBSmlCOztJQUt4QyxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHVCwrQ0FBUUEsQ0FBZ0I7SUFDaEUsTUFBTSxDQUFDVSxlQUFlQyxpQkFBaUIsR0FBR1gsK0NBQVFBLENBQVM7SUFDM0QsTUFBTSxDQUFDWSxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUFDO0lBRXJDQyxnREFBU0E7dUNBQUM7WUFDUixNQUFNYTtpRUFBb0I7b0JBQ3hCLElBQUk7d0JBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGlCQUFxQyxPQUFwQlgsWUFBWVksT0FBTzt3QkFDakUsSUFBSUYsU0FBU0csRUFBRSxFQUFFOzRCQUNmLE1BQU1DLE9BQU8sTUFBTUosU0FBU0ssSUFBSTs0QkFDaENYLGdCQUFnQlUsS0FBS1gsWUFBWTs0QkFDakNHLGlCQUFpQlEsS0FBS1QsYUFBYSxJQUFJO3dCQUN6QztvQkFDRixFQUFFLE9BQU9XLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO29CQUNqRDtnQkFDRjs7WUFFQSxJQUFJaEIsWUFBWVksT0FBTyxFQUFFO2dCQUN2Qkg7WUFDRjtRQUNGO3NDQUFHO1FBQUNULFlBQVlZLE9BQU87S0FBQztJQUV4QixNQUFNTSxtQkFBbUI7UUFDdkIsSUFBSWYsY0FBYztZQUNoQixJQUFJO2dCQUNGLE1BQU1nQixVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ2xCO2dCQUNwQ0ssVUFBVTtnQkFDVmMsV0FBVyxJQUFNZCxVQUFVLFFBQVE7WUFDckMsRUFBRSxPQUFPUSxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUNqRDtRQUNGO0lBQ0Y7SUFFQSw0Q0FBNEM7SUFDNUMsTUFBTU8sZ0JBQWdCLENBQUNDO1FBQ3JCLDZDQUE2QztRQUM3QyxNQUFNQyxnQkFBd0M7WUFDNUNDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxLQUFLO1FBQ1A7UUFFQSxPQUFPWixhQUFhLENBQUNELFNBQVNjLFdBQVcsR0FBRyxJQUFJO0lBQ2xEO0lBRUEsb0NBQW9DO0lBQ3BDLE1BQU1DLG9CQUFvQjtRQUN4QixJQUFJLENBQUN0QyxjQUFjdUMsV0FBVyxFQUFFO1lBQzlCLHFCQUNFLDhEQUFDQztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUVELFdBQVU7OEJBQTJCOzs7Ozs7Ozs7OztRQUc5QztRQUVBLElBQUk7WUFDRixNQUFNRSxRQUFRLE9BQU8zQyxjQUFjdUMsV0FBVyxLQUFLLFdBQy9DSyxLQUFLQyxLQUFLLENBQUM3QyxjQUFjdUMsV0FBVyxJQUNwQ3ZDLGNBQWN1QyxXQUFXO1lBRTdCLElBQUksQ0FBQ0ksU0FBU0csT0FBT0MsSUFBSSxDQUFDSixPQUFPSyxNQUFNLEtBQUssR0FBRztnQkFDN0MscUJBQ0UsOERBQUNSO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBRUQsV0FBVTtrQ0FBMkI7Ozs7Ozs7Ozs7O1lBRzlDO1lBRUEsMkJBQTJCO1lBQzNCLE1BQU1RLGlCQUFpQkgsT0FBT0ksT0FBTyxDQUFDUCxPQUNuQ1EsTUFBTSxDQUFDO29CQUFDLENBQUNDLEdBQUdDLElBQUk7dUJBQUtBO2VBQUssd0JBQXdCO2FBQ2xEQyxHQUFHLENBQUM7b0JBQUMsQ0FBQy9CLFVBQVU4QixJQUFJO2dCQUNuQixNQUFNRSxTQUFTQyxPQUFPSDtnQkFDdEIsTUFBTUksV0FBV2xDLFNBQVNtQyxVQUFVLENBQUM7Z0JBRXJDLElBQUlDLGNBQWM7Z0JBQ2xCLElBQUlDLFdBQVdMO2dCQUVmLElBQUlFLFlBQVlGLE9BQU9NLFFBQVEsQ0FBQyxNQUFNO29CQUNwQyxNQUFNQyxRQUFRUCxPQUFPUSxLQUFLLENBQUM7b0JBQzNCLElBQUlELE1BQU1kLE1BQU0sSUFBSSxHQUFHO3dCQUNyQlcsY0FBY0csS0FBSyxDQUFDLEVBQUUsQ0FBQ0UsSUFBSTt3QkFDM0JKLFdBQVdFLE1BQU1HLEtBQUssQ0FBQyxHQUFHQyxJQUFJLENBQUMsS0FBS0YsSUFBSTtvQkFDMUM7Z0JBQ0Y7Z0JBRUEsTUFBTUcsY0FBY1YsV0FBWUUsZUFBZSxnQkFBaUJwQztnQkFDaEUsTUFBTTZDLE9BQU85QyxjQUFjQztnQkFFM0IsT0FBTztvQkFDTEE7b0JBQ0E4QixLQUFLTztvQkFDTE87b0JBQ0FDO2dCQUNGO1lBQ0Y7WUFFRixJQUFJbkIsZUFBZUQsTUFBTSxLQUFLLEdBQUc7Z0JBQy9CLHFCQUNFLDhEQUFDUjtvQkFBSUMsV0FBVTtvQkFBd0I0QixPQUFPO3dCQUFFQyxPQUFPdEUsY0FBY3VFLFNBQVMsSUFBSUM7b0JBQVU7OEJBQzFGLDRFQUFDOUI7d0JBQUVELFdBQVU7a0NBQTJCOzs7Ozs7Ozs7OztZQUc5QztZQUVBLHFCQUNFLDhEQUFDRDtnQkFBSUMsV0FBVTtnQkFBVzRCLE9BQU87b0JBQUVDLE9BQU90RSxjQUFjdUUsU0FBUyxJQUFJQztnQkFBVTs7a0NBQzdFLDhEQUFDaEM7d0JBQUlDLFdBQVU7a0NBQ1pRLGVBQWVLLEdBQUcsQ0FBQyxDQUFDbUIsTUFBTUMsc0JBQ3pCLDhEQUFDQztnQ0FFQ0MsTUFBTUgsS0FBS3BCLEdBQUc7Z0NBQ2R3QixRQUFPO2dDQUNQQyxLQUFJO2dDQUNKckMsV0FBVTtnQ0FDVjRCLE9BQU87b0NBQUVDLE9BQU90RSxjQUFjdUUsU0FBUyxJQUFJO2dDQUFVOztrREFFckQsOERBQUNRO3dDQUFLdEMsV0FBVTtrREFBV2dDLEtBQUtMLElBQUk7Ozs7OztrREFDcEMsOERBQUNXO3dDQUFLdEMsV0FBVTtrREFBV2dDLEtBQUtOLFdBQVc7Ozs7Ozs7K0JBUnRDTzs7Ozs7Ozs7OztvQkFjVnhFLDhCQUNDLDhEQUFDc0M7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUVELFdBQVU7OENBQWdDOzs7Ozs7OENBQzdDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUN1Qzs0Q0FBS3ZDLFdBQVU7c0RBQ2J2Qzs7Ozs7O3NEQUVILDhEQUFDK0U7NENBQ0NDLFNBQVNqRTs0Q0FDVHdCLFdBQVU7NENBQ1YwQyxPQUFNO3NEQUVMN0UsdUJBQ0MsOERBQUNULHNGQUFLQTtnREFBQzRDLFdBQVU7Ozs7O3FFQUVqQiw4REFBQzdDLHNGQUFJQTtnREFBQzZDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUlyQnJDLGdCQUFnQixtQkFDZiw4REFBQ3NDO29DQUFFRCxXQUFVOzt3Q0FDVnJDO3dDQUFjO3dDQUFVQSxrQkFBa0IsSUFBSSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFRckUsRUFBRSxPQUFPVyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLE9BQU87UUFDVDtJQUNGO0lBRUEscUJBQ0UsOERBQUN5QjtRQUNDQyxXQUFVO1FBQ1Y0QixPQUFPO1lBQUVlLGlCQUFpQnBGLGNBQWNvRixlQUFlLElBQUk7UUFBYzs7WUFFeEU5QztZQUVBckMsbUNBQ0MsOERBQUN1QztnQkFBSUMsV0FBVTswQkFBaUc7Ozs7Ozs7Ozs7OztBQU14SDtHQTlMd0IzQztLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcYXBwXFxjb21wb25lbnRzXFxyZW5kZXJzXFxyZW5kZXJfc29jaWFsbGlua3MudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENvcHksIENoZWNrIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuaW50ZXJmYWNlIFJlbmRlclNvY2lhbExpbmtzUHJvcHMge1xuICBwcm9maWxlRGF0YToge1xuICAgIGFkZHJlc3M6IHN0cmluZztcbiAgICBjaGFpbjogc3RyaW5nO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBiaW86IHN0cmluZztcbiAgfTtcbiAgY29tcG9uZW50RGF0YToge1xuICAgIGFkZHJlc3M6IHN0cmluZztcbiAgICBjaGFpbjogc3RyaW5nO1xuICAgIGNvbXBvbmVudFR5cGU6IHN0cmluZztcbiAgICBvcmRlcjogc3RyaW5nO1xuICAgIGhpZGRlbjogc3RyaW5nO1xuICAgIGJhY2tncm91bmRDb2xvcj86IHN0cmluZztcbiAgICBmb250Q29sb3I/OiBzdHJpbmcgfCBudWxsO1xuICAgIHNvY2lhbExpbmtzPzogYW55O1xuICB9O1xuICBzaG93UG9zaXRpb25MYWJlbD86IGJvb2xlYW47XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJlbmRlclNvY2lhbExpbmtzKHtcbiAgcHJvZmlsZURhdGEsXG4gIGNvbXBvbmVudERhdGEsXG4gIHNob3dQb3NpdGlvbkxhYmVsID0gZmFsc2Vcbn06IFJlbmRlclNvY2lhbExpbmtzUHJvcHMpIHtcbiAgY29uc3QgW3JlZmVycmFsQ29kZSwgc2V0UmVmZXJyYWxDb2RlXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbcmVmZXJyYWxDb3VudCwgc2V0UmVmZXJyYWxDb3VudF0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xuICBjb25zdCBbY29waWVkLCBzZXRDb3BpZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgZmV0Y2hSZWZlcnJhbERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3JlZmVycmFsLyR7cHJvZmlsZURhdGEuYWRkcmVzc31gKTtcbiAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICBzZXRSZWZlcnJhbENvZGUoZGF0YS5yZWZlcnJhbENvZGUpO1xuICAgICAgICAgIHNldFJlZmVycmFsQ291bnQoZGF0YS5yZWZlcnJhbENvdW50IHx8IDApO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyByZWZlcnJhbCBkYXRhOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaWYgKHByb2ZpbGVEYXRhLmFkZHJlc3MpIHtcbiAgICAgIGZldGNoUmVmZXJyYWxEYXRhKCk7XG4gICAgfVxuICB9LCBbcHJvZmlsZURhdGEuYWRkcmVzc10pO1xuXG4gIGNvbnN0IGNvcHlSZWZlcnJhbENvZGUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKHJlZmVycmFsQ29kZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQocmVmZXJyYWxDb2RlKTtcbiAgICAgICAgc2V0Q29waWVkKHRydWUpO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHNldENvcGllZChmYWxzZSksIDIwMDApO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNvcHkgcmVmZXJyYWwgY29kZTonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgc29jaWFsIG1lZGlhIGljb25zXG4gIGNvbnN0IGdldFNvY2lhbEljb24gPSAocGxhdGZvcm06IHN0cmluZykgPT4ge1xuICAgIC8vIFNpbXBsZSB0ZXh0LWJhc2VkIGljb25zIGZvciB2aWV3LW9ubHkgbW9kZVxuICAgIGNvbnN0IHBsYXRmb3JtSWNvbnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgICB0d2l0dGVyOiAn8J+QpicsXG4gICAgICBnaXRodWI6ICfwn5CZJyxcbiAgICAgIGxpbmtlZGluOiAn8J+SvCcsXG4gICAgICBpbnN0YWdyYW06ICfwn5O3JyxcbiAgICAgIHdlYnNpdGU6ICfwn4yQJyxcbiAgICAgIGVtYWlsOiAn8J+TpycsXG4gICAgICB5b3V0dWJlOiAn8J+TuicsXG4gICAgICB0d2l0Y2g6ICfwn46uJyxcbiAgICAgIHRlbGVncmFtOiAn4pyI77iPJyxcbiAgICAgIGRpc2NvcmQ6ICfwn5KsJyxcbiAgICAgIGZhY2Vib29rOiAn8J+TmCcsXG4gICAgICBjcm86ICfwn5KOJ1xuICAgIH07XG5cbiAgICByZXR1cm4gcGxhdGZvcm1JY29uc1twbGF0Zm9ybS50b0xvd2VyQ2FzZSgpXSB8fCAn8J+Ulyc7XG4gIH07XG5cbiAgLy8gRm9ybWF0IHNvY2lhbCBsaW5rcyBpZiB0aGV5IGV4aXN0XG4gIGNvbnN0IGZvcm1hdFNvY2lhbExpbmtzID0gKCkgPT4ge1xuICAgIGlmICghY29tcG9uZW50RGF0YS5zb2NpYWxMaW5rcykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteS0xIHRleHQtY2VudGVyIHB5LTJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC00MDBcIj5ObyBzb2NpYWwgbGlua3MgYWRkZWQgeWV0PC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGxpbmtzID0gdHlwZW9mIGNvbXBvbmVudERhdGEuc29jaWFsTGlua3MgPT09ICdzdHJpbmcnXG4gICAgICAgID8gSlNPTi5wYXJzZShjb21wb25lbnREYXRhLnNvY2lhbExpbmtzKVxuICAgICAgICA6IGNvbXBvbmVudERhdGEuc29jaWFsTGlua3M7XG5cbiAgICAgIGlmICghbGlua3MgfHwgT2JqZWN0LmtleXMobGlua3MpLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktMSB0ZXh0LWNlbnRlciBweS0yXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC00MDBcIj5ObyBzb2NpYWwgbGlua3MgYWRkZWQgeWV0PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICAvLyBGb3JtYXQgbGlua3MgZm9yIGRpc3BsYXlcbiAgICAgIGNvbnN0IGZvcm1hdHRlZExpbmtzID0gT2JqZWN0LmVudHJpZXMobGlua3MpXG4gICAgICAgIC5maWx0ZXIoKFtfLCB1cmxdKSA9PiB1cmwpIC8vIEZpbHRlciBvdXQgZW1wdHkgVVJMc1xuICAgICAgICAubWFwKChbcGxhdGZvcm0sIHVybF0pID0+IHtcbiAgICAgICAgICBjb25zdCB1cmxTdHIgPSBTdHJpbmcodXJsKTtcbiAgICAgICAgICBjb25zdCBpc0N1c3RvbSA9IHBsYXRmb3JtLnN0YXJ0c1dpdGgoJ2N1c3RvbScpO1xuICAgICAgICAgIFxuICAgICAgICAgIGxldCBjdXN0b21MYWJlbCA9ICcnO1xuICAgICAgICAgIGxldCBmaW5hbFVybCA9IHVybFN0cjtcblxuICAgICAgICAgIGlmIChpc0N1c3RvbSAmJiB1cmxTdHIuaW5jbHVkZXMoJ3wnKSkge1xuICAgICAgICAgICAgY29uc3QgcGFydHMgPSB1cmxTdHIuc3BsaXQoJ3wnKTtcbiAgICAgICAgICAgIGlmIChwYXJ0cy5sZW5ndGggPj0gMikge1xuICAgICAgICAgICAgICBjdXN0b21MYWJlbCA9IHBhcnRzWzBdLnRyaW0oKTtcbiAgICAgICAgICAgICAgZmluYWxVcmwgPSBwYXJ0cy5zbGljZSgxKS5qb2luKCd8JykudHJpbSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IGRpc3BsYXlOYW1lID0gaXNDdXN0b20gPyAoY3VzdG9tTGFiZWwgfHwgJ0N1c3RvbSBMaW5rJykgOiBwbGF0Zm9ybTtcbiAgICAgICAgICBjb25zdCBpY29uID0gZ2V0U29jaWFsSWNvbihwbGF0Zm9ybSk7XG5cbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcGxhdGZvcm0sXG4gICAgICAgICAgICB1cmw6IGZpbmFsVXJsLFxuICAgICAgICAgICAgZGlzcGxheU5hbWUsXG4gICAgICAgICAgICBpY29uXG4gICAgICAgICAgfTtcbiAgICAgICAgfSk7XG5cbiAgICAgIGlmIChmb3JtYXR0ZWRMaW5rcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTEgdGV4dC1jZW50ZXIgcHktMlwiIHN0eWxlPXt7IGNvbG9yOiBjb21wb25lbnREYXRhLmZvbnRDb2xvciB8fCB1bmRlZmluZWQgfX0+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC00MDBcIj5ObyBzb2NpYWwgbGlua3MgYWRkZWQgeWV0PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTQgcC00XCIgc3R5bGU9e3sgY29sb3I6IGNvbXBvbmVudERhdGEuZm9udENvbG9yIHx8IHVuZGVmaW5lZCB9fT5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGp1c3RpZnktY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICB7Zm9ybWF0dGVkTGlua3MubWFwKChsaW5rLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgaHJlZj17bGluay51cmx9XG4gICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC0zIHB5LTIgYmctbmV1dHJhbC04MDAgaG92ZXI6YmctbmV1dHJhbC03MDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgY29sb3I6IGNvbXBvbmVudERhdGEuZm9udENvbG9yIHx8ICcjZmZmZmZmJyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZ1wiPntsaW5rLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj57bGluay5kaXNwbGF5TmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiBSZWZlcnJhbCBDb2RlIFNlY3Rpb24gKi99XG4gICAgICAgICAge3JlZmVycmFsQ29kZSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLW5ldXRyYWwtODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTQwMCBtYi0yXCI+UmVmZXJyYWwgQ29kZTwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8Y29kZSBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctbmV1dHJhbC03MDAgcm91bmRlZCB0ZXh0LXNtIGZvbnQtbW9ub1wiPlxuICAgICAgICAgICAgICAgICAgICB7cmVmZXJyYWxDb2RlfVxuICAgICAgICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb3B5UmVmZXJyYWxDb2RlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgaG92ZXI6YmctbmV1dHJhbC02MDAgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ29weSByZWZlcnJhbCBjb2RlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2NvcGllZCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LW5ldXRyYWwtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHtyZWZlcnJhbENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbmV1dHJhbC01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICB7cmVmZXJyYWxDb3VudH0gcmVmZXJyYWx7cmVmZXJyYWxDb3VudCAhPT0gMSA/ICdzJyA6ICcnfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIHNvY2lhbCBsaW5rczonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBib3JkZXIgYm9yZGVyLW5ldXRyYWwtODAwIHctZnVsbFwiXG4gICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGNvbXBvbmVudERhdGEuYmFja2dyb3VuZENvbG9yIHx8ICd0cmFuc3BhcmVudCcgfX1cbiAgICA+XG4gICAgICB7Zm9ybWF0U29jaWFsTGlua3MoKX1cblxuICAgICAge3Nob3dQb3NpdGlvbkxhYmVsICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yIGJnLWJsdWUtOTAwLzMwIHRleHQtYmx1ZS00MDAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgU29jaWFsIExpbmtzXG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNvcHkiLCJDaGVjayIsIlJlbmRlclNvY2lhbExpbmtzIiwicHJvZmlsZURhdGEiLCJjb21wb25lbnREYXRhIiwic2hvd1Bvc2l0aW9uTGFiZWwiLCJyZWZlcnJhbENvZGUiLCJzZXRSZWZlcnJhbENvZGUiLCJyZWZlcnJhbENvdW50Iiwic2V0UmVmZXJyYWxDb3VudCIsImNvcGllZCIsInNldENvcGllZCIsImZldGNoUmVmZXJyYWxEYXRhIiwicmVzcG9uc2UiLCJmZXRjaCIsImFkZHJlc3MiLCJvayIsImRhdGEiLCJqc29uIiwiZXJyb3IiLCJjb25zb2xlIiwiY29weVJlZmVycmFsQ29kZSIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsInNldFRpbWVvdXQiLCJnZXRTb2NpYWxJY29uIiwicGxhdGZvcm0iLCJwbGF0Zm9ybUljb25zIiwidHdpdHRlciIsImdpdGh1YiIsImxpbmtlZGluIiwiaW5zdGFncmFtIiwid2Vic2l0ZSIsImVtYWlsIiwieW91dHViZSIsInR3aXRjaCIsInRlbGVncmFtIiwiZGlzY29yZCIsImZhY2Vib29rIiwiY3JvIiwidG9Mb3dlckNhc2UiLCJmb3JtYXRTb2NpYWxMaW5rcyIsInNvY2lhbExpbmtzIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImxpbmtzIiwiSlNPTiIsInBhcnNlIiwiT2JqZWN0Iiwia2V5cyIsImxlbmd0aCIsImZvcm1hdHRlZExpbmtzIiwiZW50cmllcyIsImZpbHRlciIsIl8iLCJ1cmwiLCJtYXAiLCJ1cmxTdHIiLCJTdHJpbmciLCJpc0N1c3RvbSIsInN0YXJ0c1dpdGgiLCJjdXN0b21MYWJlbCIsImZpbmFsVXJsIiwiaW5jbHVkZXMiLCJwYXJ0cyIsInNwbGl0IiwidHJpbSIsInNsaWNlIiwiam9pbiIsImRpc3BsYXlOYW1lIiwiaWNvbiIsInN0eWxlIiwiY29sb3IiLCJmb250Q29sb3IiLCJ1bmRlZmluZWQiLCJsaW5rIiwiaW5kZXgiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsInNwYW4iLCJjb2RlIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwiYmFja2dyb3VuZENvbG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/renders/render_sociallinks.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Check)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.501.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 6 9 17l-5-5\",\n            key: \"1gmf2c\"\n        }\n    ]\n];\nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"check\", __iconNode);\n //# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsaUJBQXVCO0lBQUM7UUFBQyxNQUFRO1FBQUEsQ0FBRTtZQUFBLEVBQUcsa0JBQW1CO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhaEYsWUFBUSxrRUFBaUIsVUFBUyxDQUFVIiwic291cmNlcyI6WyJDOlxcc3JjXFxpY29uc1xcY2hlY2sudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncGF0aCcsIHsgZDogJ00yMCA2IDkgMTdsLTUtNScsIGtleTogJzFnbWYyYycgfV1dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hlY2tcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qQWdOaUE1SURFM2JDMDFMVFVpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hlY2tcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaGVjayA9IGNyZWF0ZUx1Y2lkZUljb24oJ2NoZWNrJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZWNrO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.501.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n];\nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"copy\", __iconNode);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ })

});