'use client';
import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import type { AppKitNetwork } from '@reown/appkit/networks'
import { SolanaAdapter } from '@reown/appkit-adapter-solana'

import {
  mainnet,
  arbitrum,
  sepolia,
  solana,
  cronoszkEVM,
  cronos
} from "@reown/appkit/networks";


export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Validate project ID format
if (projectId.length !== 32) {
  console.warn('Project ID may be invalid - should be 32 characters');
}
export const metadata = {
    name: 'Web3Socials',
    description: 'Web3 Social Platform',
    url: typeof window !== 'undefined' ? window.location.origin : 'https://web3socials.fun',
    icons: ['https://avatars.githubusercontent.com/u/179229932']
  }

// Temporarily reduce networks to test 403 error - you can add more back later
export const networks = [cronos,mainnet,
  arbitrum,
  sepolia,
  solana,
  cronoszkEVM] as [AppKitNetwork, ...AppKitNetwork[]];

export const wagmiAdapter = new WagmiAdapter({
  ssr: true,
  projectId,
  networks
});

const solanaWeb3JsAdapter = new SolanaAdapter()

const generalConfig = {
  projectId,
  networks,
  metadata,
  themeMode: 'dark' as const,
  themeVariables: {
    '--w3m-accent': '#000000',
  }
}

// Prevent ethereum object conflicts
if (typeof window !== 'undefined') {
  // Clear any existing ethereum property conflicts
  try {
    delete (window as any).ethereum;
  } catch (e) {
    // Ignore if property can't be deleted
  }
}

createAppKit({
  adapters: [wagmiAdapter, solanaWeb3JsAdapter],
  ...generalConfig,
  features: {
    swaps: false,
    onramp: false,
    email: true,
    socials: false,
    history: false,
    analytics: false,
    allWallets: true,
    send: false,
    },
  // Disable features that might cause network requests
  featuredWalletIds: []
})

export const config = wagmiAdapter.wagmiConfig
