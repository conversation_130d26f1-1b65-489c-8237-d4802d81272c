{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/components/SimpleNavbar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nexport default function SimpleNavbar() {\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-neutral-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-12\">\n          <div className=\"flex items-center\">\n            <span className=\"text-white font-semibold text-lg\">\n              Web3Socials Profiles\n            </span>\n          </div>\n          <div className=\"text-neutral-400 text-sm\">\n            View Only\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;kCAIrD,8OAAC;wBAAI,WAAU;kCAA2B;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/config/index.tsx"], "sourcesContent": ["'use client';\nimport { createAppKit } from '@reown/appkit/react'\nimport { WagmiAdapter } from \"@reown/appkit-adapter-wagmi\";\nimport type { AppKitNetwork } from '@reown/appkit/networks'\nimport { SolanaAdapter } from '@reown/appkit-adapter-solana'\n\nimport {\n  mainnet,\n  arbitrum,\n  sepolia,\n  solana,\n  cronoszkEVM,\n  cronos\n} from \"@reown/appkit/networks\";\n\n\nexport const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;\n\nif (!projectId) {\n  throw new Error(\"Project ID is not defined\");\n}\n\n// Validate project ID format\nif (projectId.length !== 32) {\n  console.warn('Project ID may be invalid - should be 32 characters');\n}\nexport const metadata = {\n    name: 'Web3Socials',\n    description: 'Web3 Social Platform',\n    url: typeof window !== 'undefined' ? window.location.origin : 'https://web3socials.fun',\n    icons: ['https://avatars.githubusercontent.com/u/179229932']\n  }\n\n// Temporarily reduce networks to test 403 error - you can add more back later\nexport const networks = [cronos,mainnet,\n  arbitrum,\n  sepolia,\n  solana,\n  cronoszkEVM] as [AppKitNetwork, ...AppKitNetwork[]];\n\nexport const wagmiAdapter = new WagmiAdapter({\n  ssr: true,\n  projectId,\n  networks\n});\n\nconst solanaWeb3JsAdapter = new SolanaAdapter()\n\nconst generalConfig = {\n  projectId,\n  networks,\n  metadata,\n  themeMode: 'dark' as const,\n  themeVariables: {\n    '--w3m-accent': '#000000',\n  }\n}\n\n// Prevent ethereum object conflicts\nif (typeof window !== 'undefined') {\n  // Clear any existing ethereum property conflicts\n  try {\n    delete (window as any).ethereum;\n  } catch (e) {\n    // Ignore if property can't be deleted\n  }\n}\n\ncreateAppKit({\n  adapters: [wagmiAdapter, solanaWeb3JsAdapter],\n  ...generalConfig,\n  features: {\n    swaps: false,\n    onramp: false,\n    email: true,\n    socials: false,\n    history: false,\n    analytics: false,\n    allWallets: true,\n    send: false,\n    },\n  // Disable features that might cause network requests\n  featuredWalletIds: []\n})\n\nexport const config = wagmiAdapter.wagmiConfig\n"], "names": [], "mappings": ";;;;;;;AACA;AAAA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;AAgBO,MAAM;AAEb,uCAAgB;;AAEhB;AAEA,6BAA6B;AAC7B,IAAI,UAAU,MAAM,KAAK,IAAI;IAC3B,QAAQ,IAAI,CAAC;AACf;AACO,MAAM,WAAW;IACpB,MAAM;IACN,aAAa;IACb,KAAK,6EAAyD;IAC9D,OAAO;QAAC;KAAoD;AAC9D;AAGK,MAAM,WAAW;IAAC,+JAAA,CAAA,SAAM;IAAC,gKAAA,CAAA,UAAO;IACrC,iKAAA,CAAA,WAAQ;IACR,gKAAA,CAAA,UAAO;IACP,uLAAA,CAAA,SAAM;IACN,oKAAA,CAAA,cAAW;CAAC;AAEP,MAAM,eAAe,IAAI,qLAAA,CAAA,eAAY,CAAC;IAC3C,KAAK;IACL;IACA;AACF;AAEA,MAAM,sBAAsB,IAAI,sLAAA,CAAA,gBAAa;AAE7C,MAAM,gBAAgB;IACpB;IACA;IACA;IACA,WAAW;IACX,gBAAgB;QACd,gBAAgB;IAClB;AACF;AAEA,oCAAoC;AACpC,uCAAmC;;AAOnC;AAEA,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE;IACX,UAAU;QAAC;QAAc;KAAoB;IAC7C,GAAG,aAAa;IAChB,UAAU;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,SAAS;QACT,WAAW;QACX,YAAY;QACZ,MAAM;IACN;IACF,qDAAqD;IACrD,mBAAmB,EAAE;AACvB;AAEO,MAAM,SAAS,aAAa,WAAW", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/app/contexts/MetadataContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\ninterface ImageMetadata {\n  blobUrl: string;\n  scale: number;\n  position: { x: number; y: number };\n  naturalSize?: { width: number; height: number };\n  shape?: string;\n}\n\ninterface MetadataContextType {\n  bannerMetadata: ImageMetadata | null;\n  profilePictureMetadata: ImageMetadata | null;\n  bannerPfpMetadata: any | null;\n  fetchBannerMetadata: (address: string) => Promise<ImageMetadata | null>;\n  fetchProfilePictureMetadata: (address: string, compPosition?: string) => Promise<ImageMetadata | null>;\n  fetchBannerPfpMetadata: (address: string) => Promise<any | null>;\n  clearMetadata: () => void;\n}\n\nconst MetadataContext = createContext<MetadataContextType | undefined>(undefined);\n\nexport function MetadataProvider({ children }: { children: ReactNode }) {\n  const [bannerMetadata, setBannerMetadata] = useState<ImageMetadata | null>(null);\n  const [profilePictureMetadata, setProfilePictureMetadata] = useState<ImageMetadata | null>(null);\n  const [bannerPfpMetadata, setBannerPfpMetadata] = useState<any | null>(null);\n  const [currentAddress, setCurrentAddress] = useState<string | null>(null);\n\n  // Function to fetch banner metadata - DEPRECATED\n  // Now using bannerpfp component instead\n  const fetchBannerMetadata = async (address: string): Promise<ImageMetadata | null> => {\n    console.log('Banner component is deprecated, using bannerpfp instead');\n    // Try to get banner data from bannerpfp metadata\n    const bannerpfpData = await fetchBannerPfpMetadata(address);\n    if (bannerpfpData && bannerpfpData.bannerBlobUrl) {\n      const completeMetadata = {\n        blobUrl: bannerpfpData.bannerBlobUrl,\n        scale: bannerpfpData.bannerScale || 1,\n        position: { x: 0, y: 0 },\n        naturalSize: undefined\n      };\n      setBannerMetadata(completeMetadata);\n      return completeMetadata;\n    }\n    return null;\n  };\n\n  // Function to fetch profile picture metadata - DEPRECATED\n  // Now using bannerpfp component instead\n  const fetchProfilePictureMetadata = async (address: string, compPosition?: string): Promise<ImageMetadata | null> => {\n    console.log('ProfilePicture component is deprecated, using bannerpfp instead');\n    // Try to get profile picture data from bannerpfp metadata\n    const bannerpfpData = await fetchBannerPfpMetadata(address);\n    if (bannerpfpData && bannerpfpData.profileBlobUrl) {\n      const completeMetadata = {\n        blobUrl: bannerpfpData.profileBlobUrl,\n        scale: bannerpfpData.profileScale || 1,\n        position: { x: 0, y: 0 },\n        naturalSize: undefined,\n        shape: bannerpfpData.profileShape || 'circular'\n      };\n      setProfilePictureMetadata(completeMetadata);\n      return completeMetadata;\n    }\n    return null;\n  };\n\n  // Track in-progress fetches to prevent duplicate calls\n  const fetchingAddresses = new Set<string>();\n\n  // Function to fetch bannerpfp metadata\n  const fetchBannerPfpMetadata = async (address: string): Promise<any | null> => {\n    // If we already have metadata for this address, return it\n    if (bannerPfpMetadata && currentAddress === address) {\n      console.log('MetadataContext: Using cached bannerpfp metadata for', address);\n      return bannerPfpMetadata;\n    }\n\n    // If we're already fetching this address, wait for it to complete\n    if (fetchingAddresses.has(address)) {\n      console.log('MetadataContext: Already fetching bannerpfp metadata for', address);\n      // Wait a bit and return the current metadata (which should be updated by the in-progress fetch)\n      await new Promise(resolve => setTimeout(resolve, 500));\n      return bannerPfpMetadata;\n    }\n\n    // Mark this address as being fetched\n    fetchingAddresses.add(address);\n\n    try {\n      console.log('MetadataContext: Fetching bannerpfp metadata for', address);\n      const response = await fetch(`/api/bannerpfp/${address}`);\n\n      if (!response.ok) {\n        // If the response is not OK, log the error but don't throw\n        // This allows the UI to continue rendering even if metadata is missing\n        console.warn(`Failed to fetch bannerpfp metadata: ${response.status} ${response.statusText}`);\n\n        // For 404 errors, we'll create a default metadata object\n        if (response.status === 404) {\n          console.log('Creating default bannerpfp metadata since none exists');\n          const defaultMetadata = {\n            profileName: address.substring(0, 8),\n            profileShape: 'circular',\n            profileHorizontalPosition: 50,\n            profileNameHorizontalPosition: 50,\n            profileNameStyle: 'typewriter'\n          };\n          setBannerPfpMetadata(defaultMetadata);\n          setCurrentAddress(address);\n          return defaultMetadata;\n        }\n        return null;\n      }\n\n      const metadata = await response.json();\n      console.log('MetadataContext: Received bannerpfp metadata:', metadata);\n      if (metadata) {\n        console.log('MetadataContext: Setting bannerpfp metadata:', metadata);\n        setBannerPfpMetadata(metadata);\n        setCurrentAddress(address);\n        return metadata;\n      }\n    } catch (error) {\n      console.error('Failed to load bannerpfp metadata:', error);\n      // Create a default metadata object on error\n      const defaultMetadata = {\n        profileName: address.substring(0, 8),\n        profileShape: 'circular',\n        profileHorizontalPosition: 50,\n        profileNameHorizontalPosition: 50,\n        profileNameStyle: 'typewriter'\n      };\n      setBannerPfpMetadata(defaultMetadata);\n      setCurrentAddress(address);\n      return defaultMetadata;\n    } finally {\n      // Remove this address from the fetching set\n      fetchingAddresses.delete(address);\n    }\n    return null;\n  };\n\n  // Function to clear metadata (useful when changing addresses)\n  const clearMetadata = () => {\n    setBannerMetadata(null);\n    setProfilePictureMetadata(null);\n    setBannerPfpMetadata(null);\n    setCurrentAddress(null);\n  };\n\n  return (\n    <MetadataContext.Provider\n      value={{\n        bannerMetadata,\n        profilePictureMetadata,\n        bannerPfpMetadata,\n        fetchBannerMetadata,\n        fetchProfilePictureMetadata,\n        fetchBannerPfpMetadata,\n        clearMetadata\n      }}\n    >\n      {children}\n    </MetadataContext.Provider>\n  );\n}\n\nexport function useMetadata() {\n  const context = useContext(MetadataContext);\n  if (context === undefined) {\n    throw new Error('useMetadata must be used within a MetadataProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAsBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,iDAAiD;IACjD,wCAAwC;IACxC,MAAM,sBAAsB,OAAO;QACjC,QAAQ,GAAG,CAAC;QACZ,iDAAiD;QACjD,MAAM,gBAAgB,MAAM,uBAAuB;QACnD,IAAI,iBAAiB,cAAc,aAAa,EAAE;YAChD,MAAM,mBAAmB;gBACvB,SAAS,cAAc,aAAa;gBACpC,OAAO,cAAc,WAAW,IAAI;gBACpC,UAAU;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBACvB,aAAa;YACf;YACA,kBAAkB;YAClB,OAAO;QACT;QACA,OAAO;IACT;IAEA,0DAA0D;IAC1D,wCAAwC;IACxC,MAAM,8BAA8B,OAAO,SAAiB;QAC1D,QAAQ,GAAG,CAAC;QACZ,0DAA0D;QAC1D,MAAM,gBAAgB,MAAM,uBAAuB;QACnD,IAAI,iBAAiB,cAAc,cAAc,EAAE;YACjD,MAAM,mBAAmB;gBACvB,SAAS,cAAc,cAAc;gBACrC,OAAO,cAAc,YAAY,IAAI;gBACrC,UAAU;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBACvB,aAAa;gBACb,OAAO,cAAc,YAAY,IAAI;YACvC;YACA,0BAA0B;YAC1B,OAAO;QACT;QACA,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,oBAAoB,IAAI;IAE9B,uCAAuC;IACvC,MAAM,yBAAyB,OAAO;QACpC,0DAA0D;QAC1D,IAAI,qBAAqB,mBAAmB,SAAS;YACnD,QAAQ,GAAG,CAAC,wDAAwD;YACpE,OAAO;QACT;QAEA,kEAAkE;QAClE,IAAI,kBAAkB,GAAG,CAAC,UAAU;YAClC,QAAQ,GAAG,CAAC,4DAA4D;YACxE,gGAAgG;YAChG,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,OAAO;QACT;QAEA,qCAAqC;QACrC,kBAAkB,GAAG,CAAC;QAEtB,IAAI;YACF,QAAQ,GAAG,CAAC,oDAAoD;YAChE,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,SAAS;YAExD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,2DAA2D;gBAC3D,uEAAuE;gBACvE,QAAQ,IAAI,CAAC,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBAE5F,yDAAyD;gBACzD,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,MAAM,kBAAkB;wBACtB,aAAa,QAAQ,SAAS,CAAC,GAAG;wBAClC,cAAc;wBACd,2BAA2B;wBAC3B,+BAA+B;wBAC/B,kBAAkB;oBACpB;oBACA,qBAAqB;oBACrB,kBAAkB;oBAClB,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,SAAS,IAAI;YACpC,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,IAAI,UAAU;gBACZ,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,qBAAqB;gBACrB,kBAAkB;gBAClB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,4CAA4C;YAC5C,MAAM,kBAAkB;gBACtB,aAAa,QAAQ,SAAS,CAAC,GAAG;gBAClC,cAAc;gBACd,2BAA2B;gBAC3B,+BAA+B;gBAC/B,kBAAkB;YACpB;YACA,qBAAqB;YACrB,kBAAkB;YAClB,OAAO;QACT,SAAU;YACR,4CAA4C;YAC5C,kBAAkB,MAAM,CAAC;QAC3B;QACA,OAAO;IACT;IAEA,8DAA8D;IAC9D,MAAM,gBAAgB;QACpB,kBAAkB;QAClB,0BAA0B;QAC1B,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/components/ThemeProvider.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { ThemeProvider as NextThemesProvider } from 'next-themes'\nimport { type ThemeProviderProps } from 'next-themes'\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/app/contexts/GridBgContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\n\ninterface GridBgContextType {\n  isGridBgDisabled: boolean;\n  setIsGridBgDisabled: (disabled: boolean) => void;\n}\n\nconst GridBgContext = createContext<GridBgContextType | undefined>(undefined);\n\nexport function GridBgProvider({ children }: { children: ReactNode }) {\n  const [isGridBgDisabled, setIsGridBgDisabled] = useState(false);\n\n  return (\n    <GridBgContext.Provider\n      value={{\n        isGridBgDisabled,\n        setIsGridBgDisabled\n      }}\n    >\n      {children}\n    </GridBgContext.Provider>\n  );\n}\n\nexport function useGridBg() {\n  const context = useContext(GridBgContext);\n  if (context === undefined) {\n    throw new Error('useGridBg must be used within a GridBgProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,8OAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/hooks/useWalletConnectionPersistence.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useAppKitAccount } from '@reown/appkit/react';\nimport { usePathname } from 'next/navigation';\n\n/**\n * Simplified hook to log wallet connection status for debugging\n * AppKit handles connection persistence automatically\n */\nexport function useWalletConnectionPersistence() {\n  const { isConnected, address } = useAppKitAccount();\n  const pathname = usePathname();\n\n  // Log connection status for debugging only\n  useEffect(() => {\n    console.log('[Wallet Connection] Path changed to:', pathname);\n    console.log('[Wallet Connection] isConnected:', isConnected);\n    console.log('[Wallet Connection] address:', address);\n  }, [pathname, isConnected, address]);\n\n  return { isConnected, address };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAAA;AACA;AAJA;;;;AAUO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,mBAAgB,AAAD;IAChD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,wCAAwC;QACpD,QAAQ,GAAG,CAAC,oCAAoC;QAChD,QAAQ,GAAG,CAAC,gCAAgC;IAC9C,GAAG;QAAC;QAAU;QAAa;KAAQ;IAEnC,OAAO;QAAE;QAAa;IAAQ;AAChC", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/components/Providers.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { WagmiProvider } from \"wagmi\";\nimport { config } from \"@/config\";\nimport React, { useState, useEffect } from \"react\";\nimport { MetadataProvider } from \"@/app/contexts/MetadataContext\";\nimport { ThemeProvider } from \"@/components/ThemeProvider\";\nimport { GridBgProvider } from \"@/app/contexts/GridBgContext\";\nimport { useWalletConnectionPersistence } from \"@/hooks/useWalletConnectionPersistence\";\nimport AppKitErrorBoundary from \"@/components/AppKitErrorBoundary\";\n\n// Create a client outside the component\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n      retry: 1,\n    },\n  },\n});\n\n// Inner component to use hooks after WagmiProvider is mounted\nfunction ProvidersContent({ children }: { children: React.ReactNode }) {\n  // Use the wallet connection persistence hook\n  useWalletConnectionPersistence();\n\n  return (\n    <MetadataProvider>\n      <GridBgProvider>\n        {children}\n      </GridBgProvider>\n    </MetadataProvider>\n  );\n}\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <WagmiProvider config={config}>\n      <QueryClientProvider client={queryClient}>\n        <ThemeProvider attribute=\"class\" defaultTheme=\"dark\" enableSystem={false} forcedTheme=\"dark\">\n          <ProvidersContent>\n            {children}\n          </ProvidersContent>\n        </ThemeProvider>\n      </QueryClientProvider>\n    </WagmiProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYA,wCAAwC;AACxC,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW,CAAC;IAClC,gBAAgB;QACd,SAAS;YACP,sBAAsB;YACtB,OAAO;QACT;IACF;AACF;AAEA,8DAA8D;AAC9D,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IACnE,6CAA6C;IAC7C,CAAA,GAAA,uIAAA,CAAA,iCAA8B,AAAD;IAE7B,qBACE,8OAAC,mIAAA,CAAA,mBAAgB;kBACf,cAAA,8OAAC,iIAAA,CAAA,iBAAc;sBACZ;;;;;;;;;;;AAIT;AAEe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,+IAAA,CAAA,gBAAa;QAAC,QAAQ,gHAAA,CAAA,SAAM;kBAC3B,cAAA,8OAAC,sLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,8OAAC,4HAAA,CAAA,gBAAa;gBAAC,WAAU;gBAAQ,cAAa;gBAAO,cAAc;gBAAO,aAAY;0BACpF,cAAA,8OAAC;8BACE;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}