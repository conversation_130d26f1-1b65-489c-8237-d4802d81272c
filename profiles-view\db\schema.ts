import { mysqlTable, varchar, text, longtext, timestamp, json, int, decimal, uniqueIndex, primaryKey } from "drizzle-orm/mysql-core";
import { randomUUID } from 'crypto';

export const systemSettings = mysqlTable("system_settings", {
  id: varchar("id", { length: 50 }).primaryKey(),
  value: json("value").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const web3Profile = mysqlTable("web3Profile", {
  address: varchar("address", { length: 255 }).primaryKey(),
  chain: varchar("chain", { length: 255 }).notNull(),
  name: varchar("name", { length: 255 }), // URL name for the profile
  // bio and compPosition fields removed - now stored in componentPositions table for profilePicture component
  theme: j<PERSON>("theme"), // Store theme preferences as JSON
  role: varchar("role", { length: 50 }).notNull().default("user"), // Role: admin or user
  status: varchar("status", { length: 50 }).notNull().default("new"), // Status: new, in-progress, pending, approved, deleted
  expiryDate: timestamp("expiry_date"), // Expiry date for the profile
  transactionHash: varchar("transaction_hash", { length: 255 }), // Transaction hash for blockchain verification
  referralCode: varchar("referral_code", { length: 8 }), // Referral code (w3txxxxx) - generated when approved
  referredBy: varchar("referred_by", { length: 8 }), // Referral code of who referred this user
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure referral code is unique if provided
    referralCodeIndex: uniqueIndex("referral_code_idx").on(table.referralCode),
  };
});

export const waitingList = mysqlTable("waitingList", {
  address: varchar("address", { length: 255 }).primaryKey(), // Removed foreign key constraint
  chain: varchar("chain", { length: 255 }).notNull(),
  xHandle: text("xHandle").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Store component positions for each user's profile
export const componentPositions = mysqlTable("componentPositions", {
  address: varchar("address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }),
  chain: varchar("chain", { length: 255 }).notNull(),
  componentType: varchar("component_type", { length: 50 }).notNull(), // e.g., "banner", "profilePicture", "socialLinks", etc.
  order: varchar("order", { length: 10 }).notNull(), // Order of components (1, 2, 3, etc.)
  hidden: varchar("hidden", { length: 1 }).notNull().default('N'), // Whether the component is hidden (Y/N)
  details: json("details"), // Store component-specific properties as JSON
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.address, table.componentType] }), // Composite primary key
  };
});

// Images table to store all images separately
export const componentImages = mysqlTable("componentImages", {
  id: varchar("id", { length: 36 }).primaryKey().notNull(), // UUID for the image
  address: varchar("address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }),
  componentType: varchar("component_type", { length: 50 }).notNull(), // e.g., "banner", "profilePicture", "hero"
  section: varchar("section", { length: 50 }).default("0"), // Section identifier (e.g., hero section index)
  imageData: longtext("image_data"), // Store base64 encoded image (LONGTEXT)
  scale: decimal("scale", { precision: 20, scale: 16 }).default("1"),
  positionX: int("position_x").default(0),
  positionY: int("position_y").default(0),
  naturalWidth: int("natural_width"),
  naturalHeight: int("natural_height"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Store profile likes
export const profileLikes = mysqlTable("profileLikes", {
  id: varchar("id", { length: 36 }).primaryKey().notNull(), // UUID for the like
  likerAddress: varchar("liker_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the user who liked
  likedAddress: varchar("liked_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the profile that was liked
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure a user can only like a profile once
    uniqueLike: uniqueIndex('unique_like_idx').on(table.likerAddress, table.likedAddress),
  };
});

// Store profile referrals
export const profileReferrals = mysqlTable("profileReferrals", {
  id: varchar("id", { length: 36 }).primaryKey().notNull(), // UUID for the referral
  referrerAddress: varchar("referrer_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the user who referred
  referredAddress: varchar("referred_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the user who was referred
  referralCode: varchar("referral_code", { length: 8 }).notNull(), // The referral code that was used
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure a user can only be referred once
    uniqueReferral: uniqueIndex('unique_referral_idx').on(table.referredAddress),
  };
});
