'use client';

import { useState, useEffect } from 'react';
import { Copy, Check } from 'lucide-react';

interface RenderSocialLinksProps {
  profileData: {
    address: string;
    chain: string;
    name: string;
    bio: string;
  };
  componentData: {
    address: string;
    chain: string;
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    fontColor?: string | null;
    socialLinks?: any;
  };
  showPositionLabel?: boolean;
}

export default function RenderSocialLinks({
  profileData,
  componentData,
  showPositionLabel = false
}: RenderSocialLinksProps) {
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [referralCount, setReferralCount] = useState<number>(0);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const fetchReferralData = async () => {
      try {
        const response = await fetch(`/api/referral/${profileData.address}`);
        if (response.ok) {
          const data = await response.json();
          setReferralCode(data.referralCode);
          setReferralCount(data.referralCount || 0);
        }
      } catch (error) {
        console.error('Error fetching referral data:', error);
      }
    };

    if (profileData.address) {
      fetchReferralData();
    }
  }, [profileData.address]);

  const copyReferralCode = async () => {
    if (referralCode) {
      try {
        await navigator.clipboard.writeText(referralCode);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy referral code:', error);
      }
    }
  };

  // Helper function to get social media icons
  const getSocialIcon = (platform: string) => {
    // Simple text-based icons for view-only mode
    const platformIcons: Record<string, string> = {
      twitter: '🐦',
      github: '🐙',
      linkedin: '💼',
      instagram: '📷',
      website: '🌐',
      email: '📧',
      youtube: '📺',
      twitch: '🎮',
      telegram: '✈️',
      discord: '💬',
      facebook: '📘',
      cro: '💎'
    };

    return platformIcons[platform.toLowerCase()] || '🔗';
  };

  // Format social links if they exist
  const formatSocialLinks = () => {
    if (!componentData.socialLinks) {
      return (
        <div className="my-1 text-center py-2">
          <p className="text-sm text-neutral-400">No social links added yet</p>
        </div>
      );
    }

    try {
      const links = typeof componentData.socialLinks === 'string'
        ? JSON.parse(componentData.socialLinks)
        : componentData.socialLinks;

      if (!links || Object.keys(links).length === 0) {
        return (
          <div className="my-1 text-center py-2">
            <p className="text-sm text-neutral-400">No social links added yet</p>
          </div>
        );
      }

      // Format links for display
      const formattedLinks = Object.entries(links)
        .filter(([_, url]) => url) // Filter out empty URLs
        .map(([platform, url]) => {
          const urlStr = String(url);
          const isCustom = platform.startsWith('custom');
          
          let customLabel = '';
          let finalUrl = urlStr;

          if (isCustom && urlStr.includes('|')) {
            const parts = urlStr.split('|');
            if (parts.length >= 2) {
              customLabel = parts[0].trim();
              finalUrl = parts.slice(1).join('|').trim();
            }
          }

          const displayName = isCustom ? (customLabel || 'Custom Link') : platform;
          const icon = getSocialIcon(platform);

          return {
            platform,
            url: finalUrl,
            displayName,
            icon
          };
        });

      if (formattedLinks.length === 0) {
        return (
          <div className="my-1 text-center py-2" style={{ color: componentData.fontColor || undefined }}>
            <p className="text-sm text-neutral-400">No social links added yet</p>
          </div>
        );
      }

      return (
        <div className="my-4 p-4" style={{ color: componentData.fontColor || undefined }}>
          <div className="flex flex-wrap justify-center gap-4">
            {formattedLinks.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 px-3 py-2 bg-neutral-800 hover:bg-neutral-700 rounded-lg transition-colors"
                style={{ color: componentData.fontColor || '#ffffff' }}
              >
                <span className="text-lg">{link.icon}</span>
                <span className="text-sm">{link.displayName}</span>
              </a>
            ))}
          </div>
          
          {/* Referral Code Section */}
          {referralCode && (
            <div className="mt-6 p-4 bg-neutral-800/50 rounded-lg">
              <div className="text-center">
                <p className="text-sm text-neutral-400 mb-2">Referral Code</p>
                <div className="flex items-center justify-center gap-2">
                  <code className="px-3 py-1 bg-neutral-700 rounded text-sm font-mono">
                    {referralCode}
                  </code>
                  <button
                    onClick={copyReferralCode}
                    className="p-1 hover:bg-neutral-600 rounded transition-colors"
                    title="Copy referral code"
                  >
                    {copied ? (
                      <Check className="w-4 h-4 text-green-400" />
                    ) : (
                      <Copy className="w-4 h-4 text-neutral-400" />
                    )}
                  </button>
                </div>
                {referralCount > 0 && (
                  <p className="text-xs text-neutral-500 mt-1">
                    {referralCount} referral{referralCount !== 1 ? 's' : ''}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      );
    } catch (error) {
      console.error('Error parsing social links:', error);
      return null;
    }
  };

  return (
    <div
      className="relative border border-neutral-800 w-full"
      style={{ backgroundColor: componentData.backgroundColor || 'transparent' }}
    >
      {formatSocialLinks()}

      {showPositionLabel && (
        <div className="absolute top-2 right-2 bg-blue-900/30 text-blue-400 px-2 py-1 rounded-full text-xs font-medium">
          Social Links
        </div>
      )}
    </div>
  );
}
