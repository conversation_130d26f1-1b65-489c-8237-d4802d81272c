'use client';

import { FloatingDock } from '@/components/ui/floating-dock';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { Copy, Check } from 'lucide-react';

interface RenderSocialLinksProps {
  profileData: {
    address: string;
    chain: string;
    name: string;
    bio: string;
  };
  componentData: {
    address: string;
    chain: string;
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    fontColor?: string | null;
    socialLinks?: any;
  };
  showPositionLabel?: boolean;
}

export default function RenderSocialLinks({
  profileData,
  componentData,
  showPositionLabel = false
}: RenderSocialLinksProps) {
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [referralCount, setReferralCount] = useState<number>(0);
  const [copied, setCopied] = useState(false);

  // Fetch user's referral code and stats
  useEffect(() => {
    const fetchReferralStats = async () => {
      try {
        const response = await fetch(`/api/referral/${profileData.address}`);
        if (response.ok) {
          const data = await response.json();
          setReferralCode(data.referralCode || null);
          setReferralCount(data.referralCount || 0);
        }
      } catch (error) {
        console.error('Failed to fetch referral stats:', error);
      }
    };

    fetchReferralStats();
  }, [profileData.address]);

  // Copy referral code to clipboard
  const copyReferralCode = async () => {
    if (referralCode) {
      try {
        await navigator.clipboard.writeText(referralCode);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy referral code:', error);
      }
    }
  };
  // Helper function to get social media icons
  const getSocialIcon = (platform: string) => {
    // Map platform names to file names
    const platformMap: Record<string, string> = {
      twitter: '/twitter.gif',
      github: '/github.gif',
      linkedin: '/link.gif',
      instagram: '/instagram.gif',
      website: '/www.gif',
      email: '/email.gif',
      youtube: '/yt.gif',
      twitch: '/twitch.gif',
      telegram: '/tg.gif',
      discord: '/discord.gif',
      facebook: '/fb.gif',
      cro: '/cro.gif'
    };

    // For custom platforms, use the link.gif icon
    const iconPath = platform.startsWith('custom') ? '/link.gif' : platformMap[platform.toLowerCase()];
    return (
      <Image
        src={iconPath}
        alt={platform}
        width={64}
        height={64}
        className="w-full h-full object-contain"
        style={{ background: 'transparent' }}
      />
    );
  };

  // Format social links if they exist
  const formatSocialLinks = () => {
    if (!componentData.socialLinks) {
      // Display a message when socialLinks is null or undefined
      return (
        <div className="my-1 text-center py-2">
          <p className="text-sm text-neutral-400">No social links added yet</p>
        </div>
      );
    }

    try {
      const links = typeof componentData.socialLinks === 'string'
        ? JSON.parse(componentData.socialLinks)
        : componentData.socialLinks;

      if (!links || Object.keys(links).length === 0) {
        // Display a message when links object is empty
        return (
          <div className="my-1 text-center py-2">
            <p className="text-sm text-neutral-400">No social links added yet</p>
          </div>
        );
      }

      // Define the desired order of platforms
      const platformOrder = ['twitter', 'discord', 'telegram', 'website', 'facebook', 'youtube', 'email', 'cro'];

      // Add custom platforms to the end of the order array
      const customPlatforms = Object.keys(links).filter(key => key.startsWith('custom'));
      platformOrder.push(...customPlatforms);

      // Format links for FloatingDock
      let formattedLinks = Object.entries(links)
        .filter(([_, url]) => url) // Filter out empty URLs
        .map(([platform, url]) => {
          // Convert url to string safely
          const urlStr = String(url);

          // Check if this is a custom link (platform starts with 'custom')
          const isCustom = platform.startsWith('custom');

          // For custom links, extract the custom label if it's in the format "customLabel|url"
          let customLabel = '';
          let finalUrl = urlStr;

          if (isCustom && urlStr.includes('|')) {
            const parts = urlStr.split('|');
            if (parts.length >= 2) {
              customLabel = parts[0].trim();
              finalUrl = parts.slice(1).join('|').trim();
            }
          }

          // Special case for CRO platform
          let title = '';
          if (platform.toLowerCase() === 'cro') {
            title = 'CRO Referral';
          } else {
            title = isCustom && customLabel ? customLabel : platform.charAt(0).toUpperCase() + platform.slice(1);
          }

          return {
            platform: platform.toLowerCase(),
            title: title,
            icon: getSocialIcon(platform),
            href: finalUrl.startsWith('http') ? finalUrl : `https://${finalUrl}`
          };
        });

      // Sort links according to the desired order
      formattedLinks.sort((a, b) => {
        const indexA = platformOrder.indexOf(a.platform);
        const indexB = platformOrder.indexOf(b.platform);

        // If both platforms are in the order array, sort by their position
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB;
        }

        // If only one platform is in the order array, prioritize it
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;

        // If neither platform is in the order array, maintain original order
        return 0;
      });

      if (formattedLinks.length === 0) {
        // Display a message when there are no social links with URLs
        return (
          <div className="my-1 text-center py-2" style={{ color: componentData.fontColor || undefined }}>
            <p className="text-sm text-neutral-400">No social links added yet</p>
          </div>
        );
      }

      return (
        <div className="my-1" style={{ color: componentData.fontColor || undefined }}>
          <FloatingDock
            items={formattedLinks}
            desktopClassName="justify-center py-1 sm:py-1 md:py-2 flex-wrap"
            backgroundColor={componentData.backgroundColor}
            fontColor={componentData.fontColor || undefined}
          />
        </div>
      );
    } catch (error) {
      console.error('Error parsing social links:', error);
      return null;
    }
  };

  return (
    <div
      className="relative border border-neutral-800 overflow-hidden w-full"
    >
      <div className="py-2 px-2 w-full" style={{ backgroundColor: componentData.backgroundColor }}>
        <div className="relative max-w-[800px] mx-auto">
          {formatSocialLinks()}

          {/* Display referral code if user has one */}
          {referralCode && (
            <div className="mt-4 flex justify-end">
              <div className="bg-transparent border border-neutral-700 rounded-lg px-3 py-2 flex flex-col gap-1" style={{ backgroundColor: componentData.backgroundColor }}>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-neutral-400">My Referral Code:</span>
                  <span className="text-sm font-mono text-white">{referralCode}</span>
                  <button
                    onClick={copyReferralCode}
                    className="p-1 hover:bg-neutral-700 rounded transition-colors"
                    title="Copy referral code"
                  >
                    {copied ? (
                      <Check className="h-3 w-3 text-green-400" />
                    ) : (
                      <Copy className="h-3 w-3 text-neutral-400" />
                    )}
                  </button>
                </div>
                <div className="text-xs text-neutral-500 text-right">
                  {referralCount} user{referralCount !== 1 ? 's' : ''} referred
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      {showPositionLabel && (
        <div className="absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
          Social Links
        </div>
      )}
    </div>
  );
}
