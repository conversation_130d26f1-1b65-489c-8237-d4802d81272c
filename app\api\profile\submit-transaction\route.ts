import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, profileReferrals } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { validateReferralCode } from '@/app/utils/referralUtils';
import { randomUUID } from 'crypto';

export async function POST(request: NextRequest): Promise<Response> {
  try {
    const { address, transactionHash, referralCode, validationMode } = await request.json();

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Validate based on validation mode
    if (validationMode === 'token') {
      // Token mode - require transaction hash
      if (!transactionHash) {
        return Response.json(
          { error: 'Transaction hash is required for token validation mode' },
          { status: 400 }
        );
      }
    } else {
      // Referral-only mode - require referral code
      if (!referralCode) {
        return Response.json(
          { error: 'Referral code is required for referral-only mode' },
          { status: 400 }
        );
      }
    }

    // Check if profile exists
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Validate referral code if provided
    let referrerAddress: string | undefined;
    if (referralCode) {
      const validation = await validateReferralCode(referralCode);
      if (!validation.isValid) {
        return Response.json(
          { error: 'Invalid referral code' },
          { status: 400 }
        );
      }
      referrerAddress = validation.referrerAddress;
    }

    // Prepare update data based on validation mode
    const updateData: any = {
      status: 'pending',
      updatedAt: new Date()
    };

    // Add transaction hash only in token mode
    if (validationMode === 'token' && transactionHash) {
      updateData.transactionHash = transactionHash;
    }

    // Add referral code if provided and valid
    if (referralCode && referrerAddress) {
      updateData.referredBy = referralCode;
    }

    // Update profile with transaction hash and set status to pending
    await db.update(web3Profile)
      .set(updateData)
      .where(eq(web3Profile.address, address));

    // Create referral record if referral code was used
    if (referralCode && referrerAddress) {
      try {
        await db.insert(profileReferrals).values({
          id: randomUUID(),
          referrerAddress,
          referredAddress: address,
          referralCode,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      } catch (error) {
        console.error('Failed to create referral record:', error);
        // Don't fail the transaction submission if referral record creation fails
      }
    }

    return Response.json({ success: true });
  } catch (error) {
    console.error('Failed to submit profile information:', error);
    return Response.json(
      { error: 'Failed to submit profile information' },
      { status: 500 }
    );
  }
}
