/**
 * Token validation utilities for checking wallet balances and requirements
 */

export interface TokenRequirements {
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  burnAmount: string;
}

/**
 * Check if token requirements are configured (non-zero values)
 */
export function hasTokenRequirements(requirements: TokenRequirements): boolean {
  const minHoldings = parseFloat(requirements.minimumHoldings || '0');
  const burnAmount = parseFloat(requirements.burnAmount || '0');
  
  // If either minimum holdings or burn amount is greater than 0, we have token requirements
  return minHoldings > 0 || burnAmount > 0;
}

/**
 * Check if only referral code is required (all token values are zero)
 */
export function isReferralOnlyMode(requirements: TokenRequirements): boolean {
  return !hasTokenRequirements(requirements);
}

/**
 * Get the validation mode based on token requirements
 */
export function getValidationMode(requirements: TokenRequirements): 'token' | 'referral' {
  return hasTokenRequirements(requirements) ? 'token' : 'referral';
}

/**
 * Check wallet token balance (placeholder for now - will need Web3 integration)
 * This function should be implemented with actual blockchain calls
 */
export async function checkWalletBalance(
  walletAddress: string,
  tokenAddress: string,
  chainId: string,
  minimumRequired: string
): Promise<{
  hasEnoughTokens: boolean;
  currentBalance: string;
  minimumRequired: string;
  error?: string;
}> {
  try {
    // TODO: Implement actual blockchain balance checking
    // For now, return a placeholder response
    console.log(`Checking balance for wallet ${walletAddress} on chain ${chainId}`);
    console.log(`Token address: ${tokenAddress}`);
    console.log(`Minimum required: ${minimumRequired}`);
    
    // Placeholder logic - in real implementation, this would:
    // 1. Connect to the appropriate blockchain RPC
    // 2. Call the token contract's balanceOf function
    // 3. Compare with minimum required amount
    
    return {
      hasEnoughTokens: false, // Default to false until implemented
      currentBalance: '0',
      minimumRequired,
      error: 'Balance checking not yet implemented - please submit transaction hash for manual verification'
    };
  } catch (error) {
    return {
      hasEnoughTokens: false,
      currentBalance: '0',
      minimumRequired,
      error: error instanceof Error ? error.message : 'Failed to check wallet balance'
    };
  }
}

/**
 * Format token amount for display
 */
export function formatTokenAmount(amount: string, decimals: number = 18): string {
  const num = parseFloat(amount);
  if (num === 0) return '0';
  if (num < 1) return num.toFixed(6);
  if (num < 1000) return num.toFixed(2);
  if (num < 1000000) return `${(num / 1000).toFixed(1)}K`;
  return `${(num / 1000000).toFixed(1)}M`;
}

/**
 * Validate referral code format
 */
export function isValidReferralCode(code: string): boolean {
  // Referral codes should be in format 'w3txxxxx' (8 characters total)
  const referralRegex = /^w3t[a-zA-Z0-9]{5}$/;
  return referralRegex.test(code);
}
