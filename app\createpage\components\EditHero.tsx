'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import ComponentVisibilityToggle from './ComponentVisibilityToggle';
import { StickyScrollRevealDemo } from '@/components/ui/sticky-scroll-reveal-demo';
import { Save, Loader2, Plus, Trash, Image as ImageIcon } from 'lucide-react';
import ColorPicker from './ColorPicker';
import FontColorPicker from './FontColorPicker';
import DecryptedText from '@/components/ui/DecryptedText';

interface HeroContent {
  title: string;
  description: string;
  contentType: 'color' | 'image';
  colorGradient?: string;
  imageUrl?: string;
  contentText?: string;
  textEffect?: 'none' | 'decrypted';
}

interface EditHeroProps {
  address: string;
  initialContent?: HeroContent[];
  hidden: string;
}

export default function EditHero({
  address,
  initialContent = [],
  hidden
}: EditHeroProps) {
  const [heroContent, setHeroContent] = useState<HeroContent[]>(initialContent.length > 0 ? initialContent : [
    {
      title: "My First Section",
      description: "This is my first section. Click edit to change this text.",
      contentType: 'color',
      colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))',
      contentText: "Featured Content",
      textEffect: 'decrypted'
    }
  ]);

  const [isHidden, setIsHidden] = useState(hidden === 'Y');
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState('transparent');
  const [fontColor, setFontColor] = useState<string | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [tempContent, setTempContent] = useState<HeroContent | null>(null);

  // Update isHidden state when hidden prop changes
  useEffect(() => {
    setIsHidden(hidden === 'Y');
  }, [hidden]);

  // Load hero content from API
  useEffect(() => {
    const loadHeroContent = async () => {
      try {
        const response = await fetch(`/api/hero/${address}`);
        if (response.ok) {
          const data = await response.json();
          if (data.heroContent && Array.isArray(data.heroContent) && data.heroContent.length > 0) {
            // Ensure all loaded content has textEffect field for backward compatibility
            const contentWithTextEffect = data.heroContent.map((item: HeroContent) => ({
              ...item,
              textEffect: item.textEffect || 'decrypted'
            }));
            setHeroContent(contentWithTextEffect);
          }
        }
      } catch (error) {
        console.error('Failed to load hero content:', error);
      }
    };

    if (address) {
      loadHeroContent();
    }
  }, [address]);

  // Convert heroContent to format needed for StickyScrollRevealDemo
  const formatContentForDemo = () => {
    return heroContent.map((item, index) => {

      // For image content, we'll create a special wrapper with background color
      // For color content, we'll use the gradient as before
      let bgClass = '';
      let customBackground = null;

      if (item.contentType === 'color') {
        if (item.colorGradient?.includes('cyan') && item.colorGradient?.includes('emerald')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--cyan-500),var(--emerald-500))]';
        } else if (item.colorGradient?.includes('orange') && item.colorGradient?.includes('yellow')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--orange-500),var(--yellow-500))]';
        } else if (item.colorGradient?.includes('pink') && item.colorGradient?.includes('purple')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--pink-500),var(--purple-500))]';
        } else if (item.colorGradient?.includes('blue') && item.colorGradient?.includes('indigo')) {
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--blue-500),var(--indigo-500))]';
        } else {
          // Default gradient
          bgClass = 'bg-[linear-gradient(to_bottom_right,var(--cyan-500),var(--emerald-500))]';
        }
      } else if (item.contentType === 'image') {
        // For image content, we'll override the background in the StickyScroll component
        customBackground = backgroundColor || 'transparent';
      }

      return {
        title: item.title,
        description: (item.textEffect === 'decrypted' || item.textEffect === undefined) ? (
          <DecryptedText
            text={item.description}
            animateOn="view"
            speed={50}
            maxIterations={15}
            revealDirection="start"
            sequential={true}
            className=""
            style={{ color: fontColor || '#ffffff' }}
          />
        ) : (
          <span style={{ color: fontColor || '#ffffff' }}>
            {item.description}
          </span>
        ),
        content: (
          item.contentType === 'image' && item.imageUrl ? (
            // Image content - completely separate div with no gradient classes
            <div
              key={`hero-content-image-${index}`}
              className="flex h-full w-full items-center justify-center text-white"
              style={{ backgroundColor: backgroundColor || 'transparent' }}
            >
              <img
                src={item.imageUrl}
                className="h-full w-full object-contain"
                alt={item.title}
              />
            </div>
          ) : (
            // Color content - with gradient background
            <div
              key={`hero-content-color-${index}`}
              className={`flex h-full w-full items-center justify-center text-white ${bgClass}`}
            >
              {item.contentText || item.title}
            </div>
          )
        ),
        // Add custom background for image content to override the StickyScroll gradient
        customBackground: item.contentType === 'image' ? customBackground : undefined
      };
    });
  };


  // Add a new section and save changes
  const handleAddSection = async () => {
    setIsSaving(true);
    try {
      const newSection: HeroContent = {
        title: `Section ${heroContent.length + 1}`,
        description: "Click edit to change this description.",
        contentType: 'color',
        colorGradient: 'linear-gradient(to bottom right, var(--orange-500), var(--yellow-500))',
        contentText: `Featured Content ${heroContent.length + 1}`,
        textEffect: 'decrypted'
      };

      const updatedContent = [...heroContent, newSection];
      setHeroContent(updatedContent);

      // Save to API
      const response = await fetch(`/api/hero/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          heroContent: updatedContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save changes');
      }

      toast.success('New section added successfully');
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to add section:', error);
      toast.error('Failed to add section: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Remove a section and save changes
  const handleRemoveSection = async (index: number) => {
    // Prevent deleting the last section
    if (heroContent.length <= 1) {
      toast.error('Cannot remove the last section. At least one section is required.');
      return;
    }

    setIsSaving(true);
    try {
      const updatedContent = [...heroContent];
      updatedContent.splice(index, 1);
      setHeroContent(updatedContent);

      // Save to API
      const response = await fetch(`/api/hero/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          heroContent: updatedContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save changes');
      }

      toast.success('Section removed successfully');
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to remove section:', error);
      toast.error('Failed to remove section: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Start editing a section
  const handleEditSection = (index: number) => {
    setEditingIndex(index);
    // Ensure textEffect field exists with default value for backward compatibility
    const sectionContent = {...heroContent[index]};
    if (!sectionContent.textEffect) {
      sectionContent.textEffect = 'decrypted';
    }
    setTempContent(sectionContent);
  };

  // Save edited section and save changes to API
  const handleSaveSection = async () => {
    if (editingIndex === null || !tempContent) return;

    setIsSaving(true);
    try {
      console.log('Saving section:', editingIndex, tempContent);

      // Create a deep copy of the content to avoid reference issues
      const updatedContent = JSON.parse(JSON.stringify(heroContent));
      updatedContent[editingIndex] = tempContent;

      console.log('Updated content:', updatedContent);

      setHeroContent(updatedContent);
      setEditingIndex(null);
      setTempContent(null);

      // Save to API
      const response = await fetch(`/api/hero/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          heroContent: updatedContent,
          backgroundColor,
          fontColor
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save changes');
      }

      toast.success('Section updated successfully');
      setHasUnsavedChanges(false);

      // Confirm the state was updated correctly (will be logged on next render)
      setTimeout(() => {
        console.log('Hero content after update:', heroContent);
      }, 0);
    } catch (error) {
      console.error('Failed to update section:', error);
      toast.error('Failed to update section: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingIndex(null);
    setTempContent(null);
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!tempContent) return;

    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image is too large. Maximum size is 5MB.');
      return;
    }

    console.log('Uploading image:', file.name, file.type, file.size);

    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result && tempContent) {
        const imageUrl = event.target.result as string;
        console.log('Image loaded, length:', imageUrl.length);

        // Update the tempContent with the image URL
        setTempContent({
          ...tempContent,
          contentType: 'image',
          imageUrl,
          // Remove colorGradient when switching to image
          colorGradient: undefined
        });

        console.log('TempContent updated with image');
      }
    };
    reader.onerror = (error) => {
      console.error('Error reading file:', error);
      toast.error('Failed to read image file');
    };
    reader.readAsDataURL(file);
  };

  // Save all changes to the API
  const handleSaveChanges = async () => {
    setIsSaving(true);
    try {
      // Log the content being saved
      console.log('Saving hero content:', JSON.stringify(heroContent, null, 2));

      const response = await fetch(`/api/hero/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          heroContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save changes');
      }

      const result = await response.json();
      console.log('Save result:', result);

      setHasUnsavedChanges(false);
      toast.success('Hero content saved successfully');
    } catch (error) {
      console.error('Failed to save hero content:', error);
      toast.error('Failed to save changes: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Toggle visibility is now handled by the ComponentVisibilityToggle component

  return (
    <div className="relative border border-neutral-800 overflow-visible mt-4 w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-0">

      {/* Visibility Toggle */}
      <div className="absolute top-2 right-2 z-10">
        <ComponentVisibilityToggle
          componentType="hero"
          address={address}
          initialHidden={hidden}
          onChange={(componentType, newHidden) => setIsHidden(newHidden === 'Y')}
          minimal={true}
        />
      </div>

      {/* Color Pickers */}
      <ColorPicker
        address={address}
        componentType="hero"
        onColorChange={setBackgroundColor}
      />

      <FontColorPicker
        address={address}
        componentType="hero"
        onColorChange={setFontColor}
      />

      <div className="p-0 sm:p-3 md:p-4 space-y-3 sm:space-y-4 md:space-y-5 mt-3 sm:mt-6 md:mt-8">

        {/* Preview */}
        <div className="border border-neutral-800 overflow-visible bg-transparent p-0 w-full mt-8 xs:mt-4 sm:mt-0">
          <StickyScrollRevealDemo content={formatContentForDemo()} fontColor={fontColor || undefined} backgroundColor={backgroundColor} />
        </div>

        {/* Section List */}
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Sections</h3>

          {heroContent.map((section, index) => (
            <div key={index} className="bg-neutral-800/50 p-3">
              {editingIndex === index ? (
                /* Edit mode */
                <div className="space-y-3">
                <div>
                  <Label htmlFor={`title-${index}`}>Title</Label>
                  <Input
                    id={`title-${index}`}
                    value={tempContent?.title || ''}
                    onChange={(e) => setTempContent(prev => prev ? {...prev, title: e.target.value} : null)}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor={`description-${index}`}>Description</Label>
                  <Textarea
                    id={`description-${index}`}
                    value={tempContent?.description || ''}
                    onChange={(e) => setTempContent(prev => prev ? {...prev, description: e.target.value} : null)}
                    className="mt-1"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor={`text-effect-${index}`}>Text Effect</Label>
                  <select
                    id={`text-effect-${index}`}
                    value={tempContent?.textEffect ?? 'decrypted'}
                    onChange={(e) => setTempContent(prev => prev ? {...prev, textEffect: e.target.value as 'none' | 'decrypted'} : null)}
                    className="w-full p-2 mt-1 bg-neutral-900 border border-neutral-700 rounded-md"
                  >
                    <option value="none">No Effect</option>
                    <option value="decrypted">Decrypted Effect</option>
                  </select>
                </div>

                <div>
                  <Label>Content Type</Label>
                  <div className="flex space-x-4 mt-1">
                    <Button
                      variant={tempContent?.contentType === 'color' ? 'default' : 'outline'}
                      onClick={() => setTempContent(prev => prev ? {...prev, contentType: 'color'} : null)}
                      size="sm"
                    >
                      Color
                    </Button>
                    <Button
                      variant={tempContent?.contentType === 'image' ? 'default' : 'outline'}
                      onClick={() => setTempContent(prev => prev ? {...prev, contentType: 'image'} : null)}
                      size="sm"
                    >
                      Image
                    </Button>
                  </div>
                </div>

                {tempContent?.contentType === 'color' && (
                  <>
                    <div>
                      <Label htmlFor={`gradient-${index}`}>Color Gradient</Label>
                      <select
                        id={`gradient-${index}`}
                        value={tempContent.colorGradient}
                        onChange={(e) => setTempContent(prev => prev ? {...prev, colorGradient: e.target.value} : null)}
                        className="w-full p-2 mt-1 bg-neutral-900 border border-neutral-700 rounded-md"
                      >
                        <option value="linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))">Cyan to Emerald</option>
                        <option value="linear-gradient(to bottom right, var(--orange-500), var(--yellow-500))">Orange to Yellow</option>
                        <option value="linear-gradient(to bottom right, var(--pink-500), var(--purple-500))">Pink to Purple</option>
                        <option value="linear-gradient(to bottom right, var(--blue-500), var(--indigo-500))">Blue to Indigo</option>
                      </select>
                    </div>
                    <div className="mt-3">
                      <Label htmlFor={`content-text-${index}`}>Content Text</Label>
                      <Input
                        id={`content-text-${index}`}
                        value={tempContent?.contentText || ''}
                        onChange={(e) => setTempContent(prev => prev ? {...prev, contentText: e.target.value} : null)}
                        className="mt-1"
                        placeholder="Text to display in the content area"
                      />
                    </div>
                  </>
                )}

                {tempContent?.contentType === 'image' && (
                  <div>
                    <Label htmlFor={`image-${index}`}>Image</Label>
                    <div className="mt-1 flex items-center space-x-4">
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById(`image-upload-${index}`)?.click()}
                        className="flex items-center space-x-2"
                      >
                        <ImageIcon className="h-4 w-4" />
                        <span>Upload Image</span>
                      </Button>
                      <input
                        type="file"
                        id={`image-upload-${index}`}
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      {tempContent.imageUrl && (
                        <div className="h-10 w-10 relative overflow-hidden rounded-md">
                          <img
                            src={tempContent.imageUrl}
                            alt="Preview"
                            className="h-full w-full object-contain"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-end space-x-2 mt-2">
                  <Button variant="outline" onClick={handleCancelEdit}>
                    Cancel
                  </Button>
                  <Button onClick={handleSaveSection}>
                    Save Section
                  </Button>
                </div>
              </div>
            ) : (
                /* View mode */
                <div className="flex items-start justify-between">
                  <div className="flex-1 pr-2">
                    <h4 className="text-white font-medium">{section.title}</h4>
                    <p className="text-neutral-400 text-sm mt-1 line-clamp-2">{section.description}</p>
                    {section.contentType === 'color' && section.contentText && (
                      <p className="text-blue-400 text-xs mt-1">Content: {section.contentText}</p>
                    )}
                    <p className="text-purple-400 text-xs mt-1">
                      Text Effect: {section.textEffect === 'none' ? 'No Effect' : 'Decrypted Effect'}
                    </p>
                  </div>
                  <div className="flex space-x-2 ml-2 flex-shrink-0">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditSection(index)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleRemoveSection(index)}
                      disabled={heroContent.length <= 1}
                      title={heroContent.length <= 1 ? 'Cannot remove the last section' : 'Remove section'}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
            )}
          </div>
        ))}

          <Button
            variant="outline"
            onClick={handleAddSection}
            disabled={isSaving}
            className="w-full flex items-center justify-center space-x-2 mt-2"
            size="sm"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Adding...</span>
              </>
            ) : (
              <>
                <Plus className="h-4 w-4" />
                <span>Add Section</span>
              </>
            )}
          </Button>
        </div>


      </div>
    </div>
  );
}
