/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8cebb05c8e82\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4Y2ViYjA1YzhlODJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./components/Providers.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Web3Socials Profiles - View Only\",\n    description: \"View Web3 profiles\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased overflow-x-hidden`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen flex flex-col items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-4xl mx-auto px-4 sm:px-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-white mb-6\",\n                    children: \"Web3Socials Profiles\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl text-neutral-300 mb-8\",\n                    children: \"View-Only Profile Viewer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-neutral-400 mb-8\",\n                    children: [\n                        \"This is a view-only version of Web3Socials profiles. To view a profile, navigate to /\",\n                        '{name}',\n                        \" where \",\n                        '{name}',\n                        \" is the profile name.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-neutral-800 rounded-lg p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-white mb-4\",\n                            children: \"How to Use\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left space-y-2 text-neutral-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"• Navigate to \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-neutral-700 px-2 py-1 rounded\",\n                                            children: \"/profilename\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 30\n                                        }, this),\n                                        \" to view a specific profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"• Example: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-neutral-700 px-2 py-1 rounded\",\n                                            children: \"/john\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 27\n                                        }, this),\n                                        \" to view John's profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"• Only approved profiles will be displayed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"• This viewer shares the same database as the main Web3Socials application\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-neutral-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Powered by Web3Socials • View-Only Mode\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\components\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(rsc)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/contexts/GridBgContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/GridBgContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GridBgProvider: () => (/* binding */ GridBgProvider),\n/* harmony export */   useGridBg: () => (/* binding */ useGridBg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GridBgProvider,useGridBg auto */ \n\nconst GridBgContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GridBgProvider({ children }) {\n    const [isGridBgDisabled, setIsGridBgDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridBgContext.Provider, {\n        value: {\n            isGridBgDisabled,\n            setIsGridBgDisabled\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\GridBgContext.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction useGridBg() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GridBgContext);\n    if (context === undefined) {\n        throw new Error('useGridBg must be used within a GridBgProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dHMvR3JpZEJnQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4RTtBQU85RSxNQUFNSSw4QkFBZ0JILG9EQUFhQSxDQUFnQ0k7QUFFNUQsU0FBU0MsZUFBZSxFQUFFQyxRQUFRLEVBQTJCO0lBQ2xFLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR04sK0NBQVFBLENBQUM7SUFFekQscUJBQ0UsOERBQUNDLGNBQWNNLFFBQVE7UUFDckJDLE9BQU87WUFDTEg7WUFDQUM7UUFDRjtrQkFFQ0Y7Ozs7OztBQUdQO0FBRU8sU0FBU0s7SUFDZCxNQUFNQyxVQUFVWCxpREFBVUEsQ0FBQ0U7SUFDM0IsSUFBSVMsWUFBWVIsV0FBVztRQUN6QixNQUFNLElBQUlTLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxhcHBcXGNvbnRleHRzXFxHcmlkQmdDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgR3JpZEJnQ29udGV4dFR5cGUge1xuICBpc0dyaWRCZ0Rpc2FibGVkOiBib29sZWFuO1xuICBzZXRJc0dyaWRCZ0Rpc2FibGVkOiAoZGlzYWJsZWQ6IGJvb2xlYW4pID0+IHZvaWQ7XG59XG5cbmNvbnN0IEdyaWRCZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PEdyaWRCZ0NvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgZnVuY3Rpb24gR3JpZEJnUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xuICBjb25zdCBbaXNHcmlkQmdEaXNhYmxlZCwgc2V0SXNHcmlkQmdEaXNhYmxlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgcmV0dXJuIChcbiAgICA8R3JpZEJnQ29udGV4dC5Qcm92aWRlclxuICAgICAgdmFsdWU9e3tcbiAgICAgICAgaXNHcmlkQmdEaXNhYmxlZCxcbiAgICAgICAgc2V0SXNHcmlkQmdEaXNhYmxlZFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9HcmlkQmdDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlR3JpZEJnKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChHcmlkQmdDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlR3JpZEJnIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBHcmlkQmdQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIkdyaWRCZ0NvbnRleHQiLCJ1bmRlZmluZWQiLCJHcmlkQmdQcm92aWRlciIsImNoaWxkcmVuIiwiaXNHcmlkQmdEaXNhYmxlZCIsInNldElzR3JpZEJnRGlzYWJsZWQiLCJQcm92aWRlciIsInZhbHVlIiwidXNlR3JpZEJnIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/GridBgContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/MetadataContext.tsx":
/*!******************************************!*\
  !*** ./app/contexts/MetadataContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetadataProvider: () => (/* binding */ MetadataProvider),\n/* harmony export */   useMetadata: () => (/* binding */ useMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MetadataProvider,useMetadata auto */ \n\nconst MetadataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MetadataProvider({ children }) {\n    const [bannerMetadata, setBannerMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profilePictureMetadata, setProfilePictureMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAddress, setCurrentAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to fetch banner metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchBannerMetadata = async (address)=>{\n        console.log('Banner component is deprecated, using bannerpfp instead');\n        // Try to get banner data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.bannerBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.bannerBlobUrl,\n                scale: bannerpfpData.bannerScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined\n            };\n            setBannerMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Function to fetch profile picture metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchProfilePictureMetadata = async (address, compPosition)=>{\n        console.log('ProfilePicture component is deprecated, using bannerpfp instead');\n        // Try to get profile picture data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.profileBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.profileBlobUrl,\n                scale: bannerpfpData.profileScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined,\n                shape: bannerpfpData.profileShape || 'circular'\n            };\n            setProfilePictureMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Track in-progress fetches to prevent duplicate calls\n    const fetchingAddresses = new Set();\n    // Function to fetch bannerpfp metadata\n    const fetchBannerPfpMetadata = async (address)=>{\n        // If we already have metadata for this address, return it\n        if (bannerPfpMetadata && currentAddress === address) {\n            console.log('MetadataContext: Using cached bannerpfp metadata for', address);\n            return bannerPfpMetadata;\n        }\n        // If we're already fetching this address, wait for it to complete\n        if (fetchingAddresses.has(address)) {\n            console.log('MetadataContext: Already fetching bannerpfp metadata for', address);\n            // Wait a bit and return the current metadata (which should be updated by the in-progress fetch)\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            return bannerPfpMetadata;\n        }\n        // Mark this address as being fetched\n        fetchingAddresses.add(address);\n        try {\n            console.log('MetadataContext: Fetching bannerpfp metadata for', address);\n            const response = await fetch(`/api/bannerpfp/${address}`);\n            if (!response.ok) {\n                // If the response is not OK, log the error but don't throw\n                // This allows the UI to continue rendering even if metadata is missing\n                console.warn(`Failed to fetch bannerpfp metadata: ${response.status} ${response.statusText}`);\n                // For 404 errors, we'll create a default metadata object\n                if (response.status === 404) {\n                    console.log('Creating default bannerpfp metadata since none exists');\n                    const defaultMetadata = {\n                        profileName: address.substring(0, 8),\n                        profileShape: 'circular',\n                        profileHorizontalPosition: 50,\n                        profileNameHorizontalPosition: 50,\n                        profileNameStyle: 'typewriter'\n                    };\n                    setBannerPfpMetadata(defaultMetadata);\n                    setCurrentAddress(address);\n                    return defaultMetadata;\n                }\n                return null;\n            }\n            const metadata = await response.json();\n            console.log('MetadataContext: Received bannerpfp metadata:', metadata);\n            if (metadata) {\n                console.log('MetadataContext: Setting bannerpfp metadata:', metadata);\n                setBannerPfpMetadata(metadata);\n                setCurrentAddress(address);\n                return metadata;\n            }\n        } catch (error) {\n            console.error('Failed to load bannerpfp metadata:', error);\n            // Create a default metadata object on error\n            const defaultMetadata = {\n                profileName: address.substring(0, 8),\n                profileShape: 'circular',\n                profileHorizontalPosition: 50,\n                profileNameHorizontalPosition: 50,\n                profileNameStyle: 'typewriter'\n            };\n            setBannerPfpMetadata(defaultMetadata);\n            setCurrentAddress(address);\n            return defaultMetadata;\n        } finally{\n            // Remove this address from the fetching set\n            fetchingAddresses.delete(address);\n        }\n        return null;\n    };\n    // Function to clear metadata (useful when changing addresses)\n    const clearMetadata = ()=>{\n        setBannerMetadata(null);\n        setProfilePictureMetadata(null);\n        setBannerPfpMetadata(null);\n        setCurrentAddress(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetadataContext.Provider, {\n        value: {\n            bannerMetadata,\n            profilePictureMetadata,\n            bannerPfpMetadata,\n            fetchBannerMetadata,\n            fetchProfilePictureMetadata,\n            fetchBannerPfpMetadata,\n            clearMetadata\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\MetadataContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\nfunction useMetadata() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MetadataContext);\n    if (context === undefined) {\n        throw new Error('useMetadata must be used within a MetadataProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/MetadataContext.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(ssr)/./config/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(ssr)/./components/ThemeProvider.tsx\");\n/* harmony import */ var _app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/contexts/GridBgContext */ \"(ssr)/./app/contexts/GridBgContext.tsx\");\n/* harmony import */ var _hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useWalletConnectionPersistence */ \"(ssr)/./hooks/useWalletConnectionPersistence.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Create a client outside the component\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1\n        }\n    }\n});\n// Inner component to use hooks after WagmiProvider is mounted\nfunction ProvidersContent({ children }) {\n    // Use the wallet connection persistence hook\n    (0,_hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__.useWalletConnectionPersistence)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__.MetadataProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__.GridBgProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Providers.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Providers.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_8__.WagmiProvider, {\n        config: _config__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: false,\n                forcedTheme: \"dark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProvidersContent, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ThemeProvider.tsx":
/*!**************************************!*\
  !*** ./components/ThemeProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxjb21wb25lbnRzXFxUaGVtZVByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gJ25leHQtdGhlbWVzJ1xuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./config/index.tsx":
/*!**************************!*\
  !*** ./config/index.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   networks: () => (/* binding */ networks),\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit-adapter-solana */ \"(ssr)/./node_modules/@reown/appkit-adapter-solana/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n/* __next_internal_client_entry_do_not_use__ projectId,metadata,networks,wagmiAdapter,config auto */ \n\n\n\nconst projectId = \"63cb69e4a11f991fe106897d3eede1ed\";\nif (!projectId) {\n    throw new Error(\"Project ID is not defined\");\n}\n// Validate project ID format\nif (projectId.length !== 32) {\n    console.warn('Project ID may be invalid - should be 32 characters');\n}\nconst metadata = {\n    name: 'Web3Socials',\n    description: 'Web3 Social Platform',\n    url:  false ? 0 : 'https://web3socials.fun',\n    icons: [\n        'https://avatars.githubusercontent.com/u/179229932'\n    ]\n};\n// Temporarily reduce networks to test 403 error - you can add more back later\nconst networks = [\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronos,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.mainnet,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.arbitrum,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.sepolia,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.solana,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronoszkEVM\n];\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    ssr: true,\n    projectId,\n    networks\n});\nconst solanaWeb3JsAdapter = new _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__.SolanaAdapter();\nconst generalConfig = {\n    projectId,\n    networks,\n    metadata,\n    themeMode: 'dark',\n    themeVariables: {\n        '--w3m-accent': '#000000'\n    }\n};\n// Prevent ethereum object conflicts\nif (false) {}\n(0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter,\n        solanaWeb3JsAdapter\n    ],\n    ...generalConfig,\n    features: {\n        swaps: false,\n        onramp: false,\n        email: true,\n        socials: false,\n        history: false,\n        analytics: false,\n        allWallets: true,\n        send: false\n    },\n    // Disable features that might cause network requests\n    featuredWalletIds: []\n});\nconst config = wagmiAdapter.wagmiConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./config/index.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useWalletConnectionPersistence.ts":
/*!*************************************************!*\
  !*** ./hooks/useWalletConnectionPersistence.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWalletConnectionPersistence: () => (/* binding */ useWalletConnectionPersistence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useWalletConnectionPersistence auto */ \n\n\n/**\n * Simplified hook to log wallet connection status for debugging\n * AppKit handles connection persistence automatically\n */ function useWalletConnectionPersistence() {\n    const { isConnected, address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Log connection status for debugging only\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWalletConnectionPersistence.useEffect\": ()=>{\n            console.log('[Wallet Connection] Path changed to:', pathname);\n            console.log('[Wallet Connection] isConnected:', isConnected);\n            console.log('[Wallet Connection] address:', address);\n        }\n    }[\"useWalletConnectionPersistence.useEffect\"], [\n        pathname,\n        isConnected,\n        address\n    ]);\n    return {\n        isConnected,\n        address\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ob29rcy91c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7b0ZBRWtDO0FBQ3FCO0FBQ1Q7QUFFOUM7OztDQUdDLEdBQ00sU0FBU0c7SUFDZCxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdKLHFFQUFnQkE7SUFDakQsTUFBTUssV0FBV0osNERBQVdBO0lBRTVCLDJDQUEyQztJQUMzQ0YsZ0RBQVNBO29EQUFDO1lBQ1JPLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NGO1lBQ3BEQyxRQUFRQyxHQUFHLENBQUMsb0NBQW9DSjtZQUNoREcsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0g7UUFDOUM7bURBQUc7UUFBQ0M7UUFBVUY7UUFBYUM7S0FBUTtJQUVuQyxPQUFPO1FBQUVEO1FBQWFDO0lBQVE7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGhvb2tzXFx1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBcHBLaXRBY2NvdW50IH0gZnJvbSAnQHJlb3duL2FwcGtpdC9yZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbi8qKlxuICogU2ltcGxpZmllZCBob29rIHRvIGxvZyB3YWxsZXQgY29ubmVjdGlvbiBzdGF0dXMgZm9yIGRlYnVnZ2luZ1xuICogQXBwS2l0IGhhbmRsZXMgY29ubmVjdGlvbiBwZXJzaXN0ZW5jZSBhdXRvbWF0aWNhbGx5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UoKSB7XG4gIGNvbnN0IHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfSA9IHVzZUFwcEtpdEFjY291bnQoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIC8vIExvZyBjb25uZWN0aW9uIHN0YXR1cyBmb3IgZGVidWdnaW5nIG9ubHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBQYXRoIGNoYW5nZWQgdG86JywgcGF0aG5hbWUpO1xuICAgIGNvbnNvbGUubG9nKCdbV2FsbGV0IENvbm5lY3Rpb25dIGlzQ29ubmVjdGVkOicsIGlzQ29ubmVjdGVkKTtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBhZGRyZXNzOicsIGFkZHJlc3MpO1xuICB9LCBbcGF0aG5hbWUsIGlzQ29ubmVjdGVkLCBhZGRyZXNzXSk7XG5cbiAgcmV0dXJuIHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBcHBLaXRBY2NvdW50IiwidXNlUGF0aG5hbWUiLCJ1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UiLCJpc0Nvbm5lY3RlZCIsImFkZHJlc3MiLCJwYXRobmFtZSIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useWalletConnectionPersistence.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(ssr)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNQcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDV2ViUGFnZXMlNUMlNUNXZWIzU29jaWFscyU1QyU1Q3Byb2ZpbGVzLXZpZXclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDV2ViUGFnZXMlNUMlNUNXZWIzU29jaWFscyU1QyU1Q3Byb2ZpbGVzLXZpZXclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0X01vbm8lNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1nZWlzdC1tb25vJTVDJTIyJTJDJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyZ2Vpc3RNb25vJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q3Nvbm5lciU1QyU1Q2Rpc3QlNUMlNUNpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBcUk7QUFDckk7QUFDQSxvTEFBaUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxXZWJQYWdlc1xcXFxXZWIzU29jaWFsc1xcXFxwcm9maWxlcy12aWV3XFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcbm9kZV9tb2R1bGVzXFxcXHNvbm5lclxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pino-pretty":
/*!******************************!*\
  !*** external "pino-pretty" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("pino-pretty");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/lit-html","vendor-chunks/@lit","vendor-chunks/lit","vendor-chunks/next","vendor-chunks/viem","vendor-chunks/@walletconnect","vendor-chunks/@noble","vendor-chunks/@wagmi","vendor-chunks/ox","vendor-chunks/abitype","vendor-chunks/@solana","vendor-chunks/tr46","vendor-chunks/rpc-websockets","vendor-chunks/ws","vendor-chunks/bn.js","vendor-chunks/sonner","vendor-chunks/@tanstack","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/valtio","vendor-chunks/superstruct","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/big.js","vendor-chunks/borsh","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/text-encoding-utf-8","vendor-chunks/zustand","vendor-chunks/uuid","vendor-chunks/dayjs","vendor-chunks/eventemitter3","vendor-chunks/lit-element","vendor-chunks/detect-browser","vendor-chunks/jayson","vendor-chunks/@wallet-standard","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/quick-format-unescaped","vendor-chunks/proxy-compare","vendor-chunks/mipd","vendor-chunks/@swc","vendor-chunks/destr","vendor-chunks/derive-valtio","vendor-chunks/utf-8-validate","vendor-chunks/safe-buffer","vendor-chunks/wagmi","vendor-chunks/atomic-sleep","vendor-chunks/bufferutil","vendor-chunks/bs58"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();