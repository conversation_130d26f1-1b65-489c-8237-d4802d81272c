"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_twitch_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   twitchSvg: () => (/* binding */ twitchSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst twitchSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#5A3E85\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M18.22 25.7 20 23.91h3.34l2.1-2.1v-6.68H15.4v8.78h2.82v1.77Zm3.87-8.16h1.25v3.66H22.1v-3.66Zm-3.34 0H20v3.66h-1.25v-3.66ZM20 7.9a12 12 0 1 0 0 24 12 12 0 0 0 0-24Zm6.69 14.56-3.66 3.66h-2.72l-1.77 1.78h-1.88V26.1H13.3v-9.82l.94-2.4H26.7v8.56Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=twitch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3R3aXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixrQkFBa0Isd0NBQUc7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXEByZW93blxcYXBwa2l0LXVpXFxkaXN0XFxlc21cXHNyY1xcYXNzZXRzXFxzdmdcXHR3aXRjaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IHR3aXRjaFN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgNDAgNDBcIj5cbiAgPGcgY2xpcC1wYXRoPVwidXJsKCNhKVwiPlxuICAgIDxnIGNsaXAtcGF0aD1cInVybCgjYilcIj5cbiAgICAgIDxjaXJjbGUgY3g9XCIyMFwiIGN5PVwiMTkuODlcIiByPVwiMjBcIiBmaWxsPVwiIzVBM0U4NVwiIC8+XG4gICAgICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2MpXCI+XG4gICAgICAgIDxwYXRoXG4gICAgICAgICAgZmlsbD1cIiNmZmZcIlxuICAgICAgICAgIGQ9XCJNMTguMjIgMjUuNyAyMCAyMy45MWgzLjM0bDIuMS0yLjF2LTYuNjhIMTUuNHY4Ljc4aDIuODJ2MS43N1ptMy44Ny04LjE2aDEuMjV2My42NkgyMi4xdi0zLjY2Wm0tMy4zNCAwSDIwdjMuNjZoLTEuMjV2LTMuNjZaTTIwIDcuOWExMiAxMiAwIDEgMCAwIDI0IDEyIDEyIDAgMCAwIDAtMjRabTYuNjkgMTQuNTYtMy42NiAzLjY2aC0yLjcybC0xLjc3IDEuNzhoLTEuODhWMjYuMUgxMy4zdi05LjgybC45NC0yLjRIMjYuN3Y4LjU2WlwiXG4gICAgICAgIC8+XG4gICAgICA8L2c+XG4gICAgPC9nPlxuICA8L2c+XG4gIDxkZWZzPlxuICAgIDxjbGlwUGF0aCBpZD1cImFcIj48cmVjdCB3aWR0aD1cIjQwXCIgaGVpZ2h0PVwiNDBcIiBmaWxsPVwiI2ZmZlwiIHJ4PVwiMjBcIiAvPjwvY2xpcFBhdGg+XG4gICAgPGNsaXBQYXRoIGlkPVwiYlwiPjxwYXRoIGZpbGw9XCIjZmZmXCIgZD1cIk0wIDBoNDB2NDBIMHpcIiAvPjwvY2xpcFBhdGg+XG4gICAgPGNsaXBQYXRoIGlkPVwiY1wiPjxwYXRoIGZpbGw9XCIjZmZmXCIgZD1cIk04IDcuODloMjR2MjRIOHpcIiAvPjwvY2xpcFBhdGg+XG4gIDwvZGVmcz5cbjwvc3ZnPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10d2l0Y2guanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js\n"));

/***/ })

}]);