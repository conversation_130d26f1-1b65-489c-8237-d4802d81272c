"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-block-tracker";
exports.ids = ["vendor-chunks/eth-block-tracker"];
exports.modules = {

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js":
/*!*****************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/BaseBlockTracker.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseBlockTracker = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\"));\nconst sec = 1000;\nconst calculateSum = (accumulator, currentValue) => accumulator + currentValue;\nconst blockTrackerEvents = ['sync', 'latest'];\nclass BaseBlockTracker extends safe_event_emitter_1.default {\n    constructor(opts) {\n        super();\n        // config\n        this._blockResetDuration = opts.blockResetDuration || 20 * sec;\n        this._usePastBlocks = opts.usePastBlocks || false;\n        // state\n        this._currentBlock = null;\n        this._isRunning = false;\n        // bind functions for internal use\n        this._onNewListener = this._onNewListener.bind(this);\n        this._onRemoveListener = this._onRemoveListener.bind(this);\n        this._resetCurrentBlock = this._resetCurrentBlock.bind(this);\n        // listen for handler changes\n        this._setupInternalEvents();\n    }\n    async destroy() {\n        this._cancelBlockResetTimeout();\n        await this._maybeEnd();\n        super.removeAllListeners();\n    }\n    isRunning() {\n        return this._isRunning;\n    }\n    getCurrentBlock() {\n        return this._currentBlock;\n    }\n    async getLatestBlock() {\n        // return if available\n        if (this._currentBlock) {\n            return this._currentBlock;\n        }\n        // wait for a new latest block\n        const latestBlock = await new Promise((resolve) => this.once('latest', resolve));\n        // return newly set current block\n        return latestBlock;\n    }\n    // dont allow module consumer to remove our internal event listeners\n    removeAllListeners(eventName) {\n        // perform default behavior, preserve fn arity\n        if (eventName) {\n            super.removeAllListeners(eventName);\n        }\n        else {\n            super.removeAllListeners();\n        }\n        // re-add internal events\n        this._setupInternalEvents();\n        // trigger stop check just in case\n        this._onRemoveListener();\n        return this;\n    }\n    _setupInternalEvents() {\n        // first remove listeners for idempotence\n        this.removeListener('newListener', this._onNewListener);\n        this.removeListener('removeListener', this._onRemoveListener);\n        // then add them\n        this.on('newListener', this._onNewListener);\n        this.on('removeListener', this._onRemoveListener);\n    }\n    _onNewListener(eventName) {\n        // `newListener` is called *before* the listener is added\n        if (blockTrackerEvents.includes(eventName)) {\n            this._maybeStart();\n        }\n    }\n    _onRemoveListener() {\n        // `removeListener` is called *after* the listener is removed\n        if (this._getBlockTrackerEventCount() > 0) {\n            return;\n        }\n        this._maybeEnd();\n    }\n    async _maybeStart() {\n        if (this._isRunning) {\n            return;\n        }\n        this._isRunning = true;\n        // cancel setting latest block to stale\n        this._cancelBlockResetTimeout();\n        await this._start();\n        this.emit('_started');\n    }\n    async _maybeEnd() {\n        if (!this._isRunning) {\n            return;\n        }\n        this._isRunning = false;\n        this._setupBlockResetTimeout();\n        await this._end();\n        this.emit('_ended');\n    }\n    _getBlockTrackerEventCount() {\n        return blockTrackerEvents\n            .map((eventName) => this.listenerCount(eventName))\n            .reduce(calculateSum);\n    }\n    _shouldUseNewBlock(newBlock) {\n        const currentBlock = this._currentBlock;\n        if (!currentBlock) {\n            return true;\n        }\n        const newBlockInt = hexToInt(newBlock);\n        const currentBlockInt = hexToInt(currentBlock);\n        return ((this._usePastBlocks && newBlockInt < currentBlockInt) ||\n            newBlockInt > currentBlockInt);\n    }\n    _newPotentialLatest(newBlock) {\n        if (!this._shouldUseNewBlock(newBlock)) {\n            return;\n        }\n        this._setCurrentBlock(newBlock);\n    }\n    _setCurrentBlock(newBlock) {\n        const oldBlock = this._currentBlock;\n        this._currentBlock = newBlock;\n        this.emit('latest', newBlock);\n        this.emit('sync', { oldBlock, newBlock });\n    }\n    _setupBlockResetTimeout() {\n        // clear any existing timeout\n        this._cancelBlockResetTimeout();\n        // clear latest block when stale\n        this._blockResetTimeout = setTimeout(this._resetCurrentBlock, this._blockResetDuration);\n        // nodejs - dont hold process open\n        if (this._blockResetTimeout.unref) {\n            this._blockResetTimeout.unref();\n        }\n    }\n    _cancelBlockResetTimeout() {\n        if (this._blockResetTimeout) {\n            clearTimeout(this._blockResetTimeout);\n        }\n    }\n    _resetCurrentBlock() {\n        this._currentBlock = null;\n    }\n}\nexports.BaseBlockTracker = BaseBlockTracker;\n/**\n * Converts a number represented as a string in hexadecimal format into a native\n * number.\n *\n * @param hexInt - The hex string.\n * @returns The number.\n */\nfunction hexToInt(hexInt) {\n    return Number.parseInt(hexInt, 16);\n}\n//# sourceMappingURL=BaseBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9CYXNlQmxvY2tUcmFja2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCO0FBQ3hCLDZDQUE2QyxtQkFBTyxDQUFDLHlHQUE4QjtBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsb0JBQW9CO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxldGgtYmxvY2stdHJhY2tlclxcZGlzdFxcQmFzZUJsb2NrVHJhY2tlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2ltcG9ydERlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0RGVmYXVsdCkgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgXCJkZWZhdWx0XCI6IG1vZCB9O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQmFzZUJsb2NrVHJhY2tlciA9IHZvaWQgMDtcbmNvbnN0IHNhZmVfZXZlbnRfZW1pdHRlcl8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJAbWV0YW1hc2svc2FmZS1ldmVudC1lbWl0dGVyXCIpKTtcbmNvbnN0IHNlYyA9IDEwMDA7XG5jb25zdCBjYWxjdWxhdGVTdW0gPSAoYWNjdW11bGF0b3IsIGN1cnJlbnRWYWx1ZSkgPT4gYWNjdW11bGF0b3IgKyBjdXJyZW50VmFsdWU7XG5jb25zdCBibG9ja1RyYWNrZXJFdmVudHMgPSBbJ3N5bmMnLCAnbGF0ZXN0J107XG5jbGFzcyBCYXNlQmxvY2tUcmFja2VyIGV4dGVuZHMgc2FmZV9ldmVudF9lbWl0dGVyXzEuZGVmYXVsdCB7XG4gICAgY29uc3RydWN0b3Iob3B0cykge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICAvLyBjb25maWdcbiAgICAgICAgdGhpcy5fYmxvY2tSZXNldER1cmF0aW9uID0gb3B0cy5ibG9ja1Jlc2V0RHVyYXRpb24gfHwgMjAgKiBzZWM7XG4gICAgICAgIHRoaXMuX3VzZVBhc3RCbG9ja3MgPSBvcHRzLnVzZVBhc3RCbG9ja3MgfHwgZmFsc2U7XG4gICAgICAgIC8vIHN0YXRlXG4gICAgICAgIHRoaXMuX2N1cnJlbnRCbG9jayA9IG51bGw7XG4gICAgICAgIHRoaXMuX2lzUnVubmluZyA9IGZhbHNlO1xuICAgICAgICAvLyBiaW5kIGZ1bmN0aW9ucyBmb3IgaW50ZXJuYWwgdXNlXG4gICAgICAgIHRoaXMuX29uTmV3TGlzdGVuZXIgPSB0aGlzLl9vbk5ld0xpc3RlbmVyLmJpbmQodGhpcyk7XG4gICAgICAgIHRoaXMuX29uUmVtb3ZlTGlzdGVuZXIgPSB0aGlzLl9vblJlbW92ZUxpc3RlbmVyLmJpbmQodGhpcyk7XG4gICAgICAgIHRoaXMuX3Jlc2V0Q3VycmVudEJsb2NrID0gdGhpcy5fcmVzZXRDdXJyZW50QmxvY2suYmluZCh0aGlzKTtcbiAgICAgICAgLy8gbGlzdGVuIGZvciBoYW5kbGVyIGNoYW5nZXNcbiAgICAgICAgdGhpcy5fc2V0dXBJbnRlcm5hbEV2ZW50cygpO1xuICAgIH1cbiAgICBhc3luYyBkZXN0cm95KCkge1xuICAgICAgICB0aGlzLl9jYW5jZWxCbG9ja1Jlc2V0VGltZW91dCgpO1xuICAgICAgICBhd2FpdCB0aGlzLl9tYXliZUVuZCgpO1xuICAgICAgICBzdXBlci5yZW1vdmVBbGxMaXN0ZW5lcnMoKTtcbiAgICB9XG4gICAgaXNSdW5uaW5nKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5faXNSdW5uaW5nO1xuICAgIH1cbiAgICBnZXRDdXJyZW50QmxvY2soKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jdXJyZW50QmxvY2s7XG4gICAgfVxuICAgIGFzeW5jIGdldExhdGVzdEJsb2NrKCkge1xuICAgICAgICAvLyByZXR1cm4gaWYgYXZhaWxhYmxlXG4gICAgICAgIGlmICh0aGlzLl9jdXJyZW50QmxvY2spIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9jdXJyZW50QmxvY2s7XG4gICAgICAgIH1cbiAgICAgICAgLy8gd2FpdCBmb3IgYSBuZXcgbGF0ZXN0IGJsb2NrXG4gICAgICAgIGNvbnN0IGxhdGVzdEJsb2NrID0gYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHRoaXMub25jZSgnbGF0ZXN0JywgcmVzb2x2ZSkpO1xuICAgICAgICAvLyByZXR1cm4gbmV3bHkgc2V0IGN1cnJlbnQgYmxvY2tcbiAgICAgICAgcmV0dXJuIGxhdGVzdEJsb2NrO1xuICAgIH1cbiAgICAvLyBkb250IGFsbG93IG1vZHVsZSBjb25zdW1lciB0byByZW1vdmUgb3VyIGludGVybmFsIGV2ZW50IGxpc3RlbmVyc1xuICAgIHJlbW92ZUFsbExpc3RlbmVycyhldmVudE5hbWUpIHtcbiAgICAgICAgLy8gcGVyZm9ybSBkZWZhdWx0IGJlaGF2aW9yLCBwcmVzZXJ2ZSBmbiBhcml0eVxuICAgICAgICBpZiAoZXZlbnROYW1lKSB7XG4gICAgICAgICAgICBzdXBlci5yZW1vdmVBbGxMaXN0ZW5lcnMoZXZlbnROYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN1cGVyLnJlbW92ZUFsbExpc3RlbmVycygpO1xuICAgICAgICB9XG4gICAgICAgIC8vIHJlLWFkZCBpbnRlcm5hbCBldmVudHNcbiAgICAgICAgdGhpcy5fc2V0dXBJbnRlcm5hbEV2ZW50cygpO1xuICAgICAgICAvLyB0cmlnZ2VyIHN0b3AgY2hlY2sganVzdCBpbiBjYXNlXG4gICAgICAgIHRoaXMuX29uUmVtb3ZlTGlzdGVuZXIoKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIF9zZXR1cEludGVybmFsRXZlbnRzKCkge1xuICAgICAgICAvLyBmaXJzdCByZW1vdmUgbGlzdGVuZXJzIGZvciBpZGVtcG90ZW5jZVxuICAgICAgICB0aGlzLnJlbW92ZUxpc3RlbmVyKCduZXdMaXN0ZW5lcicsIHRoaXMuX29uTmV3TGlzdGVuZXIpO1xuICAgICAgICB0aGlzLnJlbW92ZUxpc3RlbmVyKCdyZW1vdmVMaXN0ZW5lcicsIHRoaXMuX29uUmVtb3ZlTGlzdGVuZXIpO1xuICAgICAgICAvLyB0aGVuIGFkZCB0aGVtXG4gICAgICAgIHRoaXMub24oJ25ld0xpc3RlbmVyJywgdGhpcy5fb25OZXdMaXN0ZW5lcik7XG4gICAgICAgIHRoaXMub24oJ3JlbW92ZUxpc3RlbmVyJywgdGhpcy5fb25SZW1vdmVMaXN0ZW5lcik7XG4gICAgfVxuICAgIF9vbk5ld0xpc3RlbmVyKGV2ZW50TmFtZSkge1xuICAgICAgICAvLyBgbmV3TGlzdGVuZXJgIGlzIGNhbGxlZCAqYmVmb3JlKiB0aGUgbGlzdGVuZXIgaXMgYWRkZWRcbiAgICAgICAgaWYgKGJsb2NrVHJhY2tlckV2ZW50cy5pbmNsdWRlcyhldmVudE5hbWUpKSB7XG4gICAgICAgICAgICB0aGlzLl9tYXliZVN0YXJ0KCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgX29uUmVtb3ZlTGlzdGVuZXIoKSB7XG4gICAgICAgIC8vIGByZW1vdmVMaXN0ZW5lcmAgaXMgY2FsbGVkICphZnRlciogdGhlIGxpc3RlbmVyIGlzIHJlbW92ZWRcbiAgICAgICAgaWYgKHRoaXMuX2dldEJsb2NrVHJhY2tlckV2ZW50Q291bnQoKSA+IDApIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9tYXliZUVuZCgpO1xuICAgIH1cbiAgICBhc3luYyBfbWF5YmVTdGFydCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX2lzUnVubmluZykge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuX2lzUnVubmluZyA9IHRydWU7XG4gICAgICAgIC8vIGNhbmNlbCBzZXR0aW5nIGxhdGVzdCBibG9jayB0byBzdGFsZVxuICAgICAgICB0aGlzLl9jYW5jZWxCbG9ja1Jlc2V0VGltZW91dCgpO1xuICAgICAgICBhd2FpdCB0aGlzLl9zdGFydCgpO1xuICAgICAgICB0aGlzLmVtaXQoJ19zdGFydGVkJyk7XG4gICAgfVxuICAgIGFzeW5jIF9tYXliZUVuZCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLl9pc1J1bm5pbmcpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9pc1J1bm5pbmcgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5fc2V0dXBCbG9ja1Jlc2V0VGltZW91dCgpO1xuICAgICAgICBhd2FpdCB0aGlzLl9lbmQoKTtcbiAgICAgICAgdGhpcy5lbWl0KCdfZW5kZWQnKTtcbiAgICB9XG4gICAgX2dldEJsb2NrVHJhY2tlckV2ZW50Q291bnQoKSB7XG4gICAgICAgIHJldHVybiBibG9ja1RyYWNrZXJFdmVudHNcbiAgICAgICAgICAgIC5tYXAoKGV2ZW50TmFtZSkgPT4gdGhpcy5saXN0ZW5lckNvdW50KGV2ZW50TmFtZSkpXG4gICAgICAgICAgICAucmVkdWNlKGNhbGN1bGF0ZVN1bSk7XG4gICAgfVxuICAgIF9zaG91bGRVc2VOZXdCbG9jayhuZXdCbG9jaykge1xuICAgICAgICBjb25zdCBjdXJyZW50QmxvY2sgPSB0aGlzLl9jdXJyZW50QmxvY2s7XG4gICAgICAgIGlmICghY3VycmVudEJsb2NrKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBuZXdCbG9ja0ludCA9IGhleFRvSW50KG5ld0Jsb2NrKTtcbiAgICAgICAgY29uc3QgY3VycmVudEJsb2NrSW50ID0gaGV4VG9JbnQoY3VycmVudEJsb2NrKTtcbiAgICAgICAgcmV0dXJuICgodGhpcy5fdXNlUGFzdEJsb2NrcyAmJiBuZXdCbG9ja0ludCA8IGN1cnJlbnRCbG9ja0ludCkgfHxcbiAgICAgICAgICAgIG5ld0Jsb2NrSW50ID4gY3VycmVudEJsb2NrSW50KTtcbiAgICB9XG4gICAgX25ld1BvdGVudGlhbExhdGVzdChuZXdCbG9jaykge1xuICAgICAgICBpZiAoIXRoaXMuX3Nob3VsZFVzZU5ld0Jsb2NrKG5ld0Jsb2NrKSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuX3NldEN1cnJlbnRCbG9jayhuZXdCbG9jayk7XG4gICAgfVxuICAgIF9zZXRDdXJyZW50QmxvY2sobmV3QmxvY2spIHtcbiAgICAgICAgY29uc3Qgb2xkQmxvY2sgPSB0aGlzLl9jdXJyZW50QmxvY2s7XG4gICAgICAgIHRoaXMuX2N1cnJlbnRCbG9jayA9IG5ld0Jsb2NrO1xuICAgICAgICB0aGlzLmVtaXQoJ2xhdGVzdCcsIG5ld0Jsb2NrKTtcbiAgICAgICAgdGhpcy5lbWl0KCdzeW5jJywgeyBvbGRCbG9jaywgbmV3QmxvY2sgfSk7XG4gICAgfVxuICAgIF9zZXR1cEJsb2NrUmVzZXRUaW1lb3V0KCkge1xuICAgICAgICAvLyBjbGVhciBhbnkgZXhpc3RpbmcgdGltZW91dFxuICAgICAgICB0aGlzLl9jYW5jZWxCbG9ja1Jlc2V0VGltZW91dCgpO1xuICAgICAgICAvLyBjbGVhciBsYXRlc3QgYmxvY2sgd2hlbiBzdGFsZVxuICAgICAgICB0aGlzLl9ibG9ja1Jlc2V0VGltZW91dCA9IHNldFRpbWVvdXQodGhpcy5fcmVzZXRDdXJyZW50QmxvY2ssIHRoaXMuX2Jsb2NrUmVzZXREdXJhdGlvbik7XG4gICAgICAgIC8vIG5vZGVqcyAtIGRvbnQgaG9sZCBwcm9jZXNzIG9wZW5cbiAgICAgICAgaWYgKHRoaXMuX2Jsb2NrUmVzZXRUaW1lb3V0LnVucmVmKSB7XG4gICAgICAgICAgICB0aGlzLl9ibG9ja1Jlc2V0VGltZW91dC51bnJlZigpO1xuICAgICAgICB9XG4gICAgfVxuICAgIF9jYW5jZWxCbG9ja1Jlc2V0VGltZW91dCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX2Jsb2NrUmVzZXRUaW1lb3V0KSB7XG4gICAgICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5fYmxvY2tSZXNldFRpbWVvdXQpO1xuICAgICAgICB9XG4gICAgfVxuICAgIF9yZXNldEN1cnJlbnRCbG9jaygpIHtcbiAgICAgICAgdGhpcy5fY3VycmVudEJsb2NrID0gbnVsbDtcbiAgICB9XG59XG5leHBvcnRzLkJhc2VCbG9ja1RyYWNrZXIgPSBCYXNlQmxvY2tUcmFja2VyO1xuLyoqXG4gKiBDb252ZXJ0cyBhIG51bWJlciByZXByZXNlbnRlZCBhcyBhIHN0cmluZyBpbiBoZXhhZGVjaW1hbCBmb3JtYXQgaW50byBhIG5hdGl2ZVxuICogbnVtYmVyLlxuICpcbiAqIEBwYXJhbSBoZXhJbnQgLSBUaGUgaGV4IHN0cmluZy5cbiAqIEByZXR1cm5zIFRoZSBudW1iZXIuXG4gKi9cbmZ1bmN0aW9uIGhleFRvSW50KGhleEludCkge1xuICAgIHJldHVybiBOdW1iZXIucGFyc2VJbnQoaGV4SW50LCAxNik7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1CYXNlQmxvY2tUcmFja2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/PollingBlockTracker.js":
/*!********************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/PollingBlockTracker.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PollingBlockTracker = void 0;\nconst json_rpc_random_id_1 = __importDefault(__webpack_require__(/*! json-rpc-random-id */ \"(ssr)/./node_modules/json-rpc-random-id/index.js\"));\nconst pify_1 = __importDefault(__webpack_require__(/*! pify */ \"(ssr)/./node_modules/pify/index.js\"));\nconst BaseBlockTracker_1 = __webpack_require__(/*! ./BaseBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js\");\nconst logging_utils_1 = __webpack_require__(/*! ./logging-utils */ \"(ssr)/./node_modules/eth-block-tracker/dist/logging-utils.js\");\nconst log = (0, logging_utils_1.createModuleLogger)(logging_utils_1.projectLogger, 'polling-block-tracker');\nconst createRandomId = (0, json_rpc_random_id_1.default)();\nconst sec = 1000;\nclass PollingBlockTracker extends BaseBlockTracker_1.BaseBlockTracker {\n    constructor(opts = {}) {\n        var _a;\n        // parse + validate args\n        if (!opts.provider) {\n            throw new Error('PollingBlockTracker - no provider specified.');\n        }\n        super(Object.assign(Object.assign({}, opts), { blockResetDuration: (_a = opts.blockResetDuration) !== null && _a !== void 0 ? _a : opts.pollingInterval }));\n        // config\n        this._provider = opts.provider;\n        this._pollingInterval = opts.pollingInterval || 20 * sec;\n        this._retryTimeout = opts.retryTimeout || this._pollingInterval / 10;\n        this._keepEventLoopActive =\n            opts.keepEventLoopActive === undefined ? true : opts.keepEventLoopActive;\n        this._setSkipCacheFlag = opts.setSkipCacheFlag || false;\n    }\n    // trigger block polling\n    async checkForLatestBlock() {\n        await this._updateLatestBlock();\n        return await this.getLatestBlock();\n    }\n    async _start() {\n        this._synchronize();\n    }\n    async _end() {\n        // No-op\n    }\n    async _synchronize() {\n        var _a;\n        while (this._isRunning) {\n            try {\n                await this._updateLatestBlock();\n                const promise = timeout(this._pollingInterval, !this._keepEventLoopActive);\n                this.emit('_waitingForNextIteration');\n                await promise;\n            }\n            catch (err) {\n                const newErr = new Error(`PollingBlockTracker - encountered an error while attempting to update latest block:\\n${(_a = err.stack) !== null && _a !== void 0 ? _a : err}`);\n                try {\n                    this.emit('error', newErr);\n                }\n                catch (emitErr) {\n                    console.error(newErr);\n                }\n                const promise = timeout(this._retryTimeout, !this._keepEventLoopActive);\n                this.emit('_waitingForNextIteration');\n                await promise;\n            }\n        }\n    }\n    async _updateLatestBlock() {\n        // fetch + set latest block\n        const latestBlock = await this._fetchLatestBlock();\n        this._newPotentialLatest(latestBlock);\n    }\n    async _fetchLatestBlock() {\n        const req = {\n            jsonrpc: '2.0',\n            id: createRandomId(),\n            method: 'eth_blockNumber',\n            params: [],\n        };\n        if (this._setSkipCacheFlag) {\n            req.skipCache = true;\n        }\n        log('Making request', req);\n        const res = await (0, pify_1.default)((cb) => this._provider.sendAsync(req, cb))();\n        log('Got response', res);\n        if (res.error) {\n            throw new Error(`PollingBlockTracker - encountered error fetching block:\\n${res.error.message}`);\n        }\n        return res.result;\n    }\n}\nexports.PollingBlockTracker = PollingBlockTracker;\n/**\n * Waits for the specified amount of time.\n *\n * @param duration - The amount of time in milliseconds.\n * @param unref - Assuming this function is run in a Node context, governs\n * whether Node should wait before the `setTimeout` has completed before ending\n * the process (true for no, false for yes). Defaults to false.\n * @returns A promise that can be used to wait.\n */\nfunction timeout(duration, unref) {\n    return new Promise((resolve) => {\n        const timeoutRef = setTimeout(resolve, duration);\n        // don't keep process open\n        if (timeoutRef.unref && unref) {\n            timeoutRef.unref();\n        }\n    });\n}\n//# sourceMappingURL=PollingBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/PollingBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js":
/*!**********************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SubscribeBlockTracker = void 0;\nconst json_rpc_random_id_1 = __importDefault(__webpack_require__(/*! json-rpc-random-id */ \"(ssr)/./node_modules/json-rpc-random-id/index.js\"));\nconst BaseBlockTracker_1 = __webpack_require__(/*! ./BaseBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js\");\nconst createRandomId = (0, json_rpc_random_id_1.default)();\nclass SubscribeBlockTracker extends BaseBlockTracker_1.BaseBlockTracker {\n    constructor(opts = {}) {\n        // parse + validate args\n        if (!opts.provider) {\n            throw new Error('SubscribeBlockTracker - no provider specified.');\n        }\n        // BaseBlockTracker constructor\n        super(opts);\n        // config\n        this._provider = opts.provider;\n        this._subscriptionId = null;\n    }\n    async checkForLatestBlock() {\n        return await this.getLatestBlock();\n    }\n    async _start() {\n        if (this._subscriptionId === undefined || this._subscriptionId === null) {\n            try {\n                const blockNumber = (await this._call('eth_blockNumber'));\n                this._subscriptionId = (await this._call('eth_subscribe', 'newHeads'));\n                this._provider.on('data', this._handleSubData.bind(this));\n                this._newPotentialLatest(blockNumber);\n            }\n            catch (e) {\n                this.emit('error', e);\n            }\n        }\n    }\n    async _end() {\n        if (this._subscriptionId !== null && this._subscriptionId !== undefined) {\n            try {\n                await this._call('eth_unsubscribe', this._subscriptionId);\n                this._subscriptionId = null;\n            }\n            catch (e) {\n                this.emit('error', e);\n            }\n        }\n    }\n    _call(method, ...params) {\n        return new Promise((resolve, reject) => {\n            this._provider.sendAsync({\n                id: createRandomId(),\n                method,\n                params,\n                jsonrpc: '2.0',\n            }, (err, res) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(res.result);\n                }\n            });\n        });\n    }\n    _handleSubData(_, response) {\n        var _a;\n        if (response.method === 'eth_subscription' &&\n            ((_a = response.params) === null || _a === void 0 ? void 0 : _a.subscription) === this._subscriptionId) {\n            this._newPotentialLatest(response.params.result.number);\n        }\n    }\n}\nexports.SubscribeBlockTracker = SubscribeBlockTracker;\n//# sourceMappingURL=SubscribeBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./PollingBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/PollingBlockTracker.js\"), exports);\n__exportStar(__webpack_require__(/*! ./SubscribeBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLGlHQUF1QjtBQUM1QyxhQUFhLG1CQUFPLENBQUMscUdBQXlCO0FBQzlDIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXGV0aC1ibG9jay10cmFja2VyXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL1BvbGxpbmdCbG9ja1RyYWNrZXJcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL1N1YnNjcmliZUJsb2NrVHJhY2tlclwiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/logging-utils.js":
/*!**************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/logging-utils.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createModuleLogger = exports.projectLogger = void 0;\nconst utils_1 = __webpack_require__(/*! @metamask/utils */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js\");\nObject.defineProperty(exports, \"createModuleLogger\", ({ enumerable: true, get: function () { return utils_1.createModuleLogger; } }));\nexports.projectLogger = (0, utils_1.createProjectLogger)('eth-block-tracker');\n//# sourceMappingURL=logging-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9sb2dnaW5nLXV0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQixHQUFHLHFCQUFxQjtBQUNsRCxnQkFBZ0IsbUJBQU8sQ0FBQywwR0FBaUI7QUFDekMsc0RBQXFELEVBQUUscUNBQXFDLHNDQUFzQyxFQUFDO0FBQ25JLHFCQUFxQjtBQUNyQiIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxldGgtYmxvY2stdHJhY2tlclxcZGlzdFxcbG9nZ2luZy11dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuY3JlYXRlTW9kdWxlTG9nZ2VyID0gZXhwb3J0cy5wcm9qZWN0TG9nZ2VyID0gdm9pZCAwO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCJAbWV0YW1hc2svdXRpbHNcIik7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJjcmVhdGVNb2R1bGVMb2dnZXJcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHV0aWxzXzEuY3JlYXRlTW9kdWxlTG9nZ2VyOyB9IH0pO1xuZXhwb3J0cy5wcm9qZWN0TG9nZ2VyID0gKDAsIHV0aWxzXzEuY3JlYXRlUHJvamVjdExvZ2dlcikoJ2V0aC1ibG9jay10cmFja2VyJyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1sb2dnaW5nLXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/logging-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assertExhaustive = exports.assertStruct = exports.assert = exports.AssertionError = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\n/**\n * Type guard for determining whether the given value is an error object with a\n * `message` property, such as an instance of Error.\n *\n * @param error - The object to check.\n * @returns True or false, depending on the result.\n */\nfunction isErrorWithMessage(error) {\n    return typeof error === 'object' && error !== null && 'message' in error;\n}\n/**\n * Check if a value is a constructor, i.e., a function that can be called with\n * the `new` keyword.\n *\n * @param fn - The value to check.\n * @returns `true` if the value is a constructor, or `false` otherwise.\n */\nfunction isConstructable(fn) {\n    var _a, _b;\n    /* istanbul ignore next */\n    return Boolean(typeof ((_b = (_a = fn === null || fn === void 0 ? void 0 : fn.prototype) === null || _a === void 0 ? void 0 : _a.constructor) === null || _b === void 0 ? void 0 : _b.name) === 'string');\n}\n/**\n * Get the error message from an unknown error object. If the error object has\n * a `message` property, that property is returned. Otherwise, the stringified\n * error object is returned.\n *\n * @param error - The error object to get the message from.\n * @returns The error message.\n */\nfunction getErrorMessage(error) {\n    const message = isErrorWithMessage(error) ? error.message : String(error);\n    // If the error ends with a period, remove it, as we'll add our own period.\n    if (message.endsWith('.')) {\n        return message.slice(0, -1);\n    }\n    return message;\n}\n/**\n * Initialise an {@link AssertionErrorConstructor} error.\n *\n * @param ErrorWrapper - The error class to use.\n * @param message - The error message.\n * @returns The error object.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction getError(ErrorWrapper, message) {\n    if (isConstructable(ErrorWrapper)) {\n        return new ErrorWrapper({\n            message,\n        });\n    }\n    return ErrorWrapper({\n        message,\n    });\n}\n/**\n * The default error class that is thrown if an assertion fails.\n */\nclass AssertionError extends Error {\n    constructor(options) {\n        super(options.message);\n        this.code = 'ERR_ASSERTION';\n    }\n}\nexports.AssertionError = AssertionError;\n/**\n * Same as Node.js assert.\n * If the value is falsy, throws an error, does nothing otherwise.\n *\n * @throws {@link AssertionError} If value is falsy.\n * @param value - The test that should be truthy to pass.\n * @param message - Message to be passed to {@link AssertionError} or an\n * {@link Error} instance to throw.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}. If a custom error class is provided for\n * the `message` argument, this argument is ignored.\n */\nfunction assert(value, message = 'Assertion failed.', \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper = AssertionError) {\n    if (!value) {\n        if (message instanceof Error) {\n            throw message;\n        }\n        throw getError(ErrorWrapper, message);\n    }\n}\nexports.assert = assert;\n/**\n * Assert a value against a Superstruct struct.\n *\n * @param value - The value to validate.\n * @param struct - The struct to validate against.\n * @param errorPrefix - A prefix to add to the error message. Defaults to\n * \"Assertion failed\".\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the value is not valid.\n */\nfunction assertStruct(value, struct, errorPrefix = 'Assertion failed', \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper = AssertionError) {\n    try {\n        (0, superstruct_1.assert)(value, struct);\n    }\n    catch (error) {\n        throw getError(ErrorWrapper, `${errorPrefix}: ${getErrorMessage(error)}.`);\n    }\n}\nexports.assertStruct = assertStruct;\n/**\n * Use in the default case of a switch that you want to be fully exhaustive.\n * Using this function forces the compiler to enforce exhaustivity during\n * compile-time.\n *\n * @example\n * ```\n * const number = 1;\n * switch (number) {\n *   case 0:\n *     ...\n *   case 1:\n *     ...\n *   default:\n *     assertExhaustive(snapPrefix);\n * }\n * ```\n * @param _object - The object on which the switch is being operated.\n */\nfunction assertExhaustive(_object) {\n    throw new Error('Invalid branch reached. Should be detected during compilation.');\n}\nexports.assertExhaustive = assertExhaustive;\n//# sourceMappingURL=assert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.base64 = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * Ensure that a provided string-based struct is valid base64.\n *\n * @param struct - The string based struct.\n * @param options - Optional options to specialize base64 validation. See {@link Base64Options} documentation.\n * @returns A superstruct validating base64.\n */\nconst base64 = (struct, options = {}) => {\n    var _a, _b;\n    const paddingRequired = (_a = options.paddingRequired) !== null && _a !== void 0 ? _a : false;\n    const characterSet = (_b = options.characterSet) !== null && _b !== void 0 ? _b : 'base64';\n    let letters;\n    if (characterSet === 'base64') {\n        letters = String.raw `[A-Za-z0-9+\\/]`;\n    }\n    else {\n        (0, assert_1.assert)(characterSet === 'base64url');\n        letters = String.raw `[-_A-Za-z0-9]`;\n    }\n    let re;\n    if (paddingRequired) {\n        re = new RegExp(`^(?:${letters}{4})*(?:${letters}{3}=|${letters}{2}==)?$`, 'u');\n    }\n    else {\n        re = new RegExp(`^(?:${letters}{4})*(?:${letters}{2,3}|${letters}{3}=|${letters}{2}==)?$`, 'u');\n    }\n    return (0, superstruct_1.pattern)(struct, re);\n};\nexports.base64 = base64;\n//# sourceMappingURL=base64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createDataView = exports.concatBytes = exports.valueToBytes = exports.stringToBytes = exports.numberToBytes = exports.signedBigIntToBytes = exports.bigIntToBytes = exports.hexToBytes = exports.bytesToString = exports.bytesToNumber = exports.bytesToSignedBigInt = exports.bytesToBigInt = exports.bytesToHex = exports.assertIsBytes = exports.isBytes = void 0;\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\n// '0'.charCodeAt(0) === 48\nconst HEX_MINIMUM_NUMBER_CHARACTER = 48;\n// '9'.charCodeAt(0) === 57\nconst HEX_MAXIMUM_NUMBER_CHARACTER = 58;\nconst HEX_CHARACTER_OFFSET = 87;\n/**\n * Memoized function that returns an array to be used as a lookup table for\n * converting bytes to hexadecimal values.\n *\n * The array is created lazily and then cached for future use. The benefit of\n * this approach is that the performance of converting bytes to hex is much\n * better than if we were to call `toString(16)` on each byte.\n *\n * The downside is that the array is created once and then never garbage\n * collected. This is not a problem in practice because the array is only 256\n * elements long.\n *\n * @returns A function that returns the lookup table.\n */\nfunction getPrecomputedHexValuesBuilder() {\n    // To avoid issues with tree shaking, we need to use a function to return the\n    // array. This is because the array is only used in the `bytesToHex` function\n    // and if we were to use a global variable, the array might be removed by the\n    // tree shaker.\n    const lookupTable = [];\n    return () => {\n        if (lookupTable.length === 0) {\n            for (let i = 0; i < 256; i++) {\n                lookupTable.push(i.toString(16).padStart(2, '0'));\n            }\n        }\n        return lookupTable;\n    };\n}\n/**\n * Function implementation of the {@link getPrecomputedHexValuesBuilder}\n * function.\n */\nconst getPrecomputedHexValues = getPrecomputedHexValuesBuilder();\n/**\n * Check if a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @returns Whether the value is a `Uint8Array`.\n */\nfunction isBytes(value) {\n    return value instanceof Uint8Array;\n}\nexports.isBytes = isBytes;\n/**\n * Assert that a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @throws If the value is not a `Uint8Array`.\n */\nfunction assertIsBytes(value) {\n    (0, assert_1.assert)(isBytes(value), 'Value must be a Uint8Array.');\n}\nexports.assertIsBytes = assertIsBytes;\n/**\n * Convert a `Uint8Array` to a hexadecimal string.\n *\n * @param bytes - The bytes to convert to a hexadecimal string.\n * @returns The hexadecimal string.\n */\nfunction bytesToHex(bytes) {\n    assertIsBytes(bytes);\n    if (bytes.length === 0) {\n        return '0x';\n    }\n    const lookupTable = getPrecomputedHexValues();\n    const hexadecimal = new Array(bytes.length);\n    for (let i = 0; i < bytes.length; i++) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        hexadecimal[i] = lookupTable[bytes[i]];\n    }\n    return (0, hex_1.add0x)(hexadecimal.join(''));\n}\nexports.bytesToHex = bytesToHex;\n/**\n * Convert a `Uint8Array` to a `bigint`.\n *\n * To convert a `Uint8Array` to a `number` instead, use {@link bytesToNumber}.\n * To convert a two's complement encoded `Uint8Array` to a `bigint`, use\n * {@link bytesToSignedBigInt}.\n *\n * @param bytes - The bytes to convert to a `bigint`.\n * @returns The `bigint`.\n */\nfunction bytesToBigInt(bytes) {\n    assertIsBytes(bytes);\n    const hexadecimal = bytesToHex(bytes);\n    return BigInt(hexadecimal);\n}\nexports.bytesToBigInt = bytesToBigInt;\n/**\n * Convert a `Uint8Array` to a signed `bigint`. This assumes that the bytes are\n * encoded in two's complement.\n *\n * To convert a `Uint8Array` to an unsigned `bigint` instead, use\n * {@link bytesToBigInt}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param bytes - The bytes to convert to a signed `bigint`.\n * @returns The signed `bigint`.\n */\nfunction bytesToSignedBigInt(bytes) {\n    assertIsBytes(bytes);\n    let value = BigInt(0);\n    for (const byte of bytes) {\n        // eslint-disable-next-line no-bitwise\n        value = (value << BigInt(8)) + BigInt(byte);\n    }\n    return BigInt.asIntN(bytes.length * 8, value);\n}\nexports.bytesToSignedBigInt = bytesToSignedBigInt;\n/**\n * Convert a `Uint8Array` to a `number`.\n *\n * To convert a `Uint8Array` to a `bigint` instead, use {@link bytesToBigInt}.\n *\n * @param bytes - The bytes to convert to a number.\n * @returns The number.\n * @throws If the resulting number is not a safe integer.\n */\nfunction bytesToNumber(bytes) {\n    assertIsBytes(bytes);\n    const bigint = bytesToBigInt(bytes);\n    (0, assert_1.assert)(bigint <= BigInt(Number.MAX_SAFE_INTEGER), 'Number is not a safe integer. Use `bytesToBigInt` instead.');\n    return Number(bigint);\n}\nexports.bytesToNumber = bytesToNumber;\n/**\n * Convert a UTF-8 encoded `Uint8Array` to a `string`.\n *\n * @param bytes - The bytes to convert to a string.\n * @returns The string.\n */\nfunction bytesToString(bytes) {\n    assertIsBytes(bytes);\n    return new TextDecoder().decode(bytes);\n}\nexports.bytesToString = bytesToString;\n/**\n * Convert a hexadecimal string to a `Uint8Array`. The string can optionally be\n * prefixed with `0x`. It accepts even and odd length strings.\n *\n * If the value is \"0x\", an empty `Uint8Array` is returned.\n *\n * @param value - The hexadecimal string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction hexToBytes(value) {\n    var _a;\n    // \"0x\" is often used as empty byte array.\n    if (((_a = value === null || value === void 0 ? void 0 : value.toLowerCase) === null || _a === void 0 ? void 0 : _a.call(value)) === '0x') {\n        return new Uint8Array();\n    }\n    (0, hex_1.assertIsHexString)(value);\n    // Remove the `0x` prefix if it exists, and pad the string to have an even\n    // number of characters.\n    const strippedValue = (0, hex_1.remove0x)(value).toLowerCase();\n    const normalizedValue = strippedValue.length % 2 === 0 ? strippedValue : `0${strippedValue}`;\n    const bytes = new Uint8Array(normalizedValue.length / 2);\n    for (let i = 0; i < bytes.length; i++) {\n        // While this is not the prettiest way to convert a hexadecimal string to a\n        // `Uint8Array`, it is a lot faster than using `parseInt` to convert each\n        // character.\n        const c1 = normalizedValue.charCodeAt(i * 2);\n        const c2 = normalizedValue.charCodeAt(i * 2 + 1);\n        const n1 = c1 -\n            (c1 < HEX_MAXIMUM_NUMBER_CHARACTER\n                ? HEX_MINIMUM_NUMBER_CHARACTER\n                : HEX_CHARACTER_OFFSET);\n        const n2 = c2 -\n            (c2 < HEX_MAXIMUM_NUMBER_CHARACTER\n                ? HEX_MINIMUM_NUMBER_CHARACTER\n                : HEX_CHARACTER_OFFSET);\n        bytes[i] = n1 * 16 + n2;\n    }\n    return bytes;\n}\nexports.hexToBytes = hexToBytes;\n/**\n * Convert a `bigint` to a `Uint8Array`.\n *\n * This assumes that the `bigint` is an unsigned integer. To convert a signed\n * `bigint` instead, use {@link signedBigIntToBytes}.\n *\n * @param value - The bigint to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction bigIntToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(value >= BigInt(0), 'Value must be a non-negative bigint.');\n    const hexadecimal = value.toString(16);\n    return hexToBytes(hexadecimal);\n}\nexports.bigIntToBytes = bigIntToBytes;\n/**\n * Check if a `bigint` fits in a certain number of bytes.\n *\n * @param value - The `bigint` to check.\n * @param bytes - The number of bytes.\n * @returns Whether the `bigint` fits in the number of bytes.\n */\nfunction bigIntFits(value, bytes) {\n    (0, assert_1.assert)(bytes > 0);\n    /* eslint-disable no-bitwise */\n    const mask = value >> BigInt(31);\n    return !(((~value & mask) + (value & ~mask)) >> BigInt(bytes * 8 + ~0));\n    /* eslint-enable no-bitwise */\n}\n/**\n * Convert a signed `bigint` to a `Uint8Array`. This uses two's complement\n * encoding to represent negative numbers.\n *\n * To convert an unsigned `bigint` to a `Uint8Array` instead, use\n * {@link bigIntToBytes}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param value - The number to convert to bytes.\n * @param byteLength - The length of the resulting `Uint8Array`. If the number\n * is larger than the maximum value that can be represented by the given length,\n * an error is thrown.\n * @returns The bytes as `Uint8Array`.\n */\nfunction signedBigIntToBytes(value, byteLength) {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(typeof byteLength === 'number', 'Byte length must be a number.');\n    (0, assert_1.assert)(byteLength > 0, 'Byte length must be greater than 0.');\n    (0, assert_1.assert)(bigIntFits(value, byteLength), 'Byte length is too small to represent the given value.');\n    // ESLint doesn't like mutating function parameters, so to avoid having to\n    // disable the rule, we create a new variable.\n    let numberValue = value;\n    const bytes = new Uint8Array(byteLength);\n    for (let i = 0; i < bytes.length; i++) {\n        bytes[i] = Number(BigInt.asUintN(8, numberValue));\n        // eslint-disable-next-line no-bitwise\n        numberValue >>= BigInt(8);\n    }\n    return bytes.reverse();\n}\nexports.signedBigIntToBytes = signedBigIntToBytes;\n/**\n * Convert a `number` to a `Uint8Array`.\n *\n * @param value - The number to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n * @throws If the number is not a safe integer.\n */\nfunction numberToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'number', 'Value must be a number.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative number.');\n    (0, assert_1.assert)(Number.isSafeInteger(value), 'Value is not a safe integer. Use `bigIntToBytes` instead.');\n    const hexadecimal = value.toString(16);\n    return hexToBytes(hexadecimal);\n}\nexports.numberToBytes = numberToBytes;\n/**\n * Convert a `string` to a UTF-8 encoded `Uint8Array`.\n *\n * @param value - The string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction stringToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'string', 'Value must be a string.');\n    return new TextEncoder().encode(value);\n}\nexports.stringToBytes = stringToBytes;\n/**\n * Convert a byte-like value to a `Uint8Array`. The value can be a `Uint8Array`,\n * a `bigint`, a `number`, or a `string`.\n *\n * This will attempt to guess the type of the value based on its type and\n * contents. For more control over the conversion, use the more specific\n * conversion functions, such as {@link hexToBytes} or {@link stringToBytes}.\n *\n * If the value is a `string`, and it is prefixed with `0x`, it will be\n * interpreted as a hexadecimal string. Otherwise, it will be interpreted as a\n * UTF-8 string. To convert a hexadecimal string to bytes without interpreting\n * it as a UTF-8 string, use {@link hexToBytes} instead.\n *\n * If the value is a `bigint`, it is assumed to be unsigned. To convert a signed\n * `bigint` to bytes, use {@link signedBigIntToBytes} instead.\n *\n * If the value is a `Uint8Array`, it will be returned as-is.\n *\n * @param value - The value to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction valueToBytes(value) {\n    if (typeof value === 'bigint') {\n        return bigIntToBytes(value);\n    }\n    if (typeof value === 'number') {\n        return numberToBytes(value);\n    }\n    if (typeof value === 'string') {\n        if (value.startsWith('0x')) {\n            return hexToBytes(value);\n        }\n        return stringToBytes(value);\n    }\n    if (isBytes(value)) {\n        return value;\n    }\n    throw new TypeError(`Unsupported value type: \"${typeof value}\".`);\n}\nexports.valueToBytes = valueToBytes;\n/**\n * Concatenate multiple byte-like values into a single `Uint8Array`. The values\n * can be `Uint8Array`, `bigint`, `number`, or `string`. This uses\n * {@link valueToBytes} under the hood to convert each value to bytes. Refer to\n * the documentation of that function for more information.\n *\n * @param values - The values to concatenate.\n * @returns The concatenated bytes as `Uint8Array`.\n */\nfunction concatBytes(values) {\n    const normalizedValues = new Array(values.length);\n    let byteLength = 0;\n    for (let i = 0; i < values.length; i++) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const value = valueToBytes(values[i]);\n        normalizedValues[i] = value;\n        byteLength += value.length;\n    }\n    const bytes = new Uint8Array(byteLength);\n    for (let i = 0, offset = 0; i < normalizedValues.length; i++) {\n        // While we could simply spread the values into an array and use\n        // `Uint8Array.from`, that is a lot slower than using `Uint8Array.set`.\n        bytes.set(normalizedValues[i], offset);\n        offset += normalizedValues[i].length;\n    }\n    return bytes;\n}\nexports.concatBytes = concatBytes;\n/**\n * Create a {@link DataView} from a {@link Uint8Array}. This is a convenience\n * function that avoids having to create a {@link DataView} manually, which\n * requires passing the `byteOffset` and `byteLength` parameters every time.\n *\n * Not passing the `byteOffset` and `byteLength` parameters can result in\n * unexpected behavior when the {@link Uint8Array} is a view of a larger\n * {@link ArrayBuffer}, e.g., when using {@link Uint8Array.subarray}.\n *\n * This function also supports Node.js {@link Buffer}s.\n *\n * @example\n * ```typescript\n * const bytes = new Uint8Array([1, 2, 3]);\n *\n * // This is equivalent to:\n * // const dataView = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n * const dataView = createDataView(bytes);\n * ```\n * @param bytes - The bytes to create the {@link DataView} from.\n * @returns The {@link DataView}.\n */\nfunction createDataView(bytes) {\n    // To maintain compatibility with Node.js, we need to check if the bytes are\n    // a Buffer. If so, we need to slice the buffer to get the underlying\n    // ArrayBuffer.\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof Buffer !== 'undefined' && bytes instanceof Buffer) {\n        const buffer = bytes.buffer.slice(bytes.byteOffset, bytes.byteOffset + bytes.byteLength);\n        return new DataView(buffer);\n    }\n    return new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n}\nexports.createDataView = createDataView;\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChecksumStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\nconst base64_1 = __webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\");\nexports.ChecksumStruct = (0, superstruct_1.size)((0, base64_1.base64)((0, superstruct_1.string)(), { paddingRequired: true }), 44, 44);\n//# sourceMappingURL=checksum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2NoZWNrc3VtLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHNCQUFzQjtBQUN0QixzQkFBc0IsbUJBQU8sQ0FBQyxtR0FBYTtBQUMzQyxpQkFBaUIsbUJBQU8sQ0FBQyxvR0FBVTtBQUNuQyxzQkFBc0IsK0VBQStFLHVCQUF1QjtBQUM1SCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxldGgtYmxvY2stdHJhY2tlclxcbm9kZV9tb2R1bGVzXFxAbWV0YW1hc2tcXHV0aWxzXFxkaXN0XFxjaGVja3N1bS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ2hlY2tzdW1TdHJ1Y3QgPSB2b2lkIDA7XG5jb25zdCBzdXBlcnN0cnVjdF8xID0gcmVxdWlyZShcInN1cGVyc3RydWN0XCIpO1xuY29uc3QgYmFzZTY0XzEgPSByZXF1aXJlKFwiLi9iYXNlNjRcIik7XG5leHBvcnRzLkNoZWNrc3VtU3RydWN0ID0gKDAsIHN1cGVyc3RydWN0XzEuc2l6ZSkoKDAsIGJhc2U2NF8xLmJhc2U2NCkoKDAsIHN1cGVyc3RydWN0XzEuc3RyaW5nKSgpLCB7IHBhZGRpbmdSZXF1aXJlZDogdHJ1ZSB9KSwgNDQsIDQ0KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZWNrc3VtLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createHex = exports.createBytes = exports.createBigInt = exports.createNumber = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst bytes_1 = __webpack_require__(/*! ./bytes */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\nconst NumberLikeStruct = (0, superstruct_1.union)([(0, superstruct_1.number)(), (0, superstruct_1.bigint)(), (0, superstruct_1.string)(), hex_1.StrictHexStruct]);\nconst NumberCoercer = (0, superstruct_1.coerce)((0, superstruct_1.number)(), NumberLikeStruct, Number);\nconst BigIntCoercer = (0, superstruct_1.coerce)((0, superstruct_1.bigint)(), NumberLikeStruct, BigInt);\nconst BytesLikeStruct = (0, superstruct_1.union)([hex_1.StrictHexStruct, (0, superstruct_1.instance)(Uint8Array)]);\nconst BytesCoercer = (0, superstruct_1.coerce)((0, superstruct_1.instance)(Uint8Array), (0, superstruct_1.union)([hex_1.StrictHexStruct]), bytes_1.hexToBytes);\nconst HexCoercer = (0, superstruct_1.coerce)(hex_1.StrictHexStruct, (0, superstruct_1.instance)(Uint8Array), bytes_1.bytesToHex);\n/**\n * Create a number from a number-like value.\n *\n * - If the value is a number, it is returned as-is.\n * - If the value is a `bigint`, it is converted to a number.\n * - If the value is a string, it is interpreted as a decimal number.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number.\n *\n * This validates that the value is a number-like value, and that the resulting\n * number is not `NaN` or `Infinity`.\n *\n * @example\n * ```typescript\n * const value = createNumber('0x010203');\n * console.log(value); // 66051\n *\n * const otherValue = createNumber(123n);\n * console.log(otherValue); // 123\n * ```\n * @param value - The value to create the number from.\n * @returns The created number.\n * @throws If the value is not a number-like value, or if the resulting number\n * is `NaN` or `Infinity`.\n */\nfunction createNumber(value) {\n    try {\n        const result = (0, superstruct_1.create)(value, NumberCoercer);\n        (0, assert_1.assert)(Number.isFinite(result), `Expected a number-like value, got \"${value}\".`);\n        return result;\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a number-like value, got \"${value}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createNumber = createNumber;\n/**\n * Create a `bigint` from a number-like value.\n *\n * - If the value is a number, it is converted to a `bigint`.\n * - If the value is a `bigint`, it is returned as-is.\n * - If the value is a string, it is interpreted as a decimal number and\n * converted to a `bigint`.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number and converted to a `bigint`.\n *\n * @example\n * ```typescript\n * const value = createBigInt('0x010203');\n * console.log(value); // 16909060n\n *\n * const otherValue = createBigInt(123);\n * console.log(otherValue); // 123n\n * ```\n * @param value - The value to create the bigint from.\n * @returns The created bigint.\n * @throws If the value is not a number-like value.\n */\nfunction createBigInt(value) {\n    try {\n        // The `BigInt` constructor throws if the value is not a number-like value.\n        // There is no need to validate the value manually.\n        return (0, superstruct_1.create)(value, BigIntCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a number-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createBigInt = createBigInt;\n/**\n * Create a byte array from a bytes-like value.\n *\n * - If the value is a byte array, it is returned as-is.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is interpreted\n * as a hexadecimal number and converted to a byte array.\n *\n * @example\n * ```typescript\n * const value = createBytes('0x010203');\n * console.log(value); // Uint8Array [ 1, 2, 3 ]\n *\n * const otherValue = createBytes('0x010203');\n * console.log(otherValue); // Uint8Array [ 1, 2, 3 ]\n * ```\n * @param value - The value to create the byte array from.\n * @returns The created byte array.\n * @throws If the value is not a bytes-like value.\n */\nfunction createBytes(value) {\n    if (typeof value === 'string' && value.toLowerCase() === '0x') {\n        return new Uint8Array();\n    }\n    try {\n        return (0, superstruct_1.create)(value, BytesCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a bytes-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createBytes = createBytes;\n/**\n * Create a hexadecimal string from a bytes-like value.\n *\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is returned\n * as-is.\n * - If the value is a `Uint8Array`, it is converted to a hex string.\n *\n * @example\n * ```typescript\n * const value = createHex(new Uint8Array([1, 2, 3]));\n * console.log(value); // '0x010203'\n *\n * const otherValue = createHex('0x010203');\n * console.log(otherValue); // '0x010203'\n * ```\n * @param value - The value to create the hex string from.\n * @returns The created hex string.\n * @throws If the value is not a bytes-like value.\n */\nfunction createHex(value) {\n    if ((value instanceof Uint8Array && value.length === 0) ||\n        (typeof value === 'string' && value.toLowerCase() === '0x')) {\n        return '0x';\n    }\n    try {\n        return (0, superstruct_1.create)(value, HexCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a bytes-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createHex = createHex;\n//# sourceMappingURL=coercers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _FrozenMap_map, _FrozenSet_set;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FrozenSet = exports.FrozenMap = void 0;\n/**\n * A {@link ReadonlyMap} that cannot be modified after instantiation.\n * The implementation uses an inner map hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this map.\n */\nclass FrozenMap {\n    constructor(entries) {\n        _FrozenMap_map.set(this, void 0);\n        __classPrivateFieldSet(this, _FrozenMap_map, new Map(entries), \"f\");\n        Object.freeze(this);\n    }\n    get size() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").size;\n    }\n    [(_FrozenMap_map = new WeakMap(), Symbol.iterator)]() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\")[Symbol.iterator]();\n    }\n    entries() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").entries();\n    }\n    forEach(callbackfn, thisArg) {\n        // We have to wrap the specified callback in order to prevent it from\n        // receiving a reference to the inner map.\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").forEach((value, key, _map) => callbackfn.call(thisArg, value, key, this));\n    }\n    get(key) {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").get(key);\n    }\n    has(key) {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").has(key);\n    }\n    keys() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").keys();\n    }\n    values() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").values();\n    }\n    toString() {\n        return `FrozenMap(${this.size}) {${this.size > 0\n            ? ` ${[...this.entries()]\n                .map(([key, value]) => `${String(key)} => ${String(value)}`)\n                .join(', ')} `\n            : ''}}`;\n    }\n}\nexports.FrozenMap = FrozenMap;\n/**\n * A {@link ReadonlySet} that cannot be modified after instantiation.\n * The implementation uses an inner set hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this set.\n */\nclass FrozenSet {\n    constructor(values) {\n        _FrozenSet_set.set(this, void 0);\n        __classPrivateFieldSet(this, _FrozenSet_set, new Set(values), \"f\");\n        Object.freeze(this);\n    }\n    get size() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").size;\n    }\n    [(_FrozenSet_set = new WeakMap(), Symbol.iterator)]() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\")[Symbol.iterator]();\n    }\n    entries() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").entries();\n    }\n    forEach(callbackfn, thisArg) {\n        // We have to wrap the specified callback in order to prevent it from\n        // receiving a reference to the inner set.\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").forEach((value, value2, _set) => callbackfn.call(thisArg, value, value2, this));\n    }\n    has(value) {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").has(value);\n    }\n    keys() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").keys();\n    }\n    values() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").values();\n    }\n    toString() {\n        return `FrozenSet(${this.size}) {${this.size > 0\n            ? ` ${[...this.values()].map((member) => String(member)).join(', ')} `\n            : ''}}`;\n    }\n}\nexports.FrozenSet = FrozenSet;\nObject.freeze(FrozenMap);\nObject.freeze(FrozenMap.prototype);\nObject.freeze(FrozenSet);\nObject.freeze(FrozenSet.prototype);\n//# sourceMappingURL=collections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=encryption-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2VuY3J5cHRpb24tdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcZXRoLWJsb2NrLXRyYWNrZXJcXG5vZGVfbW9kdWxlc1xcQG1ldGFtYXNrXFx1dGlsc1xcZGlzdFxcZW5jcnlwdGlvbi10eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVuY3J5cHRpb24tdHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.remove0x = exports.add0x = exports.assertIsStrictHexString = exports.assertIsHexString = exports.isStrictHexString = exports.isHexString = exports.StrictHexStruct = exports.HexStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nexports.HexStruct = (0, superstruct_1.pattern)((0, superstruct_1.string)(), /^(?:0x)?[0-9a-f]+$/iu);\nexports.StrictHexStruct = (0, superstruct_1.pattern)((0, superstruct_1.string)(), /^0x[0-9a-f]+$/iu);\n/**\n * Check if a string is a valid hex string.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nfunction isHexString(value) {\n    return (0, superstruct_1.is)(value, exports.HexStruct);\n}\nexports.isHexString = isHexString;\n/**\n * Strictly check if a string is a valid hex string. A valid hex string must\n * start with the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nfunction isStrictHexString(value) {\n    return (0, superstruct_1.is)(value, exports.StrictHexStruct);\n}\nexports.isStrictHexString = isStrictHexString;\n/**\n * Assert that a value is a valid hex string.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nfunction assertIsHexString(value) {\n    (0, assert_1.assert)(isHexString(value), 'Value must be a hexadecimal string.');\n}\nexports.assertIsHexString = assertIsHexString;\n/**\n * Assert that a value is a valid hex string. A valid hex string must start with\n * the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nfunction assertIsStrictHexString(value) {\n    (0, assert_1.assert)(isStrictHexString(value), 'Value must be a hexadecimal string, starting with \"0x\".');\n}\nexports.assertIsStrictHexString = assertIsStrictHexString;\n/**\n * Add the `0x`-prefix to a hexadecimal string. If the string already has the\n * prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to add the prefix to.\n * @returns The prefixed hexadecimal string.\n */\nfunction add0x(hexadecimal) {\n    if (hexadecimal.startsWith('0x')) {\n        return hexadecimal;\n    }\n    if (hexadecimal.startsWith('0X')) {\n        return `0x${hexadecimal.substring(2)}`;\n    }\n    return `0x${hexadecimal}`;\n}\nexports.add0x = add0x;\n/**\n * Remove the `0x`-prefix from a hexadecimal string. If the string doesn't have\n * the prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to remove the prefix from.\n * @returns The un-prefixed hexadecimal string.\n */\nfunction remove0x(hexadecimal) {\n    if (hexadecimal.startsWith('0x') || hexadecimal.startsWith('0X')) {\n        return hexadecimal.substring(2);\n    }\n    return hexadecimal;\n}\nexports.remove0x = remove0x;\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\"), exports);\n__exportStar(__webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\"), exports);\n__exportStar(__webpack_require__(/*! ./bytes */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\"), exports);\n__exportStar(__webpack_require__(/*! ./checksum */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js\"), exports);\n__exportStar(__webpack_require__(/*! ./coercers */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js\"), exports);\n__exportStar(__webpack_require__(/*! ./collections */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js\"), exports);\n__exportStar(__webpack_require__(/*! ./encryption-types */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\"), exports);\n__exportStar(__webpack_require__(/*! ./json */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js\"), exports);\n__exportStar(__webpack_require__(/*! ./keyring */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js\"), exports);\n__exportStar(__webpack_require__(/*! ./logging */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js\"), exports);\n__exportStar(__webpack_require__(/*! ./misc */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js\"), exports);\n__exportStar(__webpack_require__(/*! ./number */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js\"), exports);\n__exportStar(__webpack_require__(/*! ./opaque */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js\"), exports);\n__exportStar(__webpack_require__(/*! ./time */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js\"), exports);\n__exportStar(__webpack_require__(/*! ./transaction-types */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./versions */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getJsonRpcIdValidator = exports.assertIsJsonRpcError = exports.isJsonRpcError = exports.assertIsJsonRpcFailure = exports.isJsonRpcFailure = exports.assertIsJsonRpcSuccess = exports.isJsonRpcSuccess = exports.assertIsJsonRpcResponse = exports.isJsonRpcResponse = exports.assertIsPendingJsonRpcResponse = exports.isPendingJsonRpcResponse = exports.JsonRpcResponseStruct = exports.JsonRpcFailureStruct = exports.JsonRpcSuccessStruct = exports.PendingJsonRpcResponseStruct = exports.assertIsJsonRpcRequest = exports.isJsonRpcRequest = exports.assertIsJsonRpcNotification = exports.isJsonRpcNotification = exports.JsonRpcNotificationStruct = exports.JsonRpcRequestStruct = exports.JsonRpcParamsStruct = exports.JsonRpcErrorStruct = exports.JsonRpcIdStruct = exports.JsonRpcVersionStruct = exports.jsonrpc2 = exports.getJsonSize = exports.isValidJson = exports.JsonStruct = exports.UnsafeJsonStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * A struct to check if the given value is finite number. Superstruct's\n * `number()` struct does not check if the value is finite.\n *\n * @returns A struct to check if the given value is finite number.\n */\nconst finiteNumber = () => (0, superstruct_1.define)('finite number', (value) => {\n    return (0, superstruct_1.is)(value, (0, superstruct_1.number)()) && Number.isFinite(value);\n});\n/**\n * A struct to check if the given value is a valid JSON-serializable value.\n *\n * Note that this struct is unsafe. For safe validation, use {@link JsonStruct}.\n */\n// We cannot infer the type of the struct, because it is recursive.\nexports.UnsafeJsonStruct = (0, superstruct_1.union)([\n    (0, superstruct_1.literal)(null),\n    (0, superstruct_1.boolean)(),\n    finiteNumber(),\n    (0, superstruct_1.string)(),\n    (0, superstruct_1.array)((0, superstruct_1.lazy)(() => exports.UnsafeJsonStruct)),\n    (0, superstruct_1.record)((0, superstruct_1.string)(), (0, superstruct_1.lazy)(() => exports.UnsafeJsonStruct)),\n]);\n/**\n * A struct to check if the given value is a valid JSON-serializable value.\n *\n * This struct sanitizes the value before validating it, so that it is safe to\n * use with untrusted input.\n */\nexports.JsonStruct = (0, superstruct_1.define)('Json', (value, context) => {\n    /**\n     * Helper function that runs the given struct validator and returns the\n     * validation errors, if any. If the value is valid, it returns `true`.\n     *\n     * @param innerValue - The value to validate.\n     * @param struct - The struct to use for validation.\n     * @returns The validation errors, or `true` if the value is valid.\n     */\n    function checkStruct(innerValue, struct) {\n        const iterator = struct.validator(innerValue, context);\n        const errors = [...iterator];\n        if (errors.length > 0) {\n            return errors;\n        }\n        return true;\n    }\n    try {\n        // The plain value must be a valid JSON value, but it may be altered in the\n        // process of JSON serialization, so we need to validate it again after\n        // serialization. This has the added benefit that the returned error messages\n        // will be more helpful, as they will point to the exact location of the\n        // invalid value.\n        //\n        // This seems overcomplicated, but without checking the plain value first,\n        // there are some cases where the validation passes, even though the value is\n        // not valid JSON. For example, `undefined` is not valid JSON, but serializing\n        // it will remove it from the object, so the validation will pass.\n        const unsafeResult = checkStruct(value, exports.UnsafeJsonStruct);\n        if (unsafeResult !== true) {\n            return unsafeResult;\n        }\n        // JavaScript engines are highly optimized for this specific use case of\n        // JSON parsing and stringifying, so there should be no performance impact.\n        return checkStruct(JSON.parse(JSON.stringify(value)), exports.UnsafeJsonStruct);\n    }\n    catch (error) {\n        if (error instanceof RangeError) {\n            return 'Circular reference detected';\n        }\n        return false;\n    }\n});\n/**\n * Check if the given value is a valid {@link Json} value, i.e., a value that is\n * serializable to JSON.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid {@link Json} value.\n */\nfunction isValidJson(value) {\n    return (0, superstruct_1.is)(value, exports.JsonStruct);\n}\nexports.isValidJson = isValidJson;\n/**\n * Get the size of a JSON value in bytes. This also validates the value.\n *\n * @param value - The JSON value to get the size of.\n * @returns The size of the JSON value in bytes.\n */\nfunction getJsonSize(value) {\n    (0, assert_1.assertStruct)(value, exports.JsonStruct, 'Invalid JSON value');\n    const json = JSON.stringify(value);\n    return new TextEncoder().encode(json).byteLength;\n}\nexports.getJsonSize = getJsonSize;\n/**\n * The string '2.0'.\n */\nexports.jsonrpc2 = '2.0';\nexports.JsonRpcVersionStruct = (0, superstruct_1.literal)(exports.jsonrpc2);\nexports.JsonRpcIdStruct = (0, superstruct_1.nullable)((0, superstruct_1.union)([(0, superstruct_1.number)(), (0, superstruct_1.string)()]));\nexports.JsonRpcErrorStruct = (0, superstruct_1.object)({\n    code: (0, superstruct_1.integer)(),\n    message: (0, superstruct_1.string)(),\n    data: (0, superstruct_1.optional)(exports.JsonStruct),\n    stack: (0, superstruct_1.optional)((0, superstruct_1.string)()),\n});\nexports.JsonRpcParamsStruct = (0, superstruct_1.optional)((0, superstruct_1.union)([(0, superstruct_1.record)((0, superstruct_1.string)(), exports.JsonStruct), (0, superstruct_1.array)(exports.JsonStruct)]));\nexports.JsonRpcRequestStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    method: (0, superstruct_1.string)(),\n    params: exports.JsonRpcParamsStruct,\n});\nexports.JsonRpcNotificationStruct = (0, superstruct_1.omit)(exports.JsonRpcRequestStruct, ['id']);\n/**\n * Check if the given value is a valid {@link JsonRpcNotification} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcNotification}\n * object.\n */\nfunction isJsonRpcNotification(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcNotificationStruct);\n}\nexports.isJsonRpcNotification = isJsonRpcNotification;\n/**\n * Assert that the given value is a valid {@link JsonRpcNotification} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcNotification} object.\n */\nfunction assertIsJsonRpcNotification(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcNotificationStruct, 'Invalid JSON-RPC notification', ErrorWrapper);\n}\nexports.assertIsJsonRpcNotification = assertIsJsonRpcNotification;\n/**\n * Check if the given value is a valid {@link JsonRpcRequest} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcRequest} object.\n */\nfunction isJsonRpcRequest(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcRequestStruct);\n}\nexports.isJsonRpcRequest = isJsonRpcRequest;\n/**\n * Assert that the given value is a valid {@link JsonRpcRequest} object.\n *\n * @param value - The JSON-RPC request or notification to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcRequest} object.\n */\nfunction assertIsJsonRpcRequest(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcRequestStruct, 'Invalid JSON-RPC request', ErrorWrapper);\n}\nexports.assertIsJsonRpcRequest = assertIsJsonRpcRequest;\nexports.PendingJsonRpcResponseStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    result: (0, superstruct_1.optional)((0, superstruct_1.unknown)()),\n    error: (0, superstruct_1.optional)(exports.JsonRpcErrorStruct),\n});\nexports.JsonRpcSuccessStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    result: exports.JsonStruct,\n});\nexports.JsonRpcFailureStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    error: exports.JsonRpcErrorStruct,\n});\nexports.JsonRpcResponseStruct = (0, superstruct_1.union)([\n    exports.JsonRpcSuccessStruct,\n    exports.JsonRpcFailureStruct,\n]);\n/**\n * Type guard to check whether specified JSON-RPC response is a\n * {@link PendingJsonRpcResponse}.\n *\n * @param response - The JSON-RPC response to check.\n * @returns Whether the specified JSON-RPC response is pending.\n */\nfunction isPendingJsonRpcResponse(response) {\n    return (0, superstruct_1.is)(response, exports.PendingJsonRpcResponseStruct);\n}\nexports.isPendingJsonRpcResponse = isPendingJsonRpcResponse;\n/**\n * Assert that the given value is a valid {@link PendingJsonRpcResponse} object.\n *\n * @param response - The JSON-RPC response to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link PendingJsonRpcResponse}\n * object.\n */\nfunction assertIsPendingJsonRpcResponse(response, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(response, exports.PendingJsonRpcResponseStruct, 'Invalid pending JSON-RPC response', ErrorWrapper);\n}\nexports.assertIsPendingJsonRpcResponse = assertIsPendingJsonRpcResponse;\n/**\n * Type guard to check if a value is a {@link JsonRpcResponse}.\n *\n * @param response - The object to check.\n * @returns Whether the object is a JsonRpcResponse.\n */\nfunction isJsonRpcResponse(response) {\n    return (0, superstruct_1.is)(response, exports.JsonRpcResponseStruct);\n}\nexports.isJsonRpcResponse = isJsonRpcResponse;\n/**\n * Assert that the given value is a valid {@link JsonRpcResponse} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcResponse} object.\n */\nfunction assertIsJsonRpcResponse(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcResponseStruct, 'Invalid JSON-RPC response', ErrorWrapper);\n}\nexports.assertIsJsonRpcResponse = assertIsJsonRpcResponse;\n/**\n * Check if the given value is a valid {@link JsonRpcSuccess} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcSuccess} object.\n */\nfunction isJsonRpcSuccess(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcSuccessStruct);\n}\nexports.isJsonRpcSuccess = isJsonRpcSuccess;\n/**\n * Assert that the given value is a valid {@link JsonRpcSuccess} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcSuccess} object.\n */\nfunction assertIsJsonRpcSuccess(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcSuccessStruct, 'Invalid JSON-RPC success response', ErrorWrapper);\n}\nexports.assertIsJsonRpcSuccess = assertIsJsonRpcSuccess;\n/**\n * Check if the given value is a valid {@link JsonRpcFailure} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcFailure} object.\n */\nfunction isJsonRpcFailure(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcFailureStruct);\n}\nexports.isJsonRpcFailure = isJsonRpcFailure;\n/**\n * Assert that the given value is a valid {@link JsonRpcFailure} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcFailure} object.\n */\nfunction assertIsJsonRpcFailure(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcFailureStruct, 'Invalid JSON-RPC failure response', ErrorWrapper);\n}\nexports.assertIsJsonRpcFailure = assertIsJsonRpcFailure;\n/**\n * Check if the given value is a valid {@link JsonRpcError} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcError} object.\n */\nfunction isJsonRpcError(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcErrorStruct);\n}\nexports.isJsonRpcError = isJsonRpcError;\n/**\n * Assert that the given value is a valid {@link JsonRpcError} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcError} object.\n */\nfunction assertIsJsonRpcError(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcErrorStruct, 'Invalid JSON-RPC error', ErrorWrapper);\n}\nexports.assertIsJsonRpcError = assertIsJsonRpcError;\n/**\n * Gets a function for validating JSON-RPC request / response `id` values.\n *\n * By manipulating the options of this factory, you can control the behavior\n * of the resulting validator for some edge cases. This is useful because e.g.\n * `null` should sometimes but not always be permitted.\n *\n * Note that the empty string (`''`) is always permitted by the JSON-RPC\n * specification, but that kind of sucks and you may want to forbid it in some\n * instances anyway.\n *\n * For more details, see the\n * [JSON-RPC Specification](https://www.jsonrpc.org/specification).\n *\n * @param options - An options object.\n * @param options.permitEmptyString - Whether the empty string (i.e. `''`)\n * should be treated as a valid ID. Default: `true`\n * @param options.permitFractions - Whether fractional numbers (e.g. `1.2`)\n * should be treated as valid IDs. Default: `false`\n * @param options.permitNull - Whether `null` should be treated as a valid ID.\n * Default: `true`\n * @returns The JSON-RPC ID validator function.\n */\nfunction getJsonRpcIdValidator(options) {\n    const { permitEmptyString, permitFractions, permitNull } = Object.assign({ permitEmptyString: true, permitFractions: false, permitNull: true }, options);\n    /**\n     * Type guard for {@link JsonRpcId}.\n     *\n     * @param id - The JSON-RPC ID value to check.\n     * @returns Whether the given ID is valid per the options given to the\n     * factory.\n     */\n    const isValidJsonRpcId = (id) => {\n        return Boolean((typeof id === 'number' && (permitFractions || Number.isInteger(id))) ||\n            (typeof id === 'string' && (permitEmptyString || id.length > 0)) ||\n            (permitNull && id === null));\n    };\n    return isValidJsonRpcId;\n}\nexports.getJsonRpcIdValidator = getJsonRpcIdValidator;\n//# sourceMappingURL=json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=keyring.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2tleXJpbmcuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcZXRoLWJsb2NrLXRyYWNrZXJcXG5vZGVfbW9kdWxlc1xcQG1ldGFtYXNrXFx1dGlsc1xcZGlzdFxca2V5cmluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWtleXJpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createModuleLogger = exports.createProjectLogger = void 0;\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\"));\nconst globalLogger = (0, debug_1.default)('metamask');\n/**\n * Creates a logger via the `debug` library whose log messages will be tagged\n * using the name of your project. By default, such messages will be\n * suppressed, but you can reveal them by setting the `DEBUG` environment\n * variable to `metamask:<projectName>`. You can also set this variable to\n * `metamask:*` if you want to see log messages from all MetaMask projects that\n * are also using this function to create their loggers.\n *\n * @param projectName - The name of your project. This should be the name of\n * your NPM package if you're developing one.\n * @returns An instance of `debug`.\n */\nfunction createProjectLogger(projectName) {\n    return globalLogger.extend(projectName);\n}\nexports.createProjectLogger = createProjectLogger;\n/**\n * Creates a logger via the `debug` library which is derived from the logger for\n * the whole project whose log messages will be tagged using the name of your\n * module. By default, such messages will be suppressed, but you can reveal them\n * by setting the `DEBUG` environment variable to\n * `metamask:<projectName>:<moduleName>`. You can also set this variable to\n * `metamask:<projectName>:*` if you want to see log messages from the project,\n * or `metamask:*` if you want to see log messages from all MetaMask projects.\n *\n * @param projectLogger - The logger created via {@link createProjectLogger}.\n * @param moduleName - The name of your module. You could use the name of the\n * file where you're using this logger or some other name.\n * @returns An instance of `debug`.\n */\nfunction createModuleLogger(projectLogger, moduleName) {\n    return projectLogger.extend(moduleName);\n}\nexports.createModuleLogger = createModuleLogger;\n//# sourceMappingURL=logging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n//\n// Types\n//\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.calculateNumberSize = exports.calculateStringSize = exports.isASCII = exports.isPlainObject = exports.ESCAPE_CHARACTERS_REGEXP = exports.JsonSize = exports.hasProperty = exports.isObject = exports.isNullOrUndefined = exports.isNonEmptyArray = void 0;\n//\n// Type Guards\n//\n/**\n * A {@link NonEmptyArray} type guard.\n *\n * @template Element - The non-empty array member type.\n * @param value - The value to check.\n * @returns Whether the value is a non-empty array.\n */\nfunction isNonEmptyArray(value) {\n    return Array.isArray(value) && value.length > 0;\n}\nexports.isNonEmptyArray = isNonEmptyArray;\n/**\n * Type guard for \"nullishness\".\n *\n * @param value - Any value.\n * @returns `true` if the value is null or undefined, `false` otherwise.\n */\nfunction isNullOrUndefined(value) {\n    return value === null || value === undefined;\n}\nexports.isNullOrUndefined = isNullOrUndefined;\n/**\n * A type guard for {@link RuntimeObject}.\n *\n * @param value - The value to check.\n * @returns Whether the specified value has a runtime type of `object` and is\n * neither `null` nor an `Array`.\n */\nfunction isObject(value) {\n    return Boolean(value) && typeof value === 'object' && !Array.isArray(value);\n}\nexports.isObject = isObject;\n//\n// Other utility functions\n//\n/**\n * A type guard for ensuring an object has a property.\n *\n * @param objectToCheck - The object to check.\n * @param name - The property name to check for.\n * @returns Whether the specified object has an own property with the specified\n * name, regardless of whether it is enumerable or not.\n */\nconst hasProperty = (objectToCheck, name) => Object.hasOwnProperty.call(objectToCheck, name);\nexports.hasProperty = hasProperty;\n/**\n * Predefined sizes (in Bytes) of specific parts of JSON structure.\n */\nvar JsonSize;\n(function (JsonSize) {\n    JsonSize[JsonSize[\"Null\"] = 4] = \"Null\";\n    JsonSize[JsonSize[\"Comma\"] = 1] = \"Comma\";\n    JsonSize[JsonSize[\"Wrapper\"] = 1] = \"Wrapper\";\n    JsonSize[JsonSize[\"True\"] = 4] = \"True\";\n    JsonSize[JsonSize[\"False\"] = 5] = \"False\";\n    JsonSize[JsonSize[\"Quote\"] = 1] = \"Quote\";\n    JsonSize[JsonSize[\"Colon\"] = 1] = \"Colon\";\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    JsonSize[JsonSize[\"Date\"] = 24] = \"Date\";\n})(JsonSize = exports.JsonSize || (exports.JsonSize = {}));\n/**\n * Regular expression with pattern matching for (special) escaped characters.\n */\nexports.ESCAPE_CHARACTERS_REGEXP = /\"|\\\\|\\n|\\r|\\t/gu;\n/**\n * Check if the value is plain object.\n *\n * @param value - Value to be checked.\n * @returns True if an object is the plain JavaScript object,\n * false if the object is not plain (e.g. function).\n */\nfunction isPlainObject(value) {\n    if (typeof value !== 'object' || value === null) {\n        return false;\n    }\n    try {\n        let proto = value;\n        while (Object.getPrototypeOf(proto) !== null) {\n            proto = Object.getPrototypeOf(proto);\n        }\n        return Object.getPrototypeOf(value) === proto;\n    }\n    catch (_) {\n        return false;\n    }\n}\nexports.isPlainObject = isPlainObject;\n/**\n * Check if character is ASCII.\n *\n * @param character - Character.\n * @returns True if a character code is ASCII, false if not.\n */\nfunction isASCII(character) {\n    return character.charCodeAt(0) <= 127;\n}\nexports.isASCII = isASCII;\n/**\n * Calculate string size.\n *\n * @param value - String value to calculate size.\n * @returns Number of bytes used to store whole string value.\n */\nfunction calculateStringSize(value) {\n    var _a;\n    const size = value.split('').reduce((total, character) => {\n        if (isASCII(character)) {\n            return total + 1;\n        }\n        return total + 2;\n    }, 0);\n    // Also detect characters that need backslash escape\n    return size + ((_a = value.match(exports.ESCAPE_CHARACTERS_REGEXP)) !== null && _a !== void 0 ? _a : []).length;\n}\nexports.calculateStringSize = calculateStringSize;\n/**\n * Calculate size of a number ofter JSON serialization.\n *\n * @param value - Number value to calculate size.\n * @returns Number of bytes used to store whole number in JSON.\n */\nfunction calculateNumberSize(value) {\n    return value.toString().length;\n}\nexports.calculateNumberSize = calculateNumberSize;\n//# sourceMappingURL=misc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hexToBigInt = exports.hexToNumber = exports.bigIntToHex = exports.numberToHex = void 0;\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\n/**\n * Convert a number to a hexadecimal string. This verifies that the number is a\n * non-negative safe integer.\n *\n * To convert a `bigint` to a hexadecimal string instead, use\n * {@link bigIntToHex}.\n *\n * @example\n * ```typescript\n * numberToHex(0); // '0x0'\n * numberToHex(1); // '0x1'\n * numberToHex(16); // '0x10'\n * ```\n * @param value - The number to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the number is not a non-negative safe integer.\n */\nconst numberToHex = (value) => {\n    (0, assert_1.assert)(typeof value === 'number', 'Value must be a number.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative number.');\n    (0, assert_1.assert)(Number.isSafeInteger(value), 'Value is not a safe integer. Use `bigIntToHex` instead.');\n    return (0, hex_1.add0x)(value.toString(16));\n};\nexports.numberToHex = numberToHex;\n/**\n * Convert a `bigint` to a hexadecimal string. This verifies that the `bigint`\n * is a non-negative integer.\n *\n * To convert a number to a hexadecimal string instead, use {@link numberToHex}.\n *\n * @example\n * ```typescript\n * bigIntToHex(0n); // '0x0'\n * bigIntToHex(1n); // '0x1'\n * bigIntToHex(16n); // '0x10'\n * ```\n * @param value - The `bigint` to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the `bigint` is not a non-negative integer.\n */\nconst bigIntToHex = (value) => {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative bigint.');\n    return (0, hex_1.add0x)(value.toString(16));\n};\nexports.bigIntToHex = bigIntToHex;\n/**\n * Convert a hexadecimal string to a number. This verifies that the string is a\n * valid hex string, and that the resulting number is a safe integer. Both\n * \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a `bigint` instead, use\n * {@link hexToBigInt}.\n *\n * @example\n * ```typescript\n * hexToNumber('0x0'); // 0\n * hexToNumber('0x1'); // 1\n * hexToNumber('0x10'); // 16\n * ```\n * @param value - The hexadecimal string to convert to a number.\n * @returns The number.\n * @throws If the value is not a valid hexadecimal string, or if the resulting\n * number is not a safe integer.\n */\nconst hexToNumber = (value) => {\n    (0, hex_1.assertIsHexString)(value);\n    // `parseInt` accepts values without the \"0x\"-prefix, whereas `Number` does\n    // not. Using this is slightly faster than `Number(add0x(value))`.\n    const numberValue = parseInt(value, 16);\n    (0, assert_1.assert)(Number.isSafeInteger(numberValue), 'Value is not a safe integer. Use `hexToBigInt` instead.');\n    return numberValue;\n};\nexports.hexToNumber = hexToNumber;\n/**\n * Convert a hexadecimal string to a `bigint`. This verifies that the string is\n * a valid hex string. Both \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a number instead, use {@link hexToNumber}.\n *\n * @example\n * ```typescript\n * hexToBigInt('0x0'); // 0n\n * hexToBigInt('0x1'); // 1n\n * hexToBigInt('0x10'); // 16n\n * ```\n * @param value - The hexadecimal string to convert to a `bigint`.\n * @returns The `bigint`.\n * @throws If the value is not a valid hexadecimal string.\n */\nconst hexToBigInt = (value) => {\n    (0, hex_1.assertIsHexString)(value);\n    // The `BigInt` constructor requires the \"0x\"-prefix to parse a hex string.\n    return BigInt((0, hex_1.add0x)(value));\n};\nexports.hexToBigInt = hexToBigInt;\n//# sourceMappingURL=number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=opaque.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L29wYXF1ZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxldGgtYmxvY2stdHJhY2tlclxcbm9kZV9tb2R1bGVzXFxAbWV0YW1hc2tcXHV0aWxzXFxkaXN0XFxvcGFxdWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1vcGFxdWUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.timeSince = exports.inMilliseconds = exports.Duration = void 0;\n/**\n * Common duration constants, in milliseconds.\n */\nvar Duration;\n(function (Duration) {\n    /**\n     * A millisecond.\n     */\n    Duration[Duration[\"Millisecond\"] = 1] = \"Millisecond\";\n    /**\n     * A second, in milliseconds.\n     */\n    Duration[Duration[\"Second\"] = 1000] = \"Second\";\n    /**\n     * A minute, in milliseconds.\n     */\n    Duration[Duration[\"Minute\"] = 60000] = \"Minute\";\n    /**\n     * An hour, in milliseconds.\n     */\n    Duration[Duration[\"Hour\"] = 3600000] = \"Hour\";\n    /**\n     * A day, in milliseconds.\n     */\n    Duration[Duration[\"Day\"] = 86400000] = \"Day\";\n    /**\n     * A week, in milliseconds.\n     */\n    Duration[Duration[\"Week\"] = 604800000] = \"Week\";\n    /**\n     * A year, in milliseconds.\n     */\n    Duration[Duration[\"Year\"] = 31536000000] = \"Year\";\n})(Duration = exports.Duration || (exports.Duration = {}));\nconst isNonNegativeInteger = (number) => Number.isInteger(number) && number >= 0;\nconst assertIsNonNegativeInteger = (number, name) => {\n    if (!isNonNegativeInteger(number)) {\n        throw new Error(`\"${name}\" must be a non-negative integer. Received: \"${number}\".`);\n    }\n};\n/**\n * Calculates the millisecond value of the specified number of units of time.\n *\n * @param count - The number of units of time.\n * @param duration - The unit of time to count.\n * @returns The count multiplied by the specified duration.\n */\nfunction inMilliseconds(count, duration) {\n    assertIsNonNegativeInteger(count, 'count');\n    return count * duration;\n}\nexports.inMilliseconds = inMilliseconds;\n/**\n * Gets the milliseconds since a particular Unix epoch timestamp.\n *\n * @param timestamp - A Unix millisecond timestamp.\n * @returns The number of milliseconds elapsed since the specified timestamp.\n */\nfunction timeSince(timestamp) {\n    assertIsNonNegativeInteger(timestamp, 'timestamp');\n    return Date.now() - timestamp;\n}\nexports.timeSince = timeSince;\n//# sourceMappingURL=time.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=transaction-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L3RyYW5zYWN0aW9uLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXGV0aC1ibG9jay10cmFja2VyXFxub2RlX21vZHVsZXNcXEBtZXRhbWFza1xcdXRpbHNcXGRpc3RcXHRyYW5zYWN0aW9uLXR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhbnNhY3Rpb24tdHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.satisfiesVersionRange = exports.gtRange = exports.gtVersion = exports.assertIsSemVerRange = exports.assertIsSemVerVersion = exports.isValidSemVerRange = exports.isValidSemVerVersion = exports.VersionRangeStruct = exports.VersionStruct = void 0;\nconst semver_1 = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * A struct for validating a version string.\n */\nexports.VersionStruct = (0, superstruct_1.refine)((0, superstruct_1.string)(), 'Version', (value) => {\n    if ((0, semver_1.valid)(value) === null) {\n        return `Expected SemVer version, got \"${value}\"`;\n    }\n    return true;\n});\nexports.VersionRangeStruct = (0, superstruct_1.refine)((0, superstruct_1.string)(), 'Version range', (value) => {\n    if ((0, semver_1.validRange)(value) === null) {\n        return `Expected SemVer range, got \"${value}\"`;\n    }\n    return true;\n});\n/**\n * Checks whether a SemVer version is valid.\n *\n * @param version - A potential version.\n * @returns `true` if the version is valid, and `false` otherwise.\n */\nfunction isValidSemVerVersion(version) {\n    return (0, superstruct_1.is)(version, exports.VersionStruct);\n}\nexports.isValidSemVerVersion = isValidSemVerVersion;\n/**\n * Checks whether a SemVer version range is valid.\n *\n * @param versionRange - A potential version range.\n * @returns `true` if the version range is valid, and `false` otherwise.\n */\nfunction isValidSemVerRange(versionRange) {\n    return (0, superstruct_1.is)(versionRange, exports.VersionRangeStruct);\n}\nexports.isValidSemVerRange = isValidSemVerRange;\n/**\n * Asserts that a value is a valid concrete SemVer version.\n *\n * @param version - A potential SemVer concrete version.\n */\nfunction assertIsSemVerVersion(version) {\n    (0, assert_1.assertStruct)(version, exports.VersionStruct);\n}\nexports.assertIsSemVerVersion = assertIsSemVerVersion;\n/**\n * Asserts that a value is a valid SemVer range.\n *\n * @param range - A potential SemVer range.\n */\nfunction assertIsSemVerRange(range) {\n    (0, assert_1.assertStruct)(range, exports.VersionRangeStruct);\n}\nexports.assertIsSemVerRange = assertIsSemVerRange;\n/**\n * Checks whether a SemVer version is greater than another.\n *\n * @param version1 - The left-hand version.\n * @param version2 - The right-hand version.\n * @returns `version1 > version2`.\n */\nfunction gtVersion(version1, version2) {\n    return (0, semver_1.gt)(version1, version2);\n}\nexports.gtVersion = gtVersion;\n/**\n * Checks whether a SemVer version is greater than all possibilities in a range.\n *\n * @param version - A SemvVer version.\n * @param range - The range to check against.\n * @returns `version > range`.\n */\nfunction gtRange(version, range) {\n    return (0, semver_1.gtr)(version, range);\n}\nexports.gtRange = gtRange;\n/**\n * Returns whether a SemVer version satisfies a SemVer range.\n *\n * @param version - The SemVer version to check.\n * @param versionRange - The SemVer version range to check against.\n * @returns Whether the version satisfied the version range.\n */\nfunction satisfiesVersionRange(version, versionRange) {\n    return (0, semver_1.satisfies)(version, versionRange, {\n        includePrerelease: true,\n    });\n}\nexports.satisfiesVersionRange = satisfiesVersionRange;\n//# sourceMappingURL=versions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Struct: () => (/* binding */ Struct),\n/* harmony export */   StructError: () => (/* binding */ StructError),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   bigint: () => (/* binding */ bigint),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   defaulted: () => (/* binding */ defaulted),\n/* harmony export */   define: () => (/* binding */ define),\n/* harmony export */   deprecated: () => (/* binding */ deprecated),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   enums: () => (/* binding */ enums),\n/* harmony export */   func: () => (/* binding */ func),\n/* harmony export */   instance: () => (/* binding */ instance),\n/* harmony export */   integer: () => (/* binding */ integer),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   literal: () => (/* binding */ literal),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   mask: () => (/* binding */ mask),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   nonempty: () => (/* binding */ nonempty),\n/* harmony export */   nullable: () => (/* binding */ nullable),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   omit: () => (/* binding */ omit),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   partial: () => (/* binding */ partial),\n/* harmony export */   pattern: () => (/* binding */ pattern),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   record: () => (/* binding */ record),\n/* harmony export */   refine: () => (/* binding */ refine),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   struct: () => (/* binding */ struct),\n/* harmony export */   trimmed: () => (/* binding */ trimmed),\n/* harmony export */   tuple: () => (/* binding */ tuple),\n/* harmony export */   type: () => (/* binding */ type),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unknown: () => (/* binding */ unknown),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\nclass StructError extends TypeError {\n    constructor(failure, failures) {\n        let cached;\n        const { message, explanation, ...rest } = failure;\n        const { path } = failure;\n        const msg = path.length === 0 ? message : `At path: ${path.join('.')} -- ${message}`;\n        super(explanation ?? msg);\n        if (explanation != null)\n            this.cause = msg;\n        Object.assign(this, rest);\n        this.name = this.constructor.name;\n        this.failures = () => {\n            return (cached ?? (cached = [failure, ...failures()]));\n        };\n    }\n}\n\n/**\n * Check if a value is an iterator.\n */\nfunction isIterable(x) {\n    return isObject(x) && typeof x[Symbol.iterator] === 'function';\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isObject(x) {\n    return typeof x === 'object' && x != null;\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isPlainObject(x) {\n    if (Object.prototype.toString.call(x) !== '[object Object]') {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(x);\n    return prototype === null || prototype === Object.prototype;\n}\n/**\n * Return a value as a printable string.\n */\nfunction print(value) {\n    if (typeof value === 'symbol') {\n        return value.toString();\n    }\n    return typeof value === 'string' ? JSON.stringify(value) : `${value}`;\n}\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\nfunction shiftIterator(input) {\n    const { done, value } = input.next();\n    return done ? undefined : value;\n}\n/**\n * Convert a single validation result to a failure.\n */\nfunction toFailure(result, context, struct, value) {\n    if (result === true) {\n        return;\n    }\n    else if (result === false) {\n        result = {};\n    }\n    else if (typeof result === 'string') {\n        result = { message: result };\n    }\n    const { path, branch } = context;\n    const { type } = struct;\n    const { refinement, message = `Expected a value of type \\`${type}\\`${refinement ? ` with refinement \\`${refinement}\\`` : ''}, but received: \\`${print(value)}\\``, } = result;\n    return {\n        value,\n        type,\n        refinement,\n        key: path[path.length - 1],\n        path,\n        branch,\n        ...result,\n        message,\n    };\n}\n/**\n * Convert a validation result to an iterable of failures.\n */\nfunction* toFailures(result, context, struct, value) {\n    if (!isIterable(result)) {\n        result = [result];\n    }\n    for (const r of result) {\n        const failure = toFailure(r, context, struct, value);\n        if (failure) {\n            yield failure;\n        }\n    }\n}\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\nfunction* run(value, struct, options = {}) {\n    const { path = [], branch = [value], coerce = false, mask = false } = options;\n    const ctx = { path, branch };\n    if (coerce) {\n        value = struct.coercer(value, ctx);\n        if (mask &&\n            struct.type !== 'type' &&\n            isObject(struct.schema) &&\n            isObject(value) &&\n            !Array.isArray(value)) {\n            for (const key in value) {\n                if (struct.schema[key] === undefined) {\n                    delete value[key];\n                }\n            }\n        }\n    }\n    let status = 'valid';\n    for (const failure of struct.validator(value, ctx)) {\n        failure.explanation = options.message;\n        status = 'not_valid';\n        yield [failure, undefined];\n    }\n    for (let [k, v, s] of struct.entries(value, ctx)) {\n        const ts = run(v, s, {\n            path: k === undefined ? path : [...path, k],\n            branch: k === undefined ? branch : [...branch, v],\n            coerce,\n            mask,\n            message: options.message,\n        });\n        for (const t of ts) {\n            if (t[0]) {\n                status = t[0].refinement != null ? 'not_refined' : 'not_valid';\n                yield [t[0], undefined];\n            }\n            else if (coerce) {\n                v = t[1];\n                if (k === undefined) {\n                    value = v;\n                }\n                else if (value instanceof Map) {\n                    value.set(k, v);\n                }\n                else if (value instanceof Set) {\n                    value.add(v);\n                }\n                else if (isObject(value)) {\n                    if (v !== undefined || k in value)\n                        value[k] = v;\n                }\n            }\n        }\n    }\n    if (status !== 'not_valid') {\n        for (const failure of struct.refiner(value, ctx)) {\n            failure.explanation = options.message;\n            status = 'not_refined';\n            yield [failure, undefined];\n        }\n    }\n    if (status === 'valid') {\n        yield [undefined, value];\n    }\n}\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\nclass Struct {\n    constructor(props) {\n        const { type, schema, validator, refiner, coercer = (value) => value, entries = function* () { }, } = props;\n        this.type = type;\n        this.schema = schema;\n        this.entries = entries;\n        this.coercer = coercer;\n        if (validator) {\n            this.validator = (value, context) => {\n                const result = validator(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.validator = () => [];\n        }\n        if (refiner) {\n            this.refiner = (value, context) => {\n                const result = refiner(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.refiner = () => [];\n        }\n    }\n    /**\n     * Assert that a value passes the struct's validation, throwing if it doesn't.\n     */\n    assert(value, message) {\n        return assert(value, this, message);\n    }\n    /**\n     * Create a value with the struct's coercion logic, then validate it.\n     */\n    create(value, message) {\n        return create(value, this, message);\n    }\n    /**\n     * Check if a value passes the struct's validation.\n     */\n    is(value) {\n        return is(value, this);\n    }\n    /**\n     * Mask a value, coercing and validating it, but returning only the subset of\n     * properties defined by the struct's schema.\n     */\n    mask(value, message) {\n        return mask(value, this, message);\n    }\n    /**\n     * Validate a value with the struct's validation logic, returning a tuple\n     * representing the result.\n     *\n     * You may optionally pass `true` for the `withCoercion` argument to coerce\n     * the value before attempting to validate it. If you do, the result will\n     * contain the coerced result when successful.\n     */\n    validate(value, options = {}) {\n        return validate(value, this, options);\n    }\n}\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\nfunction assert(value, struct, message) {\n    const result = validate(value, struct, { message });\n    if (result[0]) {\n        throw result[0];\n    }\n}\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\nfunction create(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\nfunction mask(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, mask: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Check if a value passes a struct.\n */\nfunction is(value, struct) {\n    const result = validate(value, struct);\n    return !result[0];\n}\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\nfunction validate(value, struct, options = {}) {\n    const tuples = run(value, struct, options);\n    const tuple = shiftIterator(tuples);\n    if (tuple[0]) {\n        const error = new StructError(tuple[0], function* () {\n            for (const t of tuples) {\n                if (t[0]) {\n                    yield t[0];\n                }\n            }\n        });\n        return [error, undefined];\n    }\n    else {\n        const v = tuple[1];\n        return [undefined, v];\n    }\n}\n\nfunction assign(...Structs) {\n    const isType = Structs[0].type === 'type';\n    const schemas = Structs.map((s) => s.schema);\n    const schema = Object.assign({}, ...schemas);\n    return isType ? type(schema) : object(schema);\n}\n/**\n * Define a new struct type with a custom validation function.\n */\nfunction define(name, validator) {\n    return new Struct({ type: name, schema: null, validator });\n}\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\nfunction deprecated(struct, log) {\n    return new Struct({\n        ...struct,\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n        validator(value, ctx) {\n            if (value === undefined) {\n                return true;\n            }\n            else {\n                log(value, ctx);\n                return struct.validator(value, ctx);\n            }\n        },\n    });\n}\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\nfunction dynamic(fn) {\n    return new Struct({\n        type: 'dynamic',\n        schema: null,\n        *entries(value, ctx) {\n            const struct = fn(value, ctx);\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\nfunction lazy(fn) {\n    let struct;\n    return new Struct({\n        type: 'lazy',\n        schema: null,\n        *entries(value, ctx) {\n            struct ?? (struct = fn());\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\nfunction omit(struct, keys) {\n    const { schema } = struct;\n    const subschema = { ...schema };\n    for (const key of keys) {\n        delete subschema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\nfunction partial(struct) {\n    const isStruct = struct instanceof Struct;\n    const schema = isStruct ? { ...struct.schema } : { ...struct };\n    for (const key in schema) {\n        schema[key] = optional(schema[key]);\n    }\n    if (isStruct && struct.type === 'type') {\n        return type(schema);\n    }\n    return object(schema);\n}\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\nfunction pick(struct, keys) {\n    const { schema } = struct;\n    const subschema = {};\n    for (const key of keys) {\n        subschema[key] = schema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\nfunction struct(name, validator) {\n    console.warn('superstruct@0.11 - The `struct` helper has been renamed to `define`.');\n    return define(name, validator);\n}\n\n/**\n * Ensure that any value passes validation.\n */\nfunction any() {\n    return define('any', () => true);\n}\nfunction array(Element) {\n    return new Struct({\n        type: 'array',\n        schema: Element,\n        *entries(value) {\n            if (Element && Array.isArray(value)) {\n                for (const [i, v] of value.entries()) {\n                    yield [i, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return Array.isArray(value) ? value.slice() : value;\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array value, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a bigint.\n */\nfunction bigint() {\n    return define('bigint', (value) => {\n        return typeof value === 'bigint';\n    });\n}\n/**\n * Ensure that a value is a boolean.\n */\nfunction boolean() {\n    return define('boolean', (value) => {\n        return typeof value === 'boolean';\n    });\n}\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\nfunction date() {\n    return define('date', (value) => {\n        return ((value instanceof Date && !isNaN(value.getTime())) ||\n            `Expected a valid \\`Date\\` object, but received: ${print(value)}`);\n    });\n}\nfunction enums(values) {\n    const schema = {};\n    const description = values.map((v) => print(v)).join();\n    for (const key of values) {\n        schema[key] = key;\n    }\n    return new Struct({\n        type: 'enums',\n        schema,\n        validator(value) {\n            return (values.includes(value) ||\n                `Expected one of \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a function.\n */\nfunction func() {\n    return define('func', (value) => {\n        return (typeof value === 'function' ||\n            `Expected a function, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an instance of a specific class.\n */\nfunction instance(Class) {\n    return define('instance', (value) => {\n        return (value instanceof Class ||\n            `Expected a \\`${Class.name}\\` instance, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an integer.\n */\nfunction integer() {\n    return define('integer', (value) => {\n        return ((typeof value === 'number' && !isNaN(value) && Number.isInteger(value)) ||\n            `Expected an integer, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value matches all of a set of types.\n */\nfunction intersection(Structs) {\n    return new Struct({\n        type: 'intersection',\n        schema: null,\n        *entries(value, ctx) {\n            for (const S of Structs) {\n                yield* S.entries(value, ctx);\n            }\n        },\n        *validator(value, ctx) {\n            for (const S of Structs) {\n                yield* S.validator(value, ctx);\n            }\n        },\n        *refiner(value, ctx) {\n            for (const S of Structs) {\n                yield* S.refiner(value, ctx);\n            }\n        },\n    });\n}\nfunction literal(constant) {\n    const description = print(constant);\n    const t = typeof constant;\n    return new Struct({\n        type: 'literal',\n        schema: t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n        validator(value) {\n            return (value === constant ||\n                `Expected the literal \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\nfunction map(Key, Value) {\n    return new Struct({\n        type: 'map',\n        schema: null,\n        *entries(value) {\n            if (Key && Value && value instanceof Map) {\n                for (const [k, v] of value.entries()) {\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Map ? new Map(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Map ||\n                `Expected a \\`Map\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that no value ever passes validation.\n */\nfunction never() {\n    return define('never', () => false);\n}\n/**\n * Augment an existing struct to allow `null` values.\n */\nfunction nullable(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === null || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === null || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is a number.\n */\nfunction number() {\n    return define('number', (value) => {\n        return ((typeof value === 'number' && !isNaN(value)) ||\n            `Expected a number, but received: ${print(value)}`);\n    });\n}\nfunction object(schema) {\n    const knowns = schema ? Object.keys(schema) : [];\n    const Never = never();\n    return new Struct({\n        type: 'object',\n        schema: schema ? schema : null,\n        *entries(value) {\n            if (schema && isObject(value)) {\n                const unknowns = new Set(Object.keys(value));\n                for (const key of knowns) {\n                    unknowns.delete(key);\n                    yield [key, value[key], schema[key]];\n                }\n                for (const key of unknowns) {\n                    yield [key, value[key], Never];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Augment a struct to allow `undefined` values.\n */\nfunction optional(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === undefined || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\nfunction record(Key, Value) {\n    return new Struct({\n        type: 'record',\n        schema: null,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k in value) {\n                    const v = value[k];\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\nfunction regexp() {\n    return define('regexp', (value) => {\n        return value instanceof RegExp;\n    });\n}\nfunction set(Element) {\n    return new Struct({\n        type: 'set',\n        schema: null,\n        *entries(value) {\n            if (Element && value instanceof Set) {\n                for (const v of value) {\n                    yield [v, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Set ? new Set(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Set ||\n                `Expected a \\`Set\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a string.\n */\nfunction string() {\n    return define('string', (value) => {\n        return (typeof value === 'string' ||\n            `Expected a string, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\nfunction tuple(Structs) {\n    const Never = never();\n    return new Struct({\n        type: 'tuple',\n        schema: null,\n        *entries(value) {\n            if (Array.isArray(value)) {\n                const length = Math.max(Structs.length, value.length);\n                for (let i = 0; i < length; i++) {\n                    yield [i, value[i], Structs[i] || Never];\n                }\n            }\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\nfunction type(schema) {\n    const keys = Object.keys(schema);\n    return new Struct({\n        type: 'type',\n        schema,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k of keys) {\n                    yield [k, value[k], schema[k]];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Ensure that a value matches one of a set of types.\n */\nfunction union(Structs) {\n    const description = Structs.map((s) => s.type).join(' | ');\n    return new Struct({\n        type: 'union',\n        schema: null,\n        coercer(value) {\n            for (const S of Structs) {\n                const [error, coerced] = S.validate(value, { coerce: true });\n                if (!error) {\n                    return coerced;\n                }\n            }\n            return value;\n        },\n        validator(value, ctx) {\n            const failures = [];\n            for (const S of Structs) {\n                const [...tuples] = run(value, S, ctx);\n                const [first] = tuples;\n                if (!first[0]) {\n                    return [];\n                }\n                else {\n                    for (const [failure] of tuples) {\n                        if (failure) {\n                            failures.push(failure);\n                        }\n                    }\n                }\n            }\n            return [\n                `Expected the value to satisfy a union of \\`${description}\\`, but received: ${print(value)}`,\n                ...failures,\n            ];\n        },\n    });\n}\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\nfunction unknown() {\n    return define('unknown', () => true);\n}\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction coerce(struct, condition, coercer) {\n    return new Struct({\n        ...struct,\n        coercer: (value, ctx) => {\n            return is(value, condition)\n                ? struct.coercer(coercer(value, ctx), ctx)\n                : struct.coercer(value, ctx);\n        },\n    });\n}\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction defaulted(struct, fallback, options = {}) {\n    return coerce(struct, unknown(), (x) => {\n        const f = typeof fallback === 'function' ? fallback() : fallback;\n        if (x === undefined) {\n            return f;\n        }\n        if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n            const ret = { ...x };\n            let changed = false;\n            for (const key in f) {\n                if (ret[key] === undefined) {\n                    ret[key] = f[key];\n                    changed = true;\n                }\n            }\n            if (changed) {\n                return ret;\n            }\n        }\n        return x;\n    });\n}\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction trimmed(struct) {\n    return coerce(struct, string(), (x) => x.trim());\n}\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\nfunction empty(struct) {\n    return refine(struct, 'empty', (value) => {\n        const size = getSize(value);\n        return (size === 0 ||\n            `Expected an empty ${struct.type} but received one with a size of \\`${size}\\``);\n    });\n}\nfunction getSize(value) {\n    if (value instanceof Map || value instanceof Set) {\n        return value.size;\n    }\n    else {\n        return value.length;\n    }\n}\n/**\n * Ensure that a number or date is below a threshold.\n */\nfunction max(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'max', (value) => {\n        return exclusive\n            ? value < threshold\n            : value <= threshold ||\n                `Expected a ${struct.type} less than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a number or date is above a threshold.\n */\nfunction min(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'min', (value) => {\n        return exclusive\n            ? value > threshold\n            : value >= threshold ||\n                `Expected a ${struct.type} greater than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a string, array, map or set is not empty.\n */\nfunction nonempty(struct) {\n    return refine(struct, 'nonempty', (value) => {\n        const size = getSize(value);\n        return (size > 0 || `Expected a nonempty ${struct.type} but received an empty one`);\n    });\n}\n/**\n * Ensure that a string matches a regular expression.\n */\nfunction pattern(struct, regexp) {\n    return refine(struct, 'pattern', (value) => {\n        return (regexp.test(value) ||\n            `Expected a ${struct.type} matching \\`/${regexp.source}/\\` but received \"${value}\"`);\n    });\n}\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\nfunction size(struct, min, max = min) {\n    const expected = `Expected a ${struct.type}`;\n    const of = min === max ? `of \\`${min}\\`` : `between \\`${min}\\` and \\`${max}\\``;\n    return refine(struct, 'size', (value) => {\n        if (typeof value === 'number' || value instanceof Date) {\n            return ((min <= value && value <= max) ||\n                `${expected} ${of} but received \\`${value}\\``);\n        }\n        else if (value instanceof Map || value instanceof Set) {\n            const { size } = value;\n            return ((min <= size && size <= max) ||\n                `${expected} with a size ${of} but received one with a size of \\`${size}\\``);\n        }\n        else {\n            const { length } = value;\n            return ((min <= length && length <= max) ||\n                `${expected} with a length ${of} but received one with a length of \\`${length}\\``);\n        }\n    });\n}\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\nfunction refine(struct, name, refiner) {\n    return new Struct({\n        ...struct,\n        *refiner(value, ctx) {\n            yield* struct.refiner(value, ctx);\n            const result = refiner(value, ctx);\n            const failures = toFailures(result, ctx, struct, value);\n            for (const failure of failures) {\n                yield { ...failure, refinement: name };\n            }\n        },\n    });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/superstruct/dist/index.mjs\n");

/***/ })

};
;