'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import Link from 'next/link';
import { checkProfileStatus } from '@/lib/profileStatus';

export default function ProfileStatusTestPage() {
  const { address } = useAppKitAccount();
  const [profileName, setProfileName] = useState('');
  const [statusResult, setStatusResult] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkStatus = async () => {
    if (!profileName) return;

    setIsChecking(true);
    try {
      const result = await checkProfileStatus(profileName);
      setStatusResult(result);
    } catch (error) {
      console.error('Error checking status:', error);
      setStatusResult({ error: 'Failed to check status' });
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-2xl font-bold mb-6">Profile Status Test</h1>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="mb-4">
          <label htmlFor="profileName" className="block text-sm font-medium mb-2">
            Profile Name or Address
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              id="profileName"
              value={profileName}
              onChange={(e) => setProfileName(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
              placeholder="Enter profile name or address"
            />
            <button
              onClick={checkStatus}
              disabled={isChecking || !profileName}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isChecking ? 'Checking...' : 'Check Status'}
            </button>
          </div>
        </div>

        {statusResult && (
          <div className="mt-6 p-4 border rounded-md">
            <h2 className="text-lg font-semibold mb-2">Status Result</h2>
            <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto">
              {JSON.stringify(statusResult, null, 2)}
            </pre>

            {statusResult.status && (
              <div className="mt-4">
                <p className="mb-2">
                  <strong>Status:</strong>{' '}
                  <span className={`px-2 py-1 rounded ${
                    statusResult.isApproved ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                    'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                  }`}>
                    {statusResult.status}
                  </span>
                </p>
                <p className="mb-2"><strong>Approved:</strong> {statusResult.isApproved ? 'Yes' : 'No'}</p>
                <p><strong>Message:</strong> {statusResult.message}</p>

                {statusResult.status !== 'not-found' && (
                  <div className="mt-4">
                    <Link
                      href={`/${profileName}`}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-block"
                    >
                      Visit Profile
                    </Link>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
