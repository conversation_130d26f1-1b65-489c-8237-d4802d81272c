'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';

interface SimpleWalletValidatorProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
}

// A simplified version of WalletValidator that only checks if the wallet is connected
// without checking the profile status
export default function SimpleWalletValidator({
  children,
  fallbackContent
}: SimpleWalletValidatorProps) {
  const { isConnected } = useAppKitAccount();
  const [isInitializing, setIsInitializing] = useState(true);

  // Handle wallet initialization state
  useEffect(() => {
    // Give the wallet some time to initialize
    const timer = setTimeout(() => {
      setIsInitializing(false);
    }, 1000); // Wait 1 second for wallet to initialize

    return () => clearTimeout(timer);
  }, []);

  // Empty fallback content
  const emptyFallbackContent = (
    <div className="mt-8 max-w-md mx-auto">
      <h2 className="text-lg font-semibold mb-4">Wallet Connection Required</h2>
      <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
        Please connect your wallet to continue.
      </div>
    </div>
  );

  return (
    <>
      {isInitializing ? (
        // Show loading state during wallet initialization
        <div className="flex flex-col items-center justify-center min-h-[200px] p-6">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500 mb-2" />
          <p className="text-neutral-400 text-sm">Initializing...</p>
        </div>
      ) : isConnected ? (
        children
      ) : (
        fallbackContent || emptyFallbackContent
      )}
    </>
  );
}
