import type { Metada<PERSON> } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import SimpleNavbar from "@/components/SimpleNavbar";
import Providers from "@/components/Providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Web3Socials Profiles - View Only",
  description: "View Web3 profiles",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased overflow-x-hidden`}
      >
        <Providers>
          <div className="relative min-h-screen">
            <SimpleNavbar />
            <div style={{ height: '48px' }}></div>
            {children}
          </div>
        </Providers>
        <Toaster />
      </body>
    </html>
  );
}
