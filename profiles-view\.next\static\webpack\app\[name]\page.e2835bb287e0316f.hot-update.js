"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[name]/page",{

/***/ "(app-pages-browser)/./app/components/renders/render_sociallinks.tsx":
/*!*******************************************************!*\
  !*** ./app/components/renders/render_sociallinks.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderSocialLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_ui_floating_dock__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/floating-dock */ \"(app-pages-browser)/./components/ui/floating-dock.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction RenderSocialLinks(param) {\n    let { profileData, componentData, showPositionLabel = false } = param;\n    _s();\n    const [referralCode, setReferralCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [referralCount, setReferralCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderSocialLinks.useEffect\": ()=>{\n            const fetchReferralData = {\n                \"RenderSocialLinks.useEffect.fetchReferralData\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/referral/\".concat(profileData.address));\n                        if (response.ok) {\n                            const data = await response.json();\n                            setReferralCode(data.referralCode);\n                            setReferralCount(data.referralCount || 0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching referral data:', error);\n                    }\n                }\n            }[\"RenderSocialLinks.useEffect.fetchReferralData\"];\n            if (profileData.address) {\n                fetchReferralData();\n            }\n        }\n    }[\"RenderSocialLinks.useEffect\"], [\n        profileData.address\n    ]);\n    const copyReferralCode = async ()=>{\n        if (referralCode) {\n            try {\n                await navigator.clipboard.writeText(referralCode);\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (error) {\n                console.error('Failed to copy referral code:', error);\n            }\n        }\n    };\n    // Helper function to get social media icons\n    const getSocialIcon = (platform)=>{\n        // Simple text-based icons for view-only mode\n        const platformIcons = {\n            twitter: '🐦',\n            github: '🐙',\n            linkedin: '💼',\n            instagram: '📷',\n            website: '🌐',\n            email: '📧',\n            youtube: '📺',\n            twitch: '🎮',\n            telegram: '✈️',\n            discord: '💬',\n            facebook: '📘',\n            cro: '💎'\n        };\n        return platformIcons[platform.toLowerCase()] || '🔗';\n    };\n    // Format social links if they exist\n    const formatSocialLinks = ()=>{\n        if (!componentData.socialLinks) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-1 text-center py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-neutral-400\",\n                    children: \"No social links added yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this);\n        }\n        try {\n            const links = typeof componentData.socialLinks === 'string' ? JSON.parse(componentData.socialLinks) : componentData.socialLinks;\n            if (!links || Object.keys(links).length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this);\n            }\n            // Format links for display\n            const formattedLinks = Object.entries(links).filter((param)=>{\n                let [_, url] = param;\n                return url;\n            }) // Filter out empty URLs\n            .map((param)=>{\n                let [platform, url] = param;\n                const urlStr = String(url);\n                const isCustom = platform.startsWith('custom');\n                let customLabel = '';\n                let finalUrl = urlStr;\n                if (isCustom && urlStr.includes('|')) {\n                    const parts = urlStr.split('|');\n                    if (parts.length >= 2) {\n                        customLabel = parts[0].trim();\n                        finalUrl = parts.slice(1).join('|').trim();\n                    }\n                }\n                const displayName = isCustom ? customLabel || 'Custom Link' : platform;\n                const icon = getSocialIcon(platform);\n                return {\n                    platform,\n                    url: finalUrl,\n                    displayName,\n                    icon\n                };\n            });\n            if (formattedLinks.length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    style: {\n                        color: componentData.fontColor || undefined\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this);\n            }\n            // Transform links for FloatingDock\n            const dockItems = formattedLinks.map((link)=>({\n                    title: link.displayName,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: link.icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 15\n                    }, this),\n                    href: link.url,\n                    platform: link.platform\n                }));\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-4 p-4\",\n                style: {\n                    color: componentData.fontColor || undefined\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_floating_dock__WEBPACK_IMPORTED_MODULE_2__.FloatingDock, {\n                        items: dockItems,\n                        backgroundColor: componentData.backgroundColor || 'rgba(255, 255, 255, 0.1)',\n                        fontColor: componentData.fontColor || '#ffffff'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    referralCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-neutral-800/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-400 mb-2\",\n                                    children: \"Referral Code\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"px-3 py-1 bg-neutral-700 rounded text-sm font-mono\",\n                                            children: referralCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: copyReferralCode,\n                                            className: \"p-1 hover:bg-neutral-600 rounded transition-colors\",\n                                            title: \"Copy referral code\",\n                                            children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this),\n                                referralCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-500 mt-1\",\n                                    children: [\n                                        referralCount,\n                                        \" referral\",\n                                        referralCount !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this);\n        } catch (error) {\n            console.error('Error parsing social links:', error);\n            return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 w-full\",\n        style: {\n            backgroundColor: componentData.backgroundColor || 'transparent'\n        },\n        children: [\n            formatSocialLinks(),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-blue-900/30 text-blue-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Social Links\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(RenderSocialLinks, \"cy4LSJhrOGO6QACr8aVRkLnRPuU=\");\n_c = RenderSocialLinks;\nvar _c;\n$RefreshReg$(_c, \"RenderSocialLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/renders/render_sociallinks.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/floating-dock.tsx":
/*!*****************************************!*\
  !*** ./components/ui/floating-dock.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingDock: () => (/* binding */ FloatingDock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ FloatingDock auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst FloatingDock = (param)=>{\n    let { items, desktopClassName, mobileClassName, backgroundColor, fontColor } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex flex-wrap justify-center gap-2 sm:gap-2 md:gap-3 lg:gap-3\", desktopClassName),\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: item.href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105\",\n                    style: {\n                        backgroundColor: backgroundColor || 'rgba(255, 255, 255, 0.1)',\n                        color: fontColor || '#ffffff',\n                        border: '1px solid rgba(255, 255, 255, 0.2)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: item.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium whitespace-nowrap\",\n                            children: item.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 21\n                }, undefined)\n            }, item.title, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 30,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 28,\n        columnNumber: 9\n    }, undefined);\n};\n_c = FloatingDock;\nconst FloatingDockDesktop = (param)=>{\n    let { items, className } = param;\n    _s();\n    let mouseX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useMotionValue)(Infinity);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        onMouseMove: (e)=>mouseX.set(e.pageX),\n        onMouseLeave: ()=>mouseX.set(Infinity),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"mx-auto hidden h-16 items-end gap-4 rounded-2xl bg-gray-50 px-4 pb-3 md:flex dark:bg-neutral-900\", className),\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconContainer, {\n                mouseX: mouseX,\n                ...item\n            }, item.title, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 74,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 65,\n        columnNumber: 9\n    }, undefined);\n};\n_s(FloatingDockDesktop, \"Srbxi5whgdQe76YJROaLRJds4Bo=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useMotionValue\n    ];\n});\n_c1 = FloatingDockDesktop;\nfunction IconContainer(param) {\n    let { mouseX, title, icon, href } = param;\n    _s1();\n    let ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    let distance = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(mouseX, {\n        \"IconContainer.useTransform[distance]\": (val)=>{\n            var _ref_current;\n            var _ref_current_getBoundingClientRect;\n            let bounds = (_ref_current_getBoundingClientRect = (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.getBoundingClientRect()) !== null && _ref_current_getBoundingClientRect !== void 0 ? _ref_current_getBoundingClientRect : {\n                x: 0,\n                width: 0\n            };\n            return val - bounds.x - bounds.width / 2;\n        }\n    }[\"IconContainer.useTransform[distance]\"]);\n    let widthTransform = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        40,\n        80,\n        40\n    ]);\n    let heightTransform = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        40,\n        80,\n        40\n    ]);\n    let widthTransformIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        20,\n        40,\n        20\n    ]);\n    let heightTransformIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        20,\n        40,\n        20\n    ]);\n    let width = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(widthTransform, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    let height = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(heightTransform, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    let widthIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(widthTransformIcon, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    let heightIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(heightTransformIcon, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            ref: ref,\n            style: {\n                width,\n                height\n            },\n            onMouseEnter: ()=>setHovered(true),\n            onMouseLeave: ()=>setHovered(false),\n            className: \"relative flex aspect-square items-center justify-center rounded-full bg-transparent border border-neutral-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10,\n                            x: \"-50%\"\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            x: \"-50%\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 2,\n                            x: \"-50%\"\n                        },\n                        className: \"absolute -top-8 left-1/2 w-fit rounded-md border border-gray-200 bg-gray-100 px-2 py-0.5 text-xs whitespace-pre text-neutral-700 dark:border-neutral-900 dark:bg-neutral-800 dark:text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    style: {\n                        width: widthIcon,\n                        height: heightIcon\n                    },\n                    className: \"flex items-center justify-center\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n            lineNumber: 131,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 130,\n        columnNumber: 9\n    }, this);\n}\n_s1(IconContainer, \"Pfynze8Bbg3fgsXz64qKc5rSXco=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring\n    ];\n});\n_c2 = IconContainer;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FloatingDock\");\n$RefreshReg$(_c1, \"FloatingDockDesktop\");\n$RefreshReg$(_c2, \"IconContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/floating-dock.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: () => (/* binding */ PopChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* __next_internal_client_entry_do_not_use__ PopChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */ class PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const parent = element.offsetParent;\n            const parentWidth = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(parent) ? parent.offsetWidth || 0 : 0;\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n            size.right = parentWidth - size.width - size.left;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */ componentDidUpdate() {}\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild(param) {\n    let { children, isPresent, anchorX } = param;\n    _s();\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        right: 0\n    });\n    const { nonce } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect)({\n        \"PopChild.useInsertionEffect\": ()=>{\n            const { width, height, top, left, right } = size.current;\n            if (isPresent || !ref.current || !width || !height) return;\n            const x = anchorX === \"left\" ? \"left: \".concat(left) : \"right: \".concat(right);\n            ref.current.dataset.motionPopId = id;\n            const style = document.createElement(\"style\");\n            if (nonce) style.nonce = nonce;\n            document.head.appendChild(style);\n            if (style.sheet) {\n                style.sheet.insertRule('\\n          [data-motion-pop-id=\"'.concat(id, '\"] {\\n            position: absolute !important;\\n            width: ').concat(width, \"px !important;\\n            height: \").concat(height, \"px !important;\\n            \").concat(x, \"px !important;\\n            top: \").concat(top, \"px !important;\\n          }\\n        \"));\n            }\n            return ({\n                \"PopChild.useInsertionEffect\": ()=>{\n                    if (document.head.contains(style)) {\n                        document.head.removeChild(style);\n                    }\n                }\n            })[\"PopChild.useInsertionEffect\"];\n        }\n    }[\"PopChild.useInsertionEffect\"], [\n        isPresent\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PopChildMeasure, {\n        isPresent: isPresent,\n        childRef: ref,\n        sizeRef: size,\n        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n            ref\n        })\n    });\n}\n_s(PopChild, \"V7z789Ed2n0+HnmYCJ8kEL0I644=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_1__.useId,\n        react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect\n    ];\n});\n_c = PopChild;\n\nvar _c;\n$RefreshReg$(_c, \"PopChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: () => (/* binding */ PresenceChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n/* __next_internal_client_entry_do_not_use__ PresenceChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\nconst PresenceChild = (param)=>{\n    let { children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, anchorX } = param;\n    _s();\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    let isReusedContext = true;\n    let context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo[context]\": ()=>{\n            isReusedContext = false;\n            return {\n                id,\n                initial,\n                isPresent,\n                custom,\n                onExitComplete: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, true);\n                        for (const isComplete of presenceChildren.values()){\n                            if (!isComplete) return; // can stop searching when any is incomplete\n                        }\n                        onExitComplete && onExitComplete();\n                    }\n                })[\"PresenceChild.useMemo[context]\"],\n                register: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, false);\n                        return ({\n                            \"PresenceChild.useMemo[context]\": ()=>presenceChildren.delete(childId)\n                        })[\"PresenceChild.useMemo[context]\"];\n                    }\n                })[\"PresenceChild.useMemo[context]\"]\n            };\n        }\n    }[\"PresenceChild.useMemo[context]\"], [\n        isPresent,\n        presenceChildren,\n        onExitComplete\n    ]);\n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */ if (presenceAffectsLayout && isReusedContext) {\n        context = {\n            ...context\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo\": ()=>{\n            presenceChildren.forEach({\n                \"PresenceChild.useMemo\": (_, key)=>presenceChildren.set(key, false)\n            }[\"PresenceChild.useMemo\"]);\n        }\n    }[\"PresenceChild.useMemo\"], [\n        isPresent\n    ]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */ react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PresenceChild.useEffect\": ()=>{\n            !isPresent && !presenceChildren.size && onExitComplete && onExitComplete();\n        }\n    }[\"PresenceChild.useEffect\"], [\n        isPresent\n    ]);\n    if (mode === \"popLayout\") {\n        children = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__.PopChild, {\n            isPresent: isPresent,\n            anchorX: anchorX,\n            children: children\n        });\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__.PresenceContext.Provider, {\n        value: context,\n        children: children\n    });\n};\n_s(PresenceChild, \"LuJRAK72iQdFH7nv0cJ6ZjdNWv8=\", false, function() {\n    return [\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant,\n        react__WEBPACK_IMPORTED_MODULE_1__.useId\n    ];\n});\n_c = PresenceChild;\nfunction newChildrenMap() {\n    return new Map();\n}\n\nvar _c;\n$RefreshReg$(_c, \"PresenceChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvUHJlc2VuY2VDaGlsZC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN3QztBQUNUO0FBQ1E7QUFDNkI7QUFDVDtBQUNqQjtBQUUxQyxNQUFNTyxnQkFBZ0I7UUFBQyxFQUFFQyxRQUFRLEVBQUVDLE9BQU8sRUFBRUMsU0FBUyxFQUFFQyxjQUFjLEVBQUVDLE1BQU0sRUFBRUMscUJBQXFCLEVBQUVDLElBQUksRUFBRUMsT0FBTyxFQUFHOztJQUNsSCxNQUFNQyxtQkFBbUJYLG9FQUFXQSxDQUFDWTtJQUNyQyxNQUFNQyxLQUFLaEIsNENBQUtBO0lBQ2hCLElBQUlpQixrQkFBa0I7SUFDdEIsSUFBSUMsVUFBVWpCLDhDQUFPQTswQ0FBQztZQUNsQmdCLGtCQUFrQjtZQUNsQixPQUFPO2dCQUNIRDtnQkFDQVQ7Z0JBQ0FDO2dCQUNBRTtnQkFDQUQsY0FBYztzREFBRSxDQUFDVTt3QkFDYkwsaUJBQWlCTSxHQUFHLENBQUNELFNBQVM7d0JBQzlCLEtBQUssTUFBTUUsY0FBY1AsaUJBQWlCUSxNQUFNLEdBQUk7NEJBQ2hELElBQUksQ0FBQ0QsWUFDRCxRQUFRLDRDQUE0Qzt3QkFDNUQ7d0JBQ0FaLGtCQUFrQkE7b0JBQ3RCOztnQkFDQWMsUUFBUTtzREFBRSxDQUFDSjt3QkFDUEwsaUJBQWlCTSxHQUFHLENBQUNELFNBQVM7d0JBQzlCOzhEQUFPLElBQU1MLGlCQUFpQlUsTUFBTSxDQUFDTDs7b0JBQ3pDOztZQUNKO1FBQ0o7eUNBQUc7UUFBQ1g7UUFBV007UUFBa0JMO0tBQWU7SUFDaEQ7Ozs7S0FJQyxHQUNELElBQUlFLHlCQUF5Qk0saUJBQWlCO1FBQzFDQyxVQUFVO1lBQUUsR0FBR0EsT0FBTztRQUFDO0lBQzNCO0lBQ0FqQiw4Q0FBT0E7aUNBQUM7WUFDSmEsaUJBQWlCVyxPQUFPO3lDQUFDLENBQUNDLEdBQUdDLE1BQVFiLGlCQUFpQk0sR0FBRyxDQUFDTyxLQUFLOztRQUNuRTtnQ0FBRztRQUFDbkI7S0FBVTtJQUNkOzs7S0FHQyxHQUNEVCw0Q0FBZTttQ0FBQztZQUNaLENBQUNTLGFBQ0csQ0FBQ00saUJBQWlCZSxJQUFJLElBQ3RCcEIsa0JBQ0FBO1FBQ1I7a0NBQUc7UUFBQ0Q7S0FBVTtJQUNkLElBQUlJLFNBQVMsYUFBYTtRQUN0Qk4sV0FBWVIsc0RBQUdBLENBQUNNLG1EQUFRQSxFQUFFO1lBQUVJLFdBQVdBO1lBQVdLLFNBQVNBO1lBQVNQLFVBQVVBO1FBQVM7SUFDM0Y7SUFDQSxPQUFRUixzREFBR0EsQ0FBQ0kseUVBQWVBLENBQUM0QixRQUFRLEVBQUU7UUFBRUMsT0FBT2I7UUFBU1osVUFBVUE7SUFBUztBQUMvRTtHQWxETUQ7O1FBQ3VCRixnRUFBV0E7UUFDekJILHdDQUFLQTs7O0tBRmRLO0FBbUROLFNBQVNVO0lBQ0wsT0FBTyxJQUFJaUI7QUFDZjtBQUV5QiIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcY29tcG9uZW50c1xcQW5pbWF0ZVByZXNlbmNlXFxQcmVzZW5jZUNoaWxkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gJ3JlYWN0L2pzeC1ydW50aW1lJztcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUlkLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUHJlc2VuY2VDb250ZXh0IH0gZnJvbSAnLi4vLi4vY29udGV4dC9QcmVzZW5jZUNvbnRleHQubWpzJztcbmltcG9ydCB7IHVzZUNvbnN0YW50IH0gZnJvbSAnLi4vLi4vdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyc7XG5pbXBvcnQgeyBQb3BDaGlsZCB9IGZyb20gJy4vUG9wQ2hpbGQubWpzJztcblxuY29uc3QgUHJlc2VuY2VDaGlsZCA9ICh7IGNoaWxkcmVuLCBpbml0aWFsLCBpc1ByZXNlbnQsIG9uRXhpdENvbXBsZXRlLCBjdXN0b20sIHByZXNlbmNlQWZmZWN0c0xheW91dCwgbW9kZSwgYW5jaG9yWCwgfSkgPT4ge1xuICAgIGNvbnN0IHByZXNlbmNlQ2hpbGRyZW4gPSB1c2VDb25zdGFudChuZXdDaGlsZHJlbk1hcCk7XG4gICAgY29uc3QgaWQgPSB1c2VJZCgpO1xuICAgIGxldCBpc1JldXNlZENvbnRleHQgPSB0cnVlO1xuICAgIGxldCBjb250ZXh0ID0gdXNlTWVtbygoKSA9PiB7XG4gICAgICAgIGlzUmV1c2VkQ29udGV4dCA9IGZhbHNlO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICBpbml0aWFsLFxuICAgICAgICAgICAgaXNQcmVzZW50LFxuICAgICAgICAgICAgY3VzdG9tLFxuICAgICAgICAgICAgb25FeGl0Q29tcGxldGU6IChjaGlsZElkKSA9PiB7XG4gICAgICAgICAgICAgICAgcHJlc2VuY2VDaGlsZHJlbi5zZXQoY2hpbGRJZCwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBpc0NvbXBsZXRlIG9mIHByZXNlbmNlQ2hpbGRyZW4udmFsdWVzKCkpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFpc0NvbXBsZXRlKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuOyAvLyBjYW4gc3RvcCBzZWFyY2hpbmcgd2hlbiBhbnkgaXMgaW5jb21wbGV0ZVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBvbkV4aXRDb21wbGV0ZSAmJiBvbkV4aXRDb21wbGV0ZSgpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHJlZ2lzdGVyOiAoY2hpbGRJZCkgPT4ge1xuICAgICAgICAgICAgICAgIHByZXNlbmNlQ2hpbGRyZW4uc2V0KGNoaWxkSWQsIGZhbHNlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gKCkgPT4gcHJlc2VuY2VDaGlsZHJlbi5kZWxldGUoY2hpbGRJZCk7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH0sIFtpc1ByZXNlbnQsIHByZXNlbmNlQ2hpbGRyZW4sIG9uRXhpdENvbXBsZXRlXSk7XG4gICAgLyoqXG4gICAgICogSWYgdGhlIHByZXNlbmNlIG9mIGEgY2hpbGQgYWZmZWN0cyB0aGUgbGF5b3V0IG9mIHRoZSBjb21wb25lbnRzIGFyb3VuZCBpdCxcbiAgICAgKiB3ZSB3YW50IHRvIG1ha2UgYSBuZXcgY29udGV4dCB2YWx1ZSB0byBlbnN1cmUgdGhleSBnZXQgcmUtcmVuZGVyZWRcbiAgICAgKiBzbyB0aGV5IGNhbiBkZXRlY3QgdGhhdCBsYXlvdXQgY2hhbmdlLlxuICAgICAqL1xuICAgIGlmIChwcmVzZW5jZUFmZmVjdHNMYXlvdXQgJiYgaXNSZXVzZWRDb250ZXh0KSB7XG4gICAgICAgIGNvbnRleHQgPSB7IC4uLmNvbnRleHQgfTtcbiAgICB9XG4gICAgdXNlTWVtbygoKSA9PiB7XG4gICAgICAgIHByZXNlbmNlQ2hpbGRyZW4uZm9yRWFjaCgoXywga2V5KSA9PiBwcmVzZW5jZUNoaWxkcmVuLnNldChrZXksIGZhbHNlKSk7XG4gICAgfSwgW2lzUHJlc2VudF0pO1xuICAgIC8qKlxuICAgICAqIElmIHRoZXJlJ3Mgbm8gYG1vdGlvbmAgY29tcG9uZW50cyB0byBmaXJlIGV4aXQgYW5pbWF0aW9ucywgd2Ugd2FudCB0byByZW1vdmUgdGhpc1xuICAgICAqIGNvbXBvbmVudCBpbW1lZGlhdGVseS5cbiAgICAgKi9cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICAhaXNQcmVzZW50ICYmXG4gICAgICAgICAgICAhcHJlc2VuY2VDaGlsZHJlbi5zaXplICYmXG4gICAgICAgICAgICBvbkV4aXRDb21wbGV0ZSAmJlxuICAgICAgICAgICAgb25FeGl0Q29tcGxldGUoKTtcbiAgICB9LCBbaXNQcmVzZW50XSk7XG4gICAgaWYgKG1vZGUgPT09IFwicG9wTGF5b3V0XCIpIHtcbiAgICAgICAgY2hpbGRyZW4gPSAoanN4KFBvcENoaWxkLCB7IGlzUHJlc2VudDogaXNQcmVzZW50LCBhbmNob3JYOiBhbmNob3JYLCBjaGlsZHJlbjogY2hpbGRyZW4gfSkpO1xuICAgIH1cbiAgICByZXR1cm4gKGpzeChQcmVzZW5jZUNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGNvbnRleHQsIGNoaWxkcmVuOiBjaGlsZHJlbiB9KSk7XG59O1xuZnVuY3Rpb24gbmV3Q2hpbGRyZW5NYXAoKSB7XG4gICAgcmV0dXJuIG5ldyBNYXAoKTtcbn1cblxuZXhwb3J0IHsgUHJlc2VuY2VDaGlsZCB9O1xuIl0sIm5hbWVzIjpbImpzeCIsIlJlYWN0IiwidXNlSWQiLCJ1c2VNZW1vIiwiUHJlc2VuY2VDb250ZXh0IiwidXNlQ29uc3RhbnQiLCJQb3BDaGlsZCIsIlByZXNlbmNlQ2hpbGQiLCJjaGlsZHJlbiIsImluaXRpYWwiLCJpc1ByZXNlbnQiLCJvbkV4aXRDb21wbGV0ZSIsImN1c3RvbSIsInByZXNlbmNlQWZmZWN0c0xheW91dCIsIm1vZGUiLCJhbmNob3JYIiwicHJlc2VuY2VDaGlsZHJlbiIsIm5ld0NoaWxkcmVuTWFwIiwiaWQiLCJpc1JldXNlZENvbnRleHQiLCJjb250ZXh0IiwiY2hpbGRJZCIsInNldCIsImlzQ29tcGxldGUiLCJ2YWx1ZXMiLCJyZWdpc3RlciIsImRlbGV0ZSIsImZvckVhY2giLCJfIiwia2V5IiwidXNlRWZmZWN0Iiwic2l6ZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJNYXAiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: () => (/* binding */ AnimatePresence)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-presence.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\");\n/* __next_internal_client_entry_do_not_use__ AnimatePresence auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */ const AnimatePresence = (param)=>{\n    let { children, custom, initial = true, onExitComplete, presenceAffectsLayout = true, mode = \"sync\", propagate = false, anchorX = \"left\" } = param;\n    _s();\n    const [isParentPresent, safeToRemove] = (0,_use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence)(propagate);\n    /**\n     * Filter any children that aren't ReactElements. We can only track components\n     * between renders with a props.key.\n     */ const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnimatePresence.useMemo[presentChildren]\": ()=>(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(children)\n    }[\"AnimatePresence.useMemo[presentChildren]\"], [\n        children\n    ]);\n    /**\n     * Track the keys of the currently rendered children. This is used to\n     * determine which children are exiting.\n     */ const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey);\n    /**\n     * If `initial={false}` we only want to pass this to components in the first render.\n     */ const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    /**\n     * A ref containing the currently present children. When all exit animations\n     * are complete, we use this to re-render the component with the latest children\n     * *committed* rather than the latest children *rendered*.\n     */ const pendingPresentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(presentChildren);\n    /**\n     * Track which exiting children have finished animating out.\n     */ const exitComplete = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant)({\n        \"AnimatePresence.useConstant[exitComplete]\": ()=>new Map()\n    }[\"AnimatePresence.useConstant[exitComplete]\"]);\n    /**\n     * Save children to render as React state. To ensure this component is concurrent-safe,\n     * we check for exiting children via an effect.\n     */ const [diffedChildren, setDiffedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    const [renderedChildren, setRenderedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)({\n        \"AnimatePresence.useIsomorphicLayoutEffect\": ()=>{\n            isInitialRender.current = false;\n            pendingPresentChildren.current = presentChildren;\n            /**\n         * Update complete status of exiting children.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n                const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(renderedChildren[i]);\n                if (!presentKeys.includes(key)) {\n                    if (exitComplete.get(key) !== true) {\n                        exitComplete.set(key, false);\n                    }\n                } else {\n                    exitComplete.delete(key);\n                }\n            }\n        }\n    }[\"AnimatePresence.useIsomorphicLayoutEffect\"], [\n        renderedChildren,\n        presentKeys.length,\n        presentKeys.join(\"-\")\n    ]);\n    const exitingChildren = [];\n    if (presentChildren !== diffedChildren) {\n        let nextChildren = [\n            ...presentChildren\n        ];\n        /**\n         * Loop through all the currently rendered components and decide which\n         * are exiting.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n            const child = renderedChildren[i];\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            if (!presentKeys.includes(key)) {\n                nextChildren.splice(i, 0, child);\n                exitingChildren.push(child);\n            }\n        }\n        /**\n         * If we're in \"wait\" mode, and we have exiting children, we want to\n         * only render these until they've all exited.\n         */ if (mode === \"wait\" && exitingChildren.length) {\n            nextChildren = exitingChildren;\n        }\n        setRenderedChildren((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(nextChildren));\n        setDiffedChildren(presentChildren);\n        /**\n         * Early return to ensure once we've set state with the latest diffed\n         * children, we can immediately re-render.\n         */ return null;\n    }\n    if ( true && mode === \"wait\" && renderedChildren.length > 1) {\n        console.warn('You\\'re attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.');\n    }\n    /**\n     * If we've been provided a forceRender function by the LayoutGroupContext,\n     * we can use it to force a re-render amongst all surrounding components once\n     * all components have finished animating out.\n     */ const { forceRender } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.LayoutGroupContext);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: renderedChildren.map((child)=>{\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            const isPresent = propagate && !isParentPresent ? false : presentChildren === renderedChildren || presentKeys.includes(key);\n            const onExit = ()=>{\n                if (exitComplete.has(key)) {\n                    exitComplete.set(key, true);\n                } else {\n                    return;\n                }\n                let isEveryExitComplete = true;\n                exitComplete.forEach((isExitComplete)=>{\n                    if (!isExitComplete) isEveryExitComplete = false;\n                });\n                if (isEveryExitComplete) {\n                    forceRender === null || forceRender === void 0 ? void 0 : forceRender();\n                    setRenderedChildren(pendingPresentChildren.current);\n                    propagate && (safeToRemove === null || safeToRemove === void 0 ? void 0 : safeToRemove());\n                    onExitComplete && onExitComplete();\n                }\n            };\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, {\n                isPresent: isPresent,\n                initial: !isInitialRender.current || initial ? undefined : false,\n                custom: custom,\n                presenceAffectsLayout: presenceAffectsLayout,\n                mode: mode,\n                onExitComplete: isPresent ? undefined : onExit,\n                anchorX: anchorX,\n                children: child\n            }, key);\n        })\n    });\n};\n_s(AnimatePresence, \"hskVsE2zKTQdrb/joPYe18qtIRg=\", false, function() {\n    return [\n        _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence,\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant,\n        _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = AnimatePresence;\n\nvar _c;\n$RefreshReg$(_c, \"AnimatePresence\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChildKey: () => (/* binding */ getChildKey),\n/* harmony export */   onlyElements: () => (/* binding */ onlyElements)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nconst getChildKey = (child) => child.key || \"\";\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child) => {\n        if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDs7QUFFakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFRO0FBQ1osWUFBWSxxREFBYztBQUMxQjtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcY29tcG9uZW50c1xcQW5pbWF0ZVByZXNlbmNlXFx1dGlscy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hpbGRyZW4sIGlzVmFsaWRFbGVtZW50IH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBnZXRDaGlsZEtleSA9IChjaGlsZCkgPT4gY2hpbGQua2V5IHx8IFwiXCI7XG5mdW5jdGlvbiBvbmx5RWxlbWVudHMoY2hpbGRyZW4pIHtcbiAgICBjb25zdCBmaWx0ZXJlZCA9IFtdO1xuICAgIC8vIFdlIHVzZSBmb3JFYWNoIGhlcmUgaW5zdGVhZCBvZiBtYXAgYXMgbWFwIG11dGF0ZXMgdGhlIGNvbXBvbmVudCBrZXkgYnkgcHJlcHJlbmRpbmcgYC4kYFxuICAgIENoaWxkcmVuLmZvckVhY2goY2hpbGRyZW4sIChjaGlsZCkgPT4ge1xuICAgICAgICBpZiAoaXNWYWxpZEVsZW1lbnQoY2hpbGQpKVxuICAgICAgICAgICAgZmlsdGVyZWQucHVzaChjaGlsZCk7XG4gICAgfSk7XG4gICAgcmV0dXJuIGZpbHRlcmVkO1xufVxuXG5leHBvcnQgeyBnZXRDaGlsZEtleSwgb25seUVsZW1lbnRzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: () => (/* binding */ useCombineMotionValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: () => (/* binding */ useComputed)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDaUI7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHZhbHVlXFx1c2UtY29tcHV0ZWQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbGxlY3RNb3Rpb25WYWx1ZXMgfSBmcm9tICdtb3Rpb24tZG9tJztcbmltcG9ydCB7IHVzZUNvbWJpbmVNb3Rpb25WYWx1ZXMgfSBmcm9tICcuL3VzZS1jb21iaW5lLXZhbHVlcy5tanMnO1xuXG5mdW5jdGlvbiB1c2VDb21wdXRlZChjb21wdXRlKSB7XG4gICAgLyoqXG4gICAgICogT3BlbiBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuIEFueSBNb3Rpb25WYWx1ZSB0aGF0IGNhbGxzIGdldCgpXG4gICAgICogd2lsbCBiZSBzYXZlZCBpbnRvIHRoaXMgYXJyYXkuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gW107XG4gICAgY29tcHV0ZSgpO1xuICAgIGNvbnN0IHZhbHVlID0gdXNlQ29tYmluZU1vdGlvblZhbHVlcyhjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQsIGNvbXB1dGUpO1xuICAgIC8qKlxuICAgICAqIFN5bmNocm9ub3VzbHkgY2xvc2Ugc2Vzc2lvbiBvZiBjb2xsZWN0TW90aW9uVmFsdWVzLlxuICAgICAqL1xuICAgIGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCA9IHVuZGVmaW5lZDtcbiAgICByZXR1cm4gdmFsdWU7XG59XG5cbmV4cG9ydCB7IHVzZUNvbXB1dGVkIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: () => (/* binding */ useMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-spring.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSpring: () => (/* binding */ useSpring)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _use_transform_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n\n\n\n\n\n\nfunction useSpring(source, options = {}) {\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionConfigContext);\n    const getFromSource = () => ((0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isMotionValue)(source) ? source.get() : source);\n    // isStatic will never change, allowing early hooks return\n    if (isStatic) {\n        return (0,_use_transform_mjs__WEBPACK_IMPORTED_MODULE_3__.useTransform)(getFromSource);\n    }\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.useMotionValue)(getFromSource());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_5__.attachSpring)(value, source, options);\n    }, [value, JSON.stringify(options)]);\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXNwcmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF5RDtBQUNGO0FBQ2tCO0FBQ2pCO0FBQ0w7O0FBRW5ELHVDQUF1QztBQUN2QyxZQUFZLFdBQVcsRUFBRSxpREFBVSxDQUFDLGlGQUFtQjtBQUN2RCxpQ0FBaUMseURBQWE7QUFDOUM7QUFDQTtBQUNBLGVBQWUsZ0VBQVk7QUFDM0I7QUFDQSxrQkFBa0IscUVBQWM7QUFDaEMsSUFBSSx5REFBa0I7QUFDdEIsZUFBZSx3REFBWTtBQUMzQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHZhbHVlXFx1c2Utc3ByaW5nLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdHRhY2hTcHJpbmcsIGlzTW90aW9uVmFsdWUgfSBmcm9tICdtb3Rpb24tZG9tJztcbmltcG9ydCB7IHVzZUNvbnRleHQsIHVzZUluc2VydGlvbkVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IE1vdGlvbkNvbmZpZ0NvbnRleHQgfSBmcm9tICcuLi9jb250ZXh0L01vdGlvbkNvbmZpZ0NvbnRleHQubWpzJztcbmltcG9ydCB7IHVzZU1vdGlvblZhbHVlIH0gZnJvbSAnLi91c2UtbW90aW9uLXZhbHVlLm1qcyc7XG5pbXBvcnQgeyB1c2VUcmFuc2Zvcm0gfSBmcm9tICcuL3VzZS10cmFuc2Zvcm0ubWpzJztcblxuZnVuY3Rpb24gdXNlU3ByaW5nKHNvdXJjZSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgY29uc3QgeyBpc1N0YXRpYyB9ID0gdXNlQ29udGV4dChNb3Rpb25Db25maWdDb250ZXh0KTtcbiAgICBjb25zdCBnZXRGcm9tU291cmNlID0gKCkgPT4gKGlzTW90aW9uVmFsdWUoc291cmNlKSA/IHNvdXJjZS5nZXQoKSA6IHNvdXJjZSk7XG4gICAgLy8gaXNTdGF0aWMgd2lsbCBuZXZlciBjaGFuZ2UsIGFsbG93aW5nIGVhcmx5IGhvb2tzIHJldHVyblxuICAgIGlmIChpc1N0YXRpYykge1xuICAgICAgICByZXR1cm4gdXNlVHJhbnNmb3JtKGdldEZyb21Tb3VyY2UpO1xuICAgIH1cbiAgICBjb25zdCB2YWx1ZSA9IHVzZU1vdGlvblZhbHVlKGdldEZyb21Tb3VyY2UoKSk7XG4gICAgdXNlSW5zZXJ0aW9uRWZmZWN0KCgpID0+IHtcbiAgICAgICAgcmV0dXJuIGF0dGFjaFNwcmluZyh2YWx1ZSwgc291cmNlLCBvcHRpb25zKTtcbiAgICB9LCBbdmFsdWUsIEpTT04uc3RyaW5naWZ5KG9wdGlvbnMpXSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQgeyB1c2VTcHJpbmcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: () => (/* binding */ useTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/transform.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsNkRBQVc7QUFDcEM7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXHV0aWxzXFx0cmFuc2Zvcm0ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludGVycG9sYXRlIH0gZnJvbSAnLi9pbnRlcnBvbGF0ZS5tanMnO1xuXG5mdW5jdGlvbiB0cmFuc2Zvcm0oLi4uYXJncykge1xuICAgIGNvbnN0IHVzZUltbWVkaWF0ZSA9ICFBcnJheS5pc0FycmF5KGFyZ3NbMF0pO1xuICAgIGNvbnN0IGFyZ09mZnNldCA9IHVzZUltbWVkaWF0ZSA/IDAgOiAtMTtcbiAgICBjb25zdCBpbnB1dFZhbHVlID0gYXJnc1swICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBpbnB1dFJhbmdlID0gYXJnc1sxICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBvdXRwdXRSYW5nZSA9IGFyZ3NbMiArIGFyZ09mZnNldF07XG4gICAgY29uc3Qgb3B0aW9ucyA9IGFyZ3NbMyArIGFyZ09mZnNldF07XG4gICAgY29uc3QgaW50ZXJwb2xhdG9yID0gaW50ZXJwb2xhdGUoaW5wdXRSYW5nZSwgb3V0cHV0UmFuZ2UsIG9wdGlvbnMpO1xuICAgIHJldHVybiB1c2VJbW1lZGlhdGUgPyBpbnRlcnBvbGF0b3IoaW5wdXRWYWx1ZSkgOiBpbnRlcnBvbGF0b3I7XG59XG5cbmV4cG9ydCB7IHRyYW5zZm9ybSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/value/spring-value.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachSpring: () => (/* binding */ attachSpring),\n/* harmony export */   springValue: () => (/* binding */ springValue)\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _animation_JSAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../animation/JSAnimation.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/JSAnimation.mjs\");\n/* harmony import */ var _utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n/**\n * Create a `MotionValue` that animates to its latest value using a spring.\n * Can either be a value or track another `MotionValue`.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction springValue(source, options) {\n    const initialValue = (0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(source) ? source.get() : source;\n    const value = (0,_index_mjs__WEBPACK_IMPORTED_MODULE_1__.motionValue)(initialValue);\n    attachSpring(value, source, options);\n    return value;\n}\nfunction attachSpring(value, source, options) {\n    const initialValue = value.get();\n    let activeAnimation = null;\n    let latestValue = initialValue;\n    let latestSetter;\n    const unit = typeof initialValue === \"string\"\n        ? initialValue.replace(/[\\d.-]/g, \"\")\n        : undefined;\n    const stopAnimation = () => {\n        if (activeAnimation) {\n            activeAnimation.stop();\n            activeAnimation = null;\n        }\n    };\n    const startAnimation = () => {\n        stopAnimation();\n        activeAnimation = new _animation_JSAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.JSAnimation({\n            keyframes: [asNumber(value.get()), asNumber(latestValue)],\n            velocity: value.getVelocity(),\n            type: \"spring\",\n            restDelta: 0.001,\n            restSpeed: 0.01,\n            ...options,\n            onUpdate: latestSetter,\n        });\n    };\n    value.attach((v, set) => {\n        latestValue = v;\n        latestSetter = (latest) => set(parseValue(latest, unit));\n        _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.postRender(startAnimation);\n        return value.get();\n    }, stopAnimation);\n    let unsubscribe = undefined;\n    if ((0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(source)) {\n        unsubscribe = source.on(\"change\", (v) => value.set(parseValue(v, unit)));\n        value.on(\"destroy\", unsubscribe);\n    }\n    return unsubscribe;\n}\nfunction parseValue(v, unit) {\n    return unit ? v + unit : v;\n}\nfunction asNumber(v) {\n    return typeof v === \"number\" ? v : parseFloat(v);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs\n"));

/***/ })

});