/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/borsh";
exports.ids = ["vendor-chunks/borsh"];
exports.modules = {

/***/ "(ssr)/./node_modules/borsh/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/borsh/lib/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.deserializeUnchecked = exports.deserialize = exports.serialize = exports.BinaryReader = exports.BinaryWriter = exports.BorshError = exports.baseDecode = exports.baseEncode = void 0;\nconst bn_js_1 = __importDefault(__webpack_require__(/*! bn.js */ \"(ssr)/./node_modules/bn.js/lib/bn.js\"));\nconst bs58_1 = __importDefault(__webpack_require__(/*! bs58 */ \"(ssr)/./node_modules/borsh/node_modules/bs58/index.js\"));\n// TODO: Make sure this polyfill not included when not required\nconst encoding = __importStar(__webpack_require__(/*! text-encoding-utf-8 */ \"(ssr)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\"));\nconst ResolvedTextDecoder = typeof TextDecoder !== \"function\" ? encoding.TextDecoder : TextDecoder;\nconst textDecoder = new ResolvedTextDecoder(\"utf-8\", { fatal: true });\nfunction baseEncode(value) {\n    if (typeof value === \"string\") {\n        value = Buffer.from(value, \"utf8\");\n    }\n    return bs58_1.default.encode(Buffer.from(value));\n}\nexports.baseEncode = baseEncode;\nfunction baseDecode(value) {\n    return Buffer.from(bs58_1.default.decode(value));\n}\nexports.baseDecode = baseDecode;\nconst INITIAL_LENGTH = 1024;\nclass BorshError extends Error {\n    constructor(message) {\n        super(message);\n        this.fieldPath = [];\n        this.originalMessage = message;\n    }\n    addToFieldPath(fieldName) {\n        this.fieldPath.splice(0, 0, fieldName);\n        // NOTE: Modifying message directly as jest doesn't use .toString()\n        this.message = this.originalMessage + \": \" + this.fieldPath.join(\".\");\n    }\n}\nexports.BorshError = BorshError;\n/// Binary encoder.\nclass BinaryWriter {\n    constructor() {\n        this.buf = Buffer.alloc(INITIAL_LENGTH);\n        this.length = 0;\n    }\n    maybeResize() {\n        if (this.buf.length < 16 + this.length) {\n            this.buf = Buffer.concat([this.buf, Buffer.alloc(INITIAL_LENGTH)]);\n        }\n    }\n    writeU8(value) {\n        this.maybeResize();\n        this.buf.writeUInt8(value, this.length);\n        this.length += 1;\n    }\n    writeU16(value) {\n        this.maybeResize();\n        this.buf.writeUInt16LE(value, this.length);\n        this.length += 2;\n    }\n    writeU32(value) {\n        this.maybeResize();\n        this.buf.writeUInt32LE(value, this.length);\n        this.length += 4;\n    }\n    writeU64(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 8)));\n    }\n    writeU128(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 16)));\n    }\n    writeU256(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 32)));\n    }\n    writeU512(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 64)));\n    }\n    writeBuffer(buffer) {\n        // Buffer.from is needed as this.buf.subarray can return plain Uint8Array in browser\n        this.buf = Buffer.concat([\n            Buffer.from(this.buf.subarray(0, this.length)),\n            buffer,\n            Buffer.alloc(INITIAL_LENGTH),\n        ]);\n        this.length += buffer.length;\n    }\n    writeString(str) {\n        this.maybeResize();\n        const b = Buffer.from(str, \"utf8\");\n        this.writeU32(b.length);\n        this.writeBuffer(b);\n    }\n    writeFixedArray(array) {\n        this.writeBuffer(Buffer.from(array));\n    }\n    writeArray(array, fn) {\n        this.maybeResize();\n        this.writeU32(array.length);\n        for (const elem of array) {\n            this.maybeResize();\n            fn(elem);\n        }\n    }\n    toArray() {\n        return this.buf.subarray(0, this.length);\n    }\n}\nexports.BinaryWriter = BinaryWriter;\nfunction handlingRangeError(target, propertyKey, propertyDescriptor) {\n    const originalMethod = propertyDescriptor.value;\n    propertyDescriptor.value = function (...args) {\n        try {\n            return originalMethod.apply(this, args);\n        }\n        catch (e) {\n            if (e instanceof RangeError) {\n                const code = e.code;\n                if ([\"ERR_BUFFER_OUT_OF_BOUNDS\", \"ERR_OUT_OF_RANGE\"].indexOf(code) >= 0) {\n                    throw new BorshError(\"Reached the end of buffer when deserializing\");\n                }\n            }\n            throw e;\n        }\n    };\n}\nclass BinaryReader {\n    constructor(buf) {\n        this.buf = buf;\n        this.offset = 0;\n    }\n    readU8() {\n        const value = this.buf.readUInt8(this.offset);\n        this.offset += 1;\n        return value;\n    }\n    readU16() {\n        const value = this.buf.readUInt16LE(this.offset);\n        this.offset += 2;\n        return value;\n    }\n    readU32() {\n        const value = this.buf.readUInt32LE(this.offset);\n        this.offset += 4;\n        return value;\n    }\n    readU64() {\n        const buf = this.readBuffer(8);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU128() {\n        const buf = this.readBuffer(16);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU256() {\n        const buf = this.readBuffer(32);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU512() {\n        const buf = this.readBuffer(64);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readBuffer(len) {\n        if (this.offset + len > this.buf.length) {\n            throw new BorshError(`Expected buffer length ${len} isn't within bounds`);\n        }\n        const result = this.buf.slice(this.offset, this.offset + len);\n        this.offset += len;\n        return result;\n    }\n    readString() {\n        const len = this.readU32();\n        const buf = this.readBuffer(len);\n        try {\n            // NOTE: Using TextDecoder to fail on invalid UTF-8\n            return textDecoder.decode(buf);\n        }\n        catch (e) {\n            throw new BorshError(`Error decoding UTF-8 string: ${e}`);\n        }\n    }\n    readFixedArray(len) {\n        return new Uint8Array(this.readBuffer(len));\n    }\n    readArray(fn) {\n        const len = this.readU32();\n        const result = Array();\n        for (let i = 0; i < len; ++i) {\n            result.push(fn());\n        }\n        return result;\n    }\n}\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU8\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU16\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU32\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU64\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU128\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU256\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU512\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readString\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readFixedArray\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readArray\", null);\nexports.BinaryReader = BinaryReader;\nfunction capitalizeFirstLetter(string) {\n    return string.charAt(0).toUpperCase() + string.slice(1);\n}\nfunction serializeField(schema, fieldName, value, fieldType, writer) {\n    try {\n        // TODO: Handle missing values properly (make sure they never result in just skipped write)\n        if (typeof fieldType === \"string\") {\n            writer[`write${capitalizeFirstLetter(fieldType)}`](value);\n        }\n        else if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                if (value.length !== fieldType[0]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[0]}, but got ${value.length} bytes`);\n                }\n                writer.writeFixedArray(value);\n            }\n            else if (fieldType.length === 2 && typeof fieldType[1] === \"number\") {\n                if (value.length !== fieldType[1]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[1]}, but got ${value.length} bytes`);\n                }\n                for (let i = 0; i < fieldType[1]; i++) {\n                    serializeField(schema, null, value[i], fieldType[0], writer);\n                }\n            }\n            else {\n                writer.writeArray(value, (item) => {\n                    serializeField(schema, fieldName, item, fieldType[0], writer);\n                });\n            }\n        }\n        else if (fieldType.kind !== undefined) {\n            switch (fieldType.kind) {\n                case \"option\": {\n                    if (value === null || value === undefined) {\n                        writer.writeU8(0);\n                    }\n                    else {\n                        writer.writeU8(1);\n                        serializeField(schema, fieldName, value, fieldType.type, writer);\n                    }\n                    break;\n                }\n                case \"map\": {\n                    writer.writeU32(value.size);\n                    value.forEach((val, key) => {\n                        serializeField(schema, fieldName, key, fieldType.key, writer);\n                        serializeField(schema, fieldName, val, fieldType.value, writer);\n                    });\n                    break;\n                }\n                default:\n                    throw new BorshError(`FieldType ${fieldType} unrecognized`);\n            }\n        }\n        else {\n            serializeStruct(schema, value, writer);\n        }\n    }\n    catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction serializeStruct(schema, obj, writer) {\n    if (typeof obj.borshSerialize === \"function\") {\n        obj.borshSerialize(writer);\n        return;\n    }\n    const structSchema = schema.get(obj.constructor);\n    if (!structSchema) {\n        throw new BorshError(`Class ${obj.constructor.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        structSchema.fields.map(([fieldName, fieldType]) => {\n            serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n        });\n    }\n    else if (structSchema.kind === \"enum\") {\n        const name = obj[structSchema.field];\n        for (let idx = 0; idx < structSchema.values.length; ++idx) {\n            const [fieldName, fieldType] = structSchema.values[idx];\n            if (fieldName === name) {\n                writer.writeU8(idx);\n                serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n                break;\n            }\n        }\n    }\n    else {\n        throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${obj.constructor.name}`);\n    }\n}\n/// Serialize given object using schema of the form:\n/// { class_name -> [ [field_name, field_type], .. ], .. }\nfunction serialize(schema, obj, Writer = BinaryWriter) {\n    const writer = new Writer();\n    serializeStruct(schema, obj, writer);\n    return writer.toArray();\n}\nexports.serialize = serialize;\nfunction deserializeField(schema, fieldName, fieldType, reader) {\n    try {\n        if (typeof fieldType === \"string\") {\n            return reader[`read${capitalizeFirstLetter(fieldType)}`]();\n        }\n        if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                return reader.readFixedArray(fieldType[0]);\n            }\n            else if (typeof fieldType[1] === \"number\") {\n                const arr = [];\n                for (let i = 0; i < fieldType[1]; i++) {\n                    arr.push(deserializeField(schema, null, fieldType[0], reader));\n                }\n                return arr;\n            }\n            else {\n                return reader.readArray(() => deserializeField(schema, fieldName, fieldType[0], reader));\n            }\n        }\n        if (fieldType.kind === \"option\") {\n            const option = reader.readU8();\n            if (option) {\n                return deserializeField(schema, fieldName, fieldType.type, reader);\n            }\n            return undefined;\n        }\n        if (fieldType.kind === \"map\") {\n            let map = new Map();\n            const length = reader.readU32();\n            for (let i = 0; i < length; i++) {\n                const key = deserializeField(schema, fieldName, fieldType.key, reader);\n                const val = deserializeField(schema, fieldName, fieldType.value, reader);\n                map.set(key, val);\n            }\n            return map;\n        }\n        return deserializeStruct(schema, fieldType, reader);\n    }\n    catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction deserializeStruct(schema, classType, reader) {\n    if (typeof classType.borshDeserialize === \"function\") {\n        return classType.borshDeserialize(reader);\n    }\n    const structSchema = schema.get(classType);\n    if (!structSchema) {\n        throw new BorshError(`Class ${classType.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        const result = {};\n        for (const [fieldName, fieldType] of schema.get(classType).fields) {\n            result[fieldName] = deserializeField(schema, fieldName, fieldType, reader);\n        }\n        return new classType(result);\n    }\n    if (structSchema.kind === \"enum\") {\n        const idx = reader.readU8();\n        if (idx >= structSchema.values.length) {\n            throw new BorshError(`Enum index: ${idx} is out of range`);\n        }\n        const [fieldName, fieldType] = structSchema.values[idx];\n        const fieldValue = deserializeField(schema, fieldName, fieldType, reader);\n        return new classType({ [fieldName]: fieldValue });\n    }\n    throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${classType.constructor.name}`);\n}\n/// Deserializes object from bytes using schema.\nfunction deserialize(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    const result = deserializeStruct(schema, classType, reader);\n    if (reader.offset < buffer.length) {\n        throw new BorshError(`Unexpected ${buffer.length - reader.offset} bytes after deserialized data`);\n    }\n    return result;\n}\nexports.deserialize = deserialize;\n/// Deserializes object from bytes using schema, without checking the length read\nfunction deserializeUnchecked(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    return deserializeStruct(schema, classType, reader);\n}\nexports.deserializeUnchecked = deserializeUnchecked;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/borsh/node_modules/base-x/src/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/borsh/node_modules/base-x/src/index.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\nvar _Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer)\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (Array.isArray(source) || source instanceof Uint8Array) { source = _Buffer.from(source) }\n    if (!_Buffer.isBuffer(source)) { throw new TypeError('Expected Buffer') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return _Buffer.alloc(0) }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (psz < source.length) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = _Buffer.allocUnsafe(zeroes + (size - it4))\n    vch.fill(0x00, 0, zeroes)\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/node_modules/base-x/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/borsh/node_modules/bs58/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/borsh/node_modules/bs58/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var basex = __webpack_require__(/*! base-x */ \"(ssr)/./node_modules/borsh/node_modules/base-x/src/index.js\")\nvar ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n\nmodule.exports = basex(ALPHABET)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYm9yc2gvbm9kZV9tb2R1bGVzL2JzNTgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsWUFBWSxtQkFBTyxDQUFDLDJFQUFRO0FBQzVCOztBQUVBIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxub2RlX21vZHVsZXNcXGJvcnNoXFxub2RlX21vZHVsZXNcXGJzNThcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBiYXNleCA9IHJlcXVpcmUoJ2Jhc2UteCcpXG52YXIgQUxQSEFCRVQgPSAnMTIzNDU2Nzg5QUJDREVGR0hKS0xNTlBRUlNUVVZXWFlaYWJjZGVmZ2hpamttbm9wcXJzdHV2d3h5eidcblxubW9kdWxlLmV4cG9ydHMgPSBiYXNleChBTFBIQUJFVClcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/borsh/node_modules/bs58/index.js\n");

/***/ })

};
;