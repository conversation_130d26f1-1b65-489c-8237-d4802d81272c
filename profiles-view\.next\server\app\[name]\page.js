/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[name]/page";
exports.ids = ["app/[name]/page"];
exports.modules = {

/***/ "(rsc)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\app\\[name]\\ClientPage.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/[name]/layout.tsx":
/*!*******************************!*\
  !*** ./app/[name]/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileLayout)\n/* harmony export */ });\nfunction ProfileLayout({ children }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW25hbWVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLGNBQWMsRUFDcENDLFFBQVEsRUFHVDtJQUNDLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcW25hbWVdXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2ZpbGVMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIGNoaWxkcmVuO1xufVxuIl0sIm5hbWVzIjpbIlByb2ZpbGVMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/[name]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[name]/page.tsx":
/*!*****************************!*\
  !*** ./app/[name]/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ClientPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ClientPage */ \"(rsc)/./app/[name]/ClientPage.tsx\");\n\n\nasync function Page({ params }) {\n    // Await the params if they are provided as a Promise\n    const resolvedParams = params ? await params : {\n        name: ''\n    };\n    // Ensure name is never undefined\n    const name = resolvedParams.name || '';\n    // Page component received name parameter\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientPage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        name: name\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW25hbWVdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNDO0FBRXZCLGVBQWVDLEtBQUssRUFDakNDLE1BQU0sRUFHUDtJQUNDLHFEQUFxRDtJQUNyRCxNQUFNQyxpQkFBaUJELFNBQVMsTUFBTUEsU0FBUztRQUFFRSxNQUFNO0lBQUc7SUFFMUQsaUNBQWlDO0lBQ2pDLE1BQU1BLE9BQU9ELGVBQWVDLElBQUksSUFBSTtJQUNwQyx5Q0FBeUM7SUFFekMscUJBQU8sOERBQUNKLG1EQUFVQTtRQUFDSSxNQUFNQTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxhcHBcXFtuYW1lXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENsaWVudFBhZ2UgZnJvbSAnLi9DbGllbnRQYWdlJztcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gUGFnZSh7XG4gIHBhcmFtcyxcbn06IHtcbiAgcGFyYW1zPzogUHJvbWlzZTx7IG5hbWU6IHN0cmluZyB9PlxufSkge1xuICAvLyBBd2FpdCB0aGUgcGFyYW1zIGlmIHRoZXkgYXJlIHByb3ZpZGVkIGFzIGEgUHJvbWlzZVxuICBjb25zdCByZXNvbHZlZFBhcmFtcyA9IHBhcmFtcyA/IGF3YWl0IHBhcmFtcyA6IHsgbmFtZTogJycgfTtcblxuICAvLyBFbnN1cmUgbmFtZSBpcyBuZXZlciB1bmRlZmluZWRcbiAgY29uc3QgbmFtZSA9IHJlc29sdmVkUGFyYW1zLm5hbWUgfHwgJyc7XG4gIC8vIFBhZ2UgY29tcG9uZW50IHJlY2VpdmVkIG5hbWUgcGFyYW1ldGVyXG5cbiAgcmV0dXJuIDxDbGllbnRQYWdlIG5hbWU9e25hbWV9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkNsaWVudFBhZ2UiLCJQYWdlIiwicGFyYW1zIiwicmVzb2x2ZWRQYXJhbXMiLCJuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[name]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8cebb05c8e82\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4Y2ViYjA1YzhlODJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./components/Providers.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Web3Socials Profiles - View Only\",\n    description: \"View Web3 profiles\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased overflow-x-hidden`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\components\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/layout.tsx */ \"(rsc)/./app/[name]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/page.tsx */ \"(rsc)/./app/[name]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[name]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[name]/page\",\n        pathname: \"/[name]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/ClientPage.tsx */ \"(rsc)/./app/[name]/ClientPage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1QyU1Qm5hbWUlNUQlNUMlNUNDbGllbnRQYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcYXBwXFxcXFtuYW1lXVxcXFxDbGllbnRQYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(rsc)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/profileStatus */ \"(ssr)/./lib/profileStatus.ts\");\n/* harmony import */ var _app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/components/renders/render_hero */ \"(ssr)/./app/components/renders/render_hero.tsx\");\n/* harmony import */ var _app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/renders/render_sociallinks */ \"(ssr)/./app/components/renders/render_sociallinks.tsx\");\n/* harmony import */ var _app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/renders/render_bannerpfp */ \"(ssr)/./app/components/renders/render_bannerpfp.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ClientPage({ name }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statusChecked, setStatusChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = (0,_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata)();\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // First check profile status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            async function checkStatus() {\n                try {\n                    console.log('[ClientPage] Checking status for:', name);\n                    const statusResult = await (0,_lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__.checkProfileStatus)(name);\n                    console.log('[ClientPage] Status result:', statusResult);\n                    // Check if this is the user's own profile\n                    const isOwnProfile = address && statusResult.address && address.toLowerCase() === statusResult.address.toLowerCase();\n                    console.log('[ClientPage] Is own profile:', isOwnProfile);\n                    // If profile is not approved, redirect to error page\n                    // We no longer make an exception for the profile owner\n                    if (!statusResult.isApproved && statusResult.status !== 'not-found') {\n                        console.log('[ClientPage] Redirecting to error page for status:', statusResult.status);\n                        // Use the name parameter instead of address\n                        const nameParam = statusResult.name ? `&name=${statusResult.name}` : `&name=${name}`;\n                        window.location.href = `/profile-error?status=${statusResult.status}${nameParam}`;\n                        return;\n                    }\n                    setStatusChecked(true);\n                } catch (error) {\n                    console.error('[ClientPage] Error checking profile status:', error);\n                    setStatusChecked(true); // Continue anyway if status check fails\n                }\n            }\n            checkStatus();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        address\n    ]);\n    // Then fetch profile data once status is checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            if (!statusChecked) return; // Wait until status check is complete\n            async function fetchProfileData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('Fetching profile for name:', name);\n                    console.log('Current pathname:', pathname);\n                    // Fetch profile data by name\n                    // Always treat the parameter as a name, not an address\n                    const response = await fetch(`/api/profile/${name}`);\n                    if (!response.ok) {\n                        // Handle errors with more specific messages\n                        const errorResponse = await response.json();\n                        if (response.status === 404) {\n                            // For 404 errors, show a user-friendly message\n                            if (errorResponse.error === 'No profiles exist in the database yet') {\n                                setError('No profiles have been created yet. Be the first to create one!');\n                            } else if (errorResponse.error === 'Profile components not properly initialized') {\n                                setError('Profile system is not properly initialized. Please contact support.');\n                            } else {\n                                setError(`Profile \"${name}\" not found`);\n                            }\n                            setLoading(false);\n                            return; // Exit early without throwing an error\n                        }\n                        // For other errors, log and throw\n                        console.error('Failed to fetch profile:', errorResponse);\n                        throw new Error(errorResponse.error || 'Failed to load profile');\n                    }\n                    const data = await response.json();\n                    setProfileData(data);\n                    // Fetch bannerpfp metadata\n                    if (data.address) {\n                        try {\n                            // Fetch bannerpfp metadata first\n                            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);\n                            if (bannerPfpMeta) {\n                                setBannerPfpMetadata(bannerPfpMeta);\n                                // Now fetch banner and profile picture metadata which will use the bannerpfp data\n                                const bannerMeta = await fetchBannerMetadata(data.address);\n                                if (bannerMeta) {\n                                    setBannerMetadata(bannerMeta);\n                                }\n                                const profilePicMeta = await fetchProfilePictureMetadata(data.address);\n                                if (profilePicMeta) {\n                                    setProfilePictureMetadata(profilePicMeta);\n                                }\n                            }\n                        } catch (metadataError) {\n                            console.error('Error fetching component metadata:', metadataError);\n                        // Continue showing the profile even if metadata fetch fails\n                        }\n                    }\n                } catch (err) {\n                    console.error('Error loading profile:', err);\n                    setError(err.message || 'An error occurred while loading the profile');\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchProfileData();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        fetchBannerMetadata,\n        fetchProfilePictureMetadata,\n        statusChecked\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen flex flex-col items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-4xl mx-auto px-4 sm:px-6 pb-8 z-10 relative\",\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center min-h-[300px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 157,\n                columnNumber: 11\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-red-400 font-medium mb-2\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this) : profileData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-0 border border-neutral-700 overflow-hidden\",\n                children: profileData.components.filter((c)=>c.hidden !== 'Y').sort((a, b)=>parseInt(a.order) - parseInt(b.order)).map((component, index)=>{\n                    // Skip old component types\n                    if (component.componentType === 'banner' || component.componentType === 'profilePicture') {\n                        return null;\n                    } else if (component.componentType === 'details') {\n                        // Skip rendering deprecated 'details' component\n                        return null;\n                    } else if (component.componentType === 'hero') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 23\n                            }, this)\n                        }, `hero-${component.order}`, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'socialLinks') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                profileData: {\n                                    address: profileData.address,\n                                    chain: profileData.chain,\n                                    name: profileData.name || '',\n                                    bio: ''\n                                },\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 23\n                            }, this)\n                        }, `socialLinks-${component.order}`, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'bannerpfp') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false,\n                                profileName: profileData.name || '',\n                                profileBio: ''\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 23\n                            }, this)\n                        }, `bannerpfp-${component.order}`, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 21\n                        }, this);\n                    }\n                    return null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 172,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-yellow-400 font-medium mb-2\",\n                        children: \"Profile Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: [\n                            'The profile \"',\n                            name,\n                            \"\\\" doesn't exist or has been removed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 237,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW25hbWVdL0NsaWVudFBhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2E7QUFDbEI7QUFDZ0I7QUFDTTtBQUNKO0FBQ0s7QUFDYztBQUNKO0FBc0J6RCxTQUFTVyxXQUFXLEVBQUVDLElBQUksRUFBb0I7SUFDM0QsTUFBTUMsV0FBV1gsNERBQVdBO0lBQzVCLE1BQU1ZLFNBQVNYLDBEQUFTQTtJQUN4QixNQUFNLEVBQUVZLE9BQU8sRUFBRSxHQUFHVixxRUFBZ0JBO0lBQ3BDLE1BQU0sQ0FBQ1csYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQXFCO0lBQ25FLE1BQU0sQ0FBQ2tCLFNBQVNDLFdBQVcsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ29CLE9BQU9DLFNBQVMsR0FBR3JCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNzQixlQUFlQyxpQkFBaUIsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sRUFBRXdCLG1CQUFtQixFQUFFQywyQkFBMkIsRUFBRUMsc0JBQXNCLEVBQUUsR0FBR3BCLDBFQUFXQTtJQUNoRyxNQUFNLENBQUNxQixtQkFBbUJDLHFCQUFxQixHQUFHNUIsK0NBQVFBLENBQU07SUFFaEUsNkJBQTZCO0lBQzdCQyxnREFBU0E7Z0NBQUM7WUFDUixlQUFlNEI7Z0JBQ2IsSUFBSTtvQkFDRkMsUUFBUUMsR0FBRyxDQUFDLHFDQUFxQ25CO29CQUNqRCxNQUFNb0IsZUFBZSxNQUFNekIsc0VBQWtCQSxDQUFDSztvQkFDOUNrQixRQUFRQyxHQUFHLENBQUMsK0JBQStCQztvQkFFM0MsMENBQTBDO29CQUMxQyxNQUFNQyxlQUFlbEIsV0FBV2lCLGFBQWFqQixPQUFPLElBQ2xEQSxRQUFRbUIsV0FBVyxPQUFPRixhQUFhakIsT0FBTyxDQUFDbUIsV0FBVztvQkFDNURKLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0NFO29CQUU1QyxxREFBcUQ7b0JBQ3JELHVEQUF1RDtvQkFDdkQsSUFBSSxDQUFDRCxhQUFhRyxVQUFVLElBQUlILGFBQWFJLE1BQU0sS0FBSyxhQUFhO3dCQUNuRU4sUUFBUUMsR0FBRyxDQUFDLHNEQUFzREMsYUFBYUksTUFBTTt3QkFDckYsNENBQTRDO3dCQUM1QyxNQUFNQyxZQUFZTCxhQUFhcEIsSUFBSSxHQUFHLENBQUMsTUFBTSxFQUFFb0IsYUFBYXBCLElBQUksRUFBRSxHQUFHLENBQUMsTUFBTSxFQUFFQSxNQUFNO3dCQUNwRjBCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHLENBQUMsc0JBQXNCLEVBQUVSLGFBQWFJLE1BQU0sR0FBR0MsV0FBVzt3QkFDakY7b0JBQ0Y7b0JBRUFkLGlCQUFpQjtnQkFDbkIsRUFBRSxPQUFPSCxPQUFPO29CQUNkVSxRQUFRVixLQUFLLENBQUMsK0NBQStDQTtvQkFDN0RHLGlCQUFpQixPQUFPLHdDQUF3QztnQkFDbEU7WUFDRjtZQUVBTTtRQUNGOytCQUFHO1FBQUNqQjtRQUFNRztLQUFRO0lBRWxCLGlEQUFpRDtJQUNqRGQsZ0RBQVNBO2dDQUFDO1lBQ1IsSUFBSSxDQUFDcUIsZUFBZSxRQUFRLHNDQUFzQztZQUVsRSxlQUFlbUI7Z0JBQ2IsSUFBSTtvQkFDRnRCLFdBQVc7b0JBQ1hFLFNBQVM7b0JBRVRTLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJuQjtvQkFDMUNrQixRQUFRQyxHQUFHLENBQUMscUJBQXFCbEI7b0JBRWpDLDZCQUE2QjtvQkFDN0IsdURBQXVEO29CQUN2RCxNQUFNNkIsV0FBVyxNQUFNQyxNQUFNLENBQUMsYUFBYSxFQUFFL0IsTUFBTTtvQkFFbkQsSUFBSSxDQUFDOEIsU0FBU0UsRUFBRSxFQUFFO3dCQUNoQiw0Q0FBNEM7d0JBQzVDLE1BQU1DLGdCQUFnQixNQUFNSCxTQUFTSSxJQUFJO3dCQUV6QyxJQUFJSixTQUFTTixNQUFNLEtBQUssS0FBSzs0QkFDM0IsK0NBQStDOzRCQUMvQyxJQUFJUyxjQUFjekIsS0FBSyxLQUFLLHlDQUF5QztnQ0FDbkVDLFNBQVM7NEJBQ1gsT0FBTyxJQUFJd0IsY0FBY3pCLEtBQUssS0FBSywrQ0FBK0M7Z0NBQ2hGQyxTQUFTOzRCQUNYLE9BQU87Z0NBQ0xBLFNBQVMsQ0FBQyxTQUFTLEVBQUVULEtBQUssV0FBVyxDQUFDOzRCQUN4Qzs0QkFDQU8sV0FBVzs0QkFDWCxRQUFRLHVDQUF1Qzt3QkFDakQ7d0JBRUEsa0NBQWtDO3dCQUNsQ1csUUFBUVYsS0FBSyxDQUFDLDRCQUE0QnlCO3dCQUMxQyxNQUFNLElBQUlFLE1BQU1GLGNBQWN6QixLQUFLLElBQUk7b0JBQ3pDO29CQUVBLE1BQU00QixPQUFPLE1BQU1OLFNBQVNJLElBQUk7b0JBQ2hDN0IsZUFBZStCO29CQUVmLDJCQUEyQjtvQkFDM0IsSUFBSUEsS0FBS2pDLE9BQU8sRUFBRTt3QkFDaEIsSUFBSTs0QkFDRixpQ0FBaUM7NEJBQ2pDLE1BQU1rQyxnQkFBZ0IsTUFBTXZCLHVCQUF1QnNCLEtBQUtqQyxPQUFPOzRCQUMvRCxJQUFJa0MsZUFBZTtnQ0FDakJyQixxQkFBcUJxQjtnQ0FFckIsa0ZBQWtGO2dDQUNsRixNQUFNQyxhQUFhLE1BQU0xQixvQkFBb0J3QixLQUFLakMsT0FBTztnQ0FDekQsSUFBSW1DLFlBQVk7b0NBQ2RDLGtCQUFrQkQ7Z0NBQ3BCO2dDQUVBLE1BQU1FLGlCQUFpQixNQUFNM0IsNEJBQTRCdUIsS0FBS2pDLE9BQU87Z0NBQ3JFLElBQUlxQyxnQkFBZ0I7b0NBQ2xCQywwQkFBMEJEO2dDQUM1Qjs0QkFDRjt3QkFDRixFQUFFLE9BQU9FLGVBQWU7NEJBQ3RCeEIsUUFBUVYsS0FBSyxDQUFDLHNDQUFzQ2tDO3dCQUNwRCw0REFBNEQ7d0JBQzlEO29CQUNGO2dCQUNGLEVBQUUsT0FBT0MsS0FBVTtvQkFDakJ6QixRQUFRVixLQUFLLENBQUMsMEJBQTBCbUM7b0JBQ3hDbEMsU0FBU2tDLElBQUlDLE9BQU8sSUFBSTtnQkFDMUIsU0FBVTtvQkFDUnJDLFdBQVc7Z0JBQ2I7WUFDRjtZQUVBc0I7UUFDRjsrQkFBRztRQUFDN0I7UUFBTVk7UUFBcUJDO1FBQTZCSDtLQUFjO0lBRTFFLHFCQUNFLDhEQUFDbUM7UUFBS0MsV0FBVTtrQkFDZCw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDWnhDLHdCQUNDLDhEQUFDeUM7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUN0RCxtRkFBT0E7b0JBQUNzRCxXQUFVOzs7Ozs7Ozs7O3VCQUVuQnRDLHNCQUNGLDhEQUFDdUM7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FBZ0M7Ozs7OztrQ0FDOUMsOERBQUNHO3dCQUFFSCxXQUFVO2tDQUF5QnRDOzs7Ozs7a0NBQ3RDLDhEQUFDMEM7d0JBQ0NDLFNBQVMsSUFBTXpCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO3dCQUN0Q2tCLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7O3VCQUlEMUMsNEJBQ0YsOERBQUMyQztnQkFBSUQsV0FBVTswQkFDWjFDLFlBQVlnRCxVQUFVLENBQ3BCQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE1BQU0sS0FBSyxLQUN6QkMsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1DLFNBQVNGLEVBQUVHLEtBQUssSUFBSUQsU0FBU0QsRUFBRUUsS0FBSyxHQUNuREMsR0FBRyxDQUFDLENBQUNDLFdBQVdDO29CQUNmLDJCQUEyQjtvQkFDM0IsSUFBSUQsVUFBVUUsYUFBYSxLQUFLLFlBQVlGLFVBQVVFLGFBQWEsS0FBSyxrQkFBa0I7d0JBQ3hGLE9BQU87b0JBQ1QsT0FBTyxJQUFJRixVQUFVRSxhQUFhLEtBQUssV0FBVzt3QkFDaEQsZ0RBQWdEO3dCQUNoRCxPQUFPO29CQUNULE9BQU8sSUFBSUYsVUFBVUUsYUFBYSxLQUFLLFFBQVE7d0JBQzdDLHFCQUNFLDhEQUFDakI7NEJBQW9DRCxXQUFVO3NDQUM3Qyw0RUFBQ2xELDJFQUFVQTtnQ0FDVE8sU0FBU0MsWUFBWUQsT0FBTztnQ0FDNUI4RCxlQUFlO29DQUNiLEdBQUdILFNBQVM7b0NBQ1ozRCxTQUFTQyxZQUFZRCxPQUFPO29DQUM1QitELE9BQU85RCxZQUFZOEQsS0FBSztnQ0FDMUI7Z0NBQ0FDLG1CQUFtQjs7Ozs7OzJCQVJiLENBQUMsS0FBSyxFQUFFTCxVQUFVRixLQUFLLEVBQUU7Ozs7O29CQVl2QyxPQUFPLElBQUlFLFVBQVVFLGFBQWEsS0FBSyxlQUFlO3dCQUNwRCxxQkFDRSw4REFBQ2pCOzRCQUEyQ0QsV0FBVTtzQ0FDcEQsNEVBQUNqRCxrRkFBaUJBO2dDQUNoQk8sYUFBYTtvQ0FDWEQsU0FBU0MsWUFBWUQsT0FBTztvQ0FDNUIrRCxPQUFPOUQsWUFBWThELEtBQUs7b0NBQ3hCbEUsTUFBTUksWUFBWUosSUFBSSxJQUFJO29DQUMxQm9FLEtBQUs7Z0NBQ1A7Z0NBQ0FILGVBQWU7b0NBQ2IsR0FBR0gsU0FBUztvQ0FDWjNELFNBQVNDLFlBQVlELE9BQU87b0NBQzVCK0QsT0FBTzlELFlBQVk4RCxLQUFLO2dDQUMxQjtnQ0FDQUMsbUJBQW1COzs7Ozs7MkJBYmIsQ0FBQyxZQUFZLEVBQUVMLFVBQVVGLEtBQUssRUFBRTs7Ozs7b0JBaUI5QyxPQUFPLElBQUlFLFVBQVVFLGFBQWEsS0FBSyxhQUFhO3dCQUNsRCxxQkFDRSw4REFBQ2pCOzRCQUF5Q0QsV0FBVTtzQ0FDbEQsNEVBQUNoRCxnRkFBZUE7Z0NBQ2RLLFNBQVNDLFlBQVlELE9BQU87Z0NBQzVCOEQsZUFBZTtvQ0FDYixHQUFHSCxTQUFTO29DQUNaM0QsU0FBU0MsWUFBWUQsT0FBTztvQ0FDNUIrRCxPQUFPOUQsWUFBWThELEtBQUs7Z0NBQzFCO2dDQUNBQyxtQkFBbUI7Z0NBQ25CRSxhQUFhakUsWUFBWUosSUFBSSxJQUFJO2dDQUNqQ3NFLFlBQVk7Ozs7OzsyQkFWTixDQUFDLFVBQVUsRUFBRVIsVUFBVUYsS0FBSyxFQUFFOzs7OztvQkFjNUM7b0JBQ0EsT0FBTztnQkFDVDs7Ozs7cUNBR0osOERBQUNiO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQW1DOzs7Ozs7a0NBQ2pELDhEQUFDRzt3QkFBRUgsV0FBVTs7NEJBQXdCOzRCQUNyQjlDOzRCQUFLOzs7Ozs7O2tDQUVyQiw4REFBQ2tEO3dCQUNDQyxTQUFTLElBQU16QixPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRzt3QkFDdENrQixXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcW25hbWVdXFxDbGllbnRQYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSwgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlQXBwS2l0QWNjb3VudCB9IGZyb20gJ0ByZW93bi9hcHBraXQvcmVhY3QnO1xuaW1wb3J0IHsgdXNlTWV0YWRhdGEgfSBmcm9tICdAL2FwcC9jb250ZXh0cy9NZXRhZGF0YUNvbnRleHQnO1xuaW1wb3J0IHsgY2hlY2tQcm9maWxlU3RhdHVzIH0gZnJvbSAnQC9saWIvcHJvZmlsZVN0YXR1cyc7XG5pbXBvcnQgUmVuZGVySGVybyBmcm9tICdAL2FwcC9jb21wb25lbnRzL3JlbmRlcnMvcmVuZGVyX2hlcm8nO1xuaW1wb3J0IFJlbmRlclNvY2lhbExpbmtzIGZyb20gJ0AvYXBwL2NvbXBvbmVudHMvcmVuZGVycy9yZW5kZXJfc29jaWFsbGlua3MnO1xuaW1wb3J0IFJlbmRlckJhbm5lclBmcCBmcm9tICdAL2FwcC9jb21wb25lbnRzL3JlbmRlcnMvcmVuZGVyX2Jhbm5lcnBmcCc7XG5cbmludGVyZmFjZSBQcm9maWxlRGF0YSB7XG4gIGFkZHJlc3M6IHN0cmluZztcbiAgbmFtZT86IHN0cmluZzsgLy8gT3B0aW9uYWwgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbiAgcHJvZmlsZU5hbWU/OiBzdHJpbmc7IC8vIE5ldyBmaWVsZFxuICBiaW8/OiBzdHJpbmc7IC8vIE9wdGlvbmFsIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XG4gIHByb2ZpbGVCaW8/OiBzdHJpbmc7IC8vIE5ldyBmaWVsZFxuICBjaGFpbjogc3RyaW5nO1xuICAvLyBzb2NpYWxMaW5rcyByZW1vdmVkIC0gbm93IHN0b3JlZCBpbiBjb21wb25lbnRQb3NpdGlvbnMgdGFibGUgZm9yIHNvY2lhbExpbmtzIGNvbXBvbmVudFxuICBjb21wb25lbnRzOiB7XG4gICAgY29tcG9uZW50VHlwZTogc3RyaW5nO1xuICAgIG9yZGVyOiBzdHJpbmc7XG4gICAgaGlkZGVuOiBzdHJpbmc7XG4gICAgYmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xuICAgIHByb2ZpbGVOYW1lPzogc3RyaW5nO1xuICAgIHByb2ZpbGVCaW8/OiBzdHJpbmc7XG4gICAgZGV0YWlscz86IGFueTtcbiAgfVtdO1xuICBjb21wUG9zaXRpb24/OiAnbGVmdCcgfCAnY2VudGVyJyB8ICdyaWdodCc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudFBhZ2UoeyBuYW1lIH06IHsgbmFtZTogc3RyaW5nIH0pIHtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgeyBhZGRyZXNzIH0gPSB1c2VBcHBLaXRBY2NvdW50KCk7XG4gIGNvbnN0IFtwcm9maWxlRGF0YSwgc2V0UHJvZmlsZURhdGFdID0gdXNlU3RhdGU8UHJvZmlsZURhdGEgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdGF0dXNDaGVja2VkLCBzZXRTdGF0dXNDaGVja2VkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgeyBmZXRjaEJhbm5lck1ldGFkYXRhLCBmZXRjaFByb2ZpbGVQaWN0dXJlTWV0YWRhdGEsIGZldGNoQmFubmVyUGZwTWV0YWRhdGEgfSA9IHVzZU1ldGFkYXRhKCk7XG4gIGNvbnN0IFtiYW5uZXJQZnBNZXRhZGF0YSwgc2V0QmFubmVyUGZwTWV0YWRhdGFdID0gdXNlU3RhdGU8YW55PihudWxsKTtcblxuICAvLyBGaXJzdCBjaGVjayBwcm9maWxlIHN0YXR1c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGFzeW5jIGZ1bmN0aW9uIGNoZWNrU3RhdHVzKCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ1tDbGllbnRQYWdlXSBDaGVja2luZyBzdGF0dXMgZm9yOicsIG5hbWUpO1xuICAgICAgICBjb25zdCBzdGF0dXNSZXN1bHQgPSBhd2FpdCBjaGVja1Byb2ZpbGVTdGF0dXMobmFtZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbQ2xpZW50UGFnZV0gU3RhdHVzIHJlc3VsdDonLCBzdGF0dXNSZXN1bHQpO1xuXG4gICAgICAgIC8vIENoZWNrIGlmIHRoaXMgaXMgdGhlIHVzZXIncyBvd24gcHJvZmlsZVxuICAgICAgICBjb25zdCBpc093blByb2ZpbGUgPSBhZGRyZXNzICYmIHN0YXR1c1Jlc3VsdC5hZGRyZXNzICYmXG4gICAgICAgICAgYWRkcmVzcy50b0xvd2VyQ2FzZSgpID09PSBzdGF0dXNSZXN1bHQuYWRkcmVzcy50b0xvd2VyQ2FzZSgpO1xuICAgICAgICBjb25zb2xlLmxvZygnW0NsaWVudFBhZ2VdIElzIG93biBwcm9maWxlOicsIGlzT3duUHJvZmlsZSk7XG5cbiAgICAgICAgLy8gSWYgcHJvZmlsZSBpcyBub3QgYXBwcm92ZWQsIHJlZGlyZWN0IHRvIGVycm9yIHBhZ2VcbiAgICAgICAgLy8gV2Ugbm8gbG9uZ2VyIG1ha2UgYW4gZXhjZXB0aW9uIGZvciB0aGUgcHJvZmlsZSBvd25lclxuICAgICAgICBpZiAoIXN0YXR1c1Jlc3VsdC5pc0FwcHJvdmVkICYmIHN0YXR1c1Jlc3VsdC5zdGF0dXMgIT09ICdub3QtZm91bmQnKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1tDbGllbnRQYWdlXSBSZWRpcmVjdGluZyB0byBlcnJvciBwYWdlIGZvciBzdGF0dXM6Jywgc3RhdHVzUmVzdWx0LnN0YXR1cyk7XG4gICAgICAgICAgLy8gVXNlIHRoZSBuYW1lIHBhcmFtZXRlciBpbnN0ZWFkIG9mIGFkZHJlc3NcbiAgICAgICAgICBjb25zdCBuYW1lUGFyYW0gPSBzdGF0dXNSZXN1bHQubmFtZSA/IGAmbmFtZT0ke3N0YXR1c1Jlc3VsdC5uYW1lfWAgOiBgJm5hbWU9JHtuYW1lfWA7XG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBgL3Byb2ZpbGUtZXJyb3I/c3RhdHVzPSR7c3RhdHVzUmVzdWx0LnN0YXR1c30ke25hbWVQYXJhbX1gO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHNldFN0YXR1c0NoZWNrZWQodHJ1ZSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbQ2xpZW50UGFnZV0gRXJyb3IgY2hlY2tpbmcgcHJvZmlsZSBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgICBzZXRTdGF0dXNDaGVja2VkKHRydWUpOyAvLyBDb250aW51ZSBhbnl3YXkgaWYgc3RhdHVzIGNoZWNrIGZhaWxzXG4gICAgICB9XG4gICAgfVxuXG4gICAgY2hlY2tTdGF0dXMoKTtcbiAgfSwgW25hbWUsIGFkZHJlc3NdKTtcblxuICAvLyBUaGVuIGZldGNoIHByb2ZpbGUgZGF0YSBvbmNlIHN0YXR1cyBpcyBjaGVja2VkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFzdGF0dXNDaGVja2VkKSByZXR1cm47IC8vIFdhaXQgdW50aWwgc3RhdHVzIGNoZWNrIGlzIGNvbXBsZXRlXG5cbiAgICBhc3luYyBmdW5jdGlvbiBmZXRjaFByb2ZpbGVEYXRhKCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIHByb2ZpbGUgZm9yIG5hbWU6JywgbmFtZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdDdXJyZW50IHBhdGhuYW1lOicsIHBhdGhuYW1lKTtcblxuICAgICAgICAvLyBGZXRjaCBwcm9maWxlIGRhdGEgYnkgbmFtZVxuICAgICAgICAvLyBBbHdheXMgdHJlYXQgdGhlIHBhcmFtZXRlciBhcyBhIG5hbWUsIG5vdCBhbiBhZGRyZXNzXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvcHJvZmlsZS8ke25hbWV9YCk7XG5cbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgIC8vIEhhbmRsZSBlcnJvcnMgd2l0aCBtb3JlIHNwZWNpZmljIG1lc3NhZ2VzXG4gICAgICAgICAgY29uc3QgZXJyb3JSZXNwb25zZSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwNCkge1xuICAgICAgICAgICAgLy8gRm9yIDQwNCBlcnJvcnMsIHNob3cgYSB1c2VyLWZyaWVuZGx5IG1lc3NhZ2VcbiAgICAgICAgICAgIGlmIChlcnJvclJlc3BvbnNlLmVycm9yID09PSAnTm8gcHJvZmlsZXMgZXhpc3QgaW4gdGhlIGRhdGFiYXNlIHlldCcpIHtcbiAgICAgICAgICAgICAgc2V0RXJyb3IoJ05vIHByb2ZpbGVzIGhhdmUgYmVlbiBjcmVhdGVkIHlldC4gQmUgdGhlIGZpcnN0IHRvIGNyZWF0ZSBvbmUhJyk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yUmVzcG9uc2UuZXJyb3IgPT09ICdQcm9maWxlIGNvbXBvbmVudHMgbm90IHByb3Blcmx5IGluaXRpYWxpemVkJykge1xuICAgICAgICAgICAgICBzZXRFcnJvcignUHJvZmlsZSBzeXN0ZW0gaXMgbm90IHByb3Blcmx5IGluaXRpYWxpemVkLiBQbGVhc2UgY29udGFjdCBzdXBwb3J0LicpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgc2V0RXJyb3IoYFByb2ZpbGUgXCIke25hbWV9XCIgbm90IGZvdW5kYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgICAgIHJldHVybjsgLy8gRXhpdCBlYXJseSB3aXRob3V0IHRocm93aW5nIGFuIGVycm9yXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gRm9yIG90aGVyIGVycm9ycywgbG9nIGFuZCB0aHJvd1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBwcm9maWxlOicsIGVycm9yUmVzcG9uc2UpO1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvclJlc3BvbnNlLmVycm9yIHx8ICdGYWlsZWQgdG8gbG9hZCBwcm9maWxlJyk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRQcm9maWxlRGF0YShkYXRhKTtcblxuICAgICAgICAvLyBGZXRjaCBiYW5uZXJwZnAgbWV0YWRhdGFcbiAgICAgICAgaWYgKGRhdGEuYWRkcmVzcykge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBGZXRjaCBiYW5uZXJwZnAgbWV0YWRhdGEgZmlyc3RcbiAgICAgICAgICAgIGNvbnN0IGJhbm5lclBmcE1ldGEgPSBhd2FpdCBmZXRjaEJhbm5lclBmcE1ldGFkYXRhKGRhdGEuYWRkcmVzcyk7XG4gICAgICAgICAgICBpZiAoYmFubmVyUGZwTWV0YSkge1xuICAgICAgICAgICAgICBzZXRCYW5uZXJQZnBNZXRhZGF0YShiYW5uZXJQZnBNZXRhKTtcblxuICAgICAgICAgICAgICAvLyBOb3cgZmV0Y2ggYmFubmVyIGFuZCBwcm9maWxlIHBpY3R1cmUgbWV0YWRhdGEgd2hpY2ggd2lsbCB1c2UgdGhlIGJhbm5lcnBmcCBkYXRhXG4gICAgICAgICAgICAgIGNvbnN0IGJhbm5lck1ldGEgPSBhd2FpdCBmZXRjaEJhbm5lck1ldGFkYXRhKGRhdGEuYWRkcmVzcyk7XG4gICAgICAgICAgICAgIGlmIChiYW5uZXJNZXRhKSB7XG4gICAgICAgICAgICAgICAgc2V0QmFubmVyTWV0YWRhdGEoYmFubmVyTWV0YSk7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICBjb25zdCBwcm9maWxlUGljTWV0YSA9IGF3YWl0IGZldGNoUHJvZmlsZVBpY3R1cmVNZXRhZGF0YShkYXRhLmFkZHJlc3MpO1xuICAgICAgICAgICAgICBpZiAocHJvZmlsZVBpY01ldGEpIHtcbiAgICAgICAgICAgICAgICBzZXRQcm9maWxlUGljdHVyZU1ldGFkYXRhKHByb2ZpbGVQaWNNZXRhKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gY2F0Y2ggKG1ldGFkYXRhRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNvbXBvbmVudCBtZXRhZGF0YTonLCBtZXRhZGF0YUVycm9yKTtcbiAgICAgICAgICAgIC8vIENvbnRpbnVlIHNob3dpbmcgdGhlIHByb2ZpbGUgZXZlbiBpZiBtZXRhZGF0YSBmZXRjaCBmYWlsc1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBwcm9maWxlOicsIGVycik7XG4gICAgICAgIHNldEVycm9yKGVyci5tZXNzYWdlIHx8ICdBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBsb2FkaW5nIHRoZSBwcm9maWxlJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBmZXRjaFByb2ZpbGVEYXRhKCk7XG4gIH0sIFtuYW1lLCBmZXRjaEJhbm5lck1ldGFkYXRhLCBmZXRjaFByb2ZpbGVQaWN0dXJlTWV0YWRhdGEsIHN0YXR1c0NoZWNrZWRdKTtcblxuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cInJlbGF0aXZlIG1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctNHhsIG14LWF1dG8gcHgtNCBzbTpweC02IHBiLTggei0xMCByZWxhdGl2ZVwiPlxuICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIG1pbi1oLVszMDBweF1cIj5cbiAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogZXJyb3IgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtOTAwLzIwIGJvcmRlciBib3JkZXItcmVkLTkwMC81MCByb3VuZGVkLWxnIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtcmVkLTQwMCBmb250LW1lZGl1bSBtYi0yXCI+RXJyb3I8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTMwMCBtYi00XCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnLyd9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1yZWQtOTAwLzUwIGhvdmVyOmJnLXJlZC04MDAvNTAgdGV4dC13aGl0ZSByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgR28gQmFja1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiBwcm9maWxlRGF0YSA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMCBib3JkZXIgYm9yZGVyLW5ldXRyYWwtNzAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAge3Byb2ZpbGVEYXRhLmNvbXBvbmVudHNcbiAgICAgICAgICAgICAgLmZpbHRlcihjID0+IGMuaGlkZGVuICE9PSAnWScpXG4gICAgICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBwYXJzZUludChhLm9yZGVyKSAtIHBhcnNlSW50KGIub3JkZXIpKVxuICAgICAgICAgICAgICAubWFwKChjb21wb25lbnQsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gU2tpcCBvbGQgY29tcG9uZW50IHR5cGVzXG4gICAgICAgICAgICAgICAgaWYgKGNvbXBvbmVudC5jb21wb25lbnRUeXBlID09PSAnYmFubmVyJyB8fCBjb21wb25lbnQuY29tcG9uZW50VHlwZSA9PT0gJ3Byb2ZpbGVQaWN0dXJlJykge1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChjb21wb25lbnQuY29tcG9uZW50VHlwZSA9PT0gJ2RldGFpbHMnKSB7XG4gICAgICAgICAgICAgICAgICAvLyBTa2lwIHJlbmRlcmluZyBkZXByZWNhdGVkICdkZXRhaWxzJyBjb21wb25lbnRcbiAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoY29tcG9uZW50LmNvbXBvbmVudFR5cGUgPT09ICdoZXJvJykge1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2BoZXJvLSR7Y29tcG9uZW50Lm9yZGVyfWB9IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxSZW5kZXJIZXJvXG4gICAgICAgICAgICAgICAgICAgICAgICBhZGRyZXNzPXtwcm9maWxlRGF0YS5hZGRyZXNzfVxuICAgICAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50RGF0YT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5jb21wb25lbnQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IHByb2ZpbGVEYXRhLmFkZHJlc3MsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluOiBwcm9maWxlRGF0YS5jaGFpblxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dQb3NpdGlvbkxhYmVsPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChjb21wb25lbnQuY29tcG9uZW50VHlwZSA9PT0gJ3NvY2lhbExpbmtzJykge1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2Bzb2NpYWxMaW5rcy0ke2NvbXBvbmVudC5vcmRlcn1gfSBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItbmV1dHJhbC03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UmVuZGVyU29jaWFsTGlua3NcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2ZpbGVEYXRhPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IHByb2ZpbGVEYXRhLmFkZHJlc3MsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluOiBwcm9maWxlRGF0YS5jaGFpbixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogcHJvZmlsZURhdGEubmFtZSB8fCAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmlvOiAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBvbmVudERhdGE9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uY29tcG9uZW50LFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRyZXNzOiBwcm9maWxlRGF0YS5hZGRyZXNzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbjogcHJvZmlsZURhdGEuY2hhaW5cbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBzaG93UG9zaXRpb25MYWJlbD17ZmFsc2V9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoY29tcG9uZW50LmNvbXBvbmVudFR5cGUgPT09ICdiYW5uZXJwZnAnKSB7XG4gICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17YGJhbm5lcnBmcC0ke2NvbXBvbmVudC5vcmRlcn1gfSBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItbmV1dHJhbC03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UmVuZGVyQmFubmVyUGZwXG4gICAgICAgICAgICAgICAgICAgICAgICBhZGRyZXNzPXtwcm9maWxlRGF0YS5hZGRyZXNzfVxuICAgICAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50RGF0YT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5jb21wb25lbnQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IHByb2ZpbGVEYXRhLmFkZHJlc3MsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluOiBwcm9maWxlRGF0YS5jaGFpblxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dQb3NpdGlvbkxhYmVsPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2ZpbGVOYW1lPXtwcm9maWxlRGF0YS5uYW1lIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgcHJvZmlsZUJpbz17Jyd9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctOTAwLzIwIGJvcmRlciBib3JkZXIteWVsbG93LTkwMC81MCByb3VuZGVkLWxnIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMCBmb250LW1lZGl1bSBtYi0yXCI+UHJvZmlsZSBOb3QgRm91bmQ8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTMwMCBtYi00XCI+XG4gICAgICAgICAgICAgIFRoZSBwcm9maWxlIFwie25hbWV9XCIgZG9lc24ndCBleGlzdCBvciBoYXMgYmVlbiByZW1vdmVkLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvJ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLXllbGxvdy05MDAvNTAgaG92ZXI6YmcteWVsbG93LTgwMC81MCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBHbyBCYWNrXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIiwiTG9hZGVyMiIsInVzZUFwcEtpdEFjY291bnQiLCJ1c2VNZXRhZGF0YSIsImNoZWNrUHJvZmlsZVN0YXR1cyIsIlJlbmRlckhlcm8iLCJSZW5kZXJTb2NpYWxMaW5rcyIsIlJlbmRlckJhbm5lclBmcCIsIkNsaWVudFBhZ2UiLCJuYW1lIiwicGF0aG5hbWUiLCJyb3V0ZXIiLCJhZGRyZXNzIiwicHJvZmlsZURhdGEiLCJzZXRQcm9maWxlRGF0YSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInN0YXR1c0NoZWNrZWQiLCJzZXRTdGF0dXNDaGVja2VkIiwiZmV0Y2hCYW5uZXJNZXRhZGF0YSIsImZldGNoUHJvZmlsZVBpY3R1cmVNZXRhZGF0YSIsImZldGNoQmFubmVyUGZwTWV0YWRhdGEiLCJiYW5uZXJQZnBNZXRhZGF0YSIsInNldEJhbm5lclBmcE1ldGFkYXRhIiwiY2hlY2tTdGF0dXMiLCJjb25zb2xlIiwibG9nIiwic3RhdHVzUmVzdWx0IiwiaXNPd25Qcm9maWxlIiwidG9Mb3dlckNhc2UiLCJpc0FwcHJvdmVkIiwic3RhdHVzIiwibmFtZVBhcmFtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiZmV0Y2hQcm9maWxlRGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImVycm9yUmVzcG9uc2UiLCJqc29uIiwiRXJyb3IiLCJkYXRhIiwiYmFubmVyUGZwTWV0YSIsImJhbm5lck1ldGEiLCJzZXRCYW5uZXJNZXRhZGF0YSIsInByb2ZpbGVQaWNNZXRhIiwic2V0UHJvZmlsZVBpY3R1cmVNZXRhZGF0YSIsIm1ldGFkYXRhRXJyb3IiLCJlcnIiLCJtZXNzYWdlIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjb21wb25lbnRzIiwiZmlsdGVyIiwiYyIsImhpZGRlbiIsInNvcnQiLCJhIiwiYiIsInBhcnNlSW50Iiwib3JkZXIiLCJtYXAiLCJjb21wb25lbnQiLCJpbmRleCIsImNvbXBvbmVudFR5cGUiLCJjb21wb25lbnREYXRhIiwiY2hhaW4iLCJzaG93UG9zaXRpb25MYWJlbCIsImJpbyIsInByb2ZpbGVOYW1lIiwicHJvZmlsZUJpbyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/[name]/ClientPage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/renders/render_bannerpfp.tsx":
/*!*****************************************************!*\
  !*** ./app/components/renders/render_bannerpfp.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderBannerPfp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_DecryptedText__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/DecryptedText */ \"(ssr)/./components/ui/DecryptedText.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction RenderBannerPfp({ address, componentData, showPositionLabel = false, profileName: propProfileName, profileBio: propProfileBio }) {\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileName, setProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileBio, setProfileBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileHorizontalPosition, setProfileHorizontalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [profileNameHorizontalPosition, setProfileNameHorizontalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [profileShape, setProfileShape] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('circular');\n    const [profileNameEffect, setProfileNameEffect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('decrypted');\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const getBorderRadiusClass = ()=>{\n        switch(profileShape){\n            case 'rectangular':\n                return 'rounded-none';\n            case 'squarish':\n                return 'rounded-md';\n            case 'circular':\n            default:\n                return 'rounded-full';\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderBannerPfp.useEffect\": ()=>{\n            const loadBannerPfpData = {\n                \"RenderBannerPfp.useEffect.loadBannerPfpData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const response = await fetch(`/api/bannerpfp/${address}`);\n                        if (!response.ok) {\n                            throw new Error('Failed to load banner/profile data');\n                        }\n                        const data = await response.json();\n                        setBannerPfpMetadata(data);\n                        // Set profile data from API response or props\n                        setProfileName(data.profileName || propProfileName || address.substring(0, 8));\n                        setProfileBio(data.profileBio || propProfileBio || '');\n                        setProfileHorizontalPosition(data.profileHorizontalPosition || 50);\n                        setProfileNameHorizontalPosition(data.profileNameHorizontalPosition || 50);\n                        setProfileShape(data.profileShape || 'circular');\n                        setProfileNameEffect(data.profileNameStyle?.effect || 'decrypted');\n                    } catch (error) {\n                        console.error('Error loading banner/profile data:', error);\n                        setError('Failed to load banner/profile data');\n                        // Set fallback data\n                        setProfileName(propProfileName || address.substring(0, 8));\n                        setProfileBio(propProfileBio || '');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"RenderBannerPfp.useEffect.loadBannerPfpData\"];\n            if (address) {\n                loadBannerPfpData();\n            }\n        }\n    }[\"RenderBannerPfp.useEffect\"], [\n        address,\n        propProfileName,\n        propProfileBio\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !bannerPfpMetadata) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-neutral-400 p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: componentData.backgroundColor,\n                    width: '100%',\n                    minWidth: '100%',\n                    boxSizing: 'border-box',\n                    paddingBottom: '0.5rem'\n                },\n                className: \"w-full min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full\",\n                        style: {\n                            marginBottom: profileShape === 'rectangular' ? '9rem' : '8rem',\n                            width: '100%'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-48 md:h-64 relative overflow-hidden\",\n                                ref: containerRef,\n                                style: {\n                                    width: '100%',\n                                    minWidth: '100%'\n                                },\n                                children: bannerPfpMetadata?.bannerUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: `url(${bannerPfpMetadata.bannerUrl})`,\n                                        backgroundSize: 'cover',\n                                        backgroundPosition: 'center',\n                                        transform: `translate(${bannerPfpMetadata.bannerPosition?.x || 0}px, ${bannerPfpMetadata.bannerPosition?.y || 0}px) scale(${bannerPfpMetadata.bannerScale || 1})`,\n                                        transformOrigin: 'center'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute flex justify-center\",\n                                style: {\n                                    bottom: profileShape === 'rectangular' ? '-4.5rem' : '-4rem',\n                                    left: `${profileHorizontalPosition}%`,\n                                    transform: 'translateX(-50%)',\n                                    zIndex: 10\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative`,\n                                    children: bannerPfpMetadata?.profileUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: bannerPfpMetadata.profileUrl,\n                                        alt: \"Profile\",\n                                        className: \"w-full h-full object-cover\",\n                                        style: {\n                                            transform: `scale(${bannerPfpMetadata.profileScale || 1})`,\n                                            transformOrigin: 'center'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-neutral-800 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 text-xs\",\n                                            children: \"No Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center w-full\",\n                        style: {\n                            left: `${profileNameHorizontalPosition}%`,\n                            transform: 'translateX(-50%)',\n                            position: 'relative'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center px-4\",\n                            children: [\n                                profileName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-2\",\n                                    style: {\n                                        color: componentData.fontColor || '#ffffff'\n                                    },\n                                    children: profileNameEffect === 'decrypted' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DecryptedText__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        text: profileName,\n                                        animateOn: \"view\",\n                                        className: \"font-bold\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this) : profileName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                profileBio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-300\",\n                                    style: {\n                                        color: componentData.fontColor || '#ffffff'\n                                    },\n                                    children: profileBio\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-purple-900/30 text-purple-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Banner/PFP\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/renders/render_bannerpfp.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/renders/render_hero.tsx":
/*!************************************************!*\
  !*** ./app/components/renders/render_hero.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderHero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_sticky_scroll_reveal_demo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sticky-scroll-reveal-demo */ \"(ssr)/./components/ui/sticky-scroll-reveal-demo.tsx\");\n/* harmony import */ var _components_ui_DecryptedText__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/DecryptedText */ \"(ssr)/./components/ui/DecryptedText.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction RenderHero({ address, componentData, showPositionLabel = false }) {\n    const [heroContent, setHeroContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderHero.useEffect\": ()=>{\n            const loadHeroContent = {\n                \"RenderHero.useEffect.loadHeroContent\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        // Fetch hero content from API\n                        const response = await fetch(`/api/hero/${address}`);\n                        if (!response.ok) {\n                            throw new Error('Failed to load hero content');\n                        }\n                        const data = await response.json();\n                        if (data && data.heroContent && Array.isArray(data.heroContent)) {\n                            // Ensure all loaded content has textEffect field for backward compatibility\n                            const contentWithTextEffect = data.heroContent.map({\n                                \"RenderHero.useEffect.loadHeroContent.contentWithTextEffect\": (item)=>({\n                                        ...item,\n                                        textEffect: item.textEffect || 'decrypted'\n                                    })\n                            }[\"RenderHero.useEffect.loadHeroContent.contentWithTextEffect\"]);\n                            setHeroContent(contentWithTextEffect);\n                        } else {\n                            setError('No hero content found');\n                        }\n                    } catch (error) {\n                        console.error('Error loading hero content:', error);\n                        setError('Failed to load hero content');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"RenderHero.useEffect.loadHeroContent\"];\n            if (address) {\n                loadHeroContent();\n            }\n        }\n    }[\"RenderHero.useEffect\"], [\n        address\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || heroContent.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-neutral-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error || 'No hero content available'\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    // Format content for the StickyScrollRevealDemo component\n    const formatContentForDemo = ()=>{\n        return heroContent.map((item, index)=>{\n            // For image content, we'll create a special wrapper with background color\n            // For color content, we'll use the gradient as before\n            let bgClass = '';\n            let customBackground = null;\n            if (item.contentType === 'color') {\n                if (item.colorGradient?.includes('cyan') && item.colorGradient?.includes('emerald')) {\n                    bgClass = 'bg-[linear-gradient(to_bottom_right,var(--cyan-500),var(--emerald-500))]';\n                } else if (item.colorGradient?.includes('orange') && item.colorGradient?.includes('yellow')) {\n                    bgClass = 'bg-[linear-gradient(to_bottom_right,var(--orange-500),var(--yellow-500))]';\n                } else if (item.colorGradient?.includes('pink') && item.colorGradient?.includes('purple')) {\n                    bgClass = 'bg-[linear-gradient(to_bottom_right,var(--pink-500),var(--purple-500))]';\n                } else if (item.colorGradient?.includes('blue') && item.colorGradient?.includes('indigo')) {\n                    bgClass = 'bg-[linear-gradient(to_bottom_right,var(--blue-500),var(--indigo-500))]';\n                } else {\n                    // No gradient specified\n                    bgClass = '';\n                }\n            } else if (item.contentType === 'image') {\n                // For image content, we'll override the background in the StickyScroll component\n                customBackground = componentData.backgroundColor || 'transparent';\n            }\n            return {\n                title: item.title,\n                // Conditionally use DecryptedText based on textEffect setting\n                description: item.textEffect === 'decrypted' || item.textEffect === undefined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DecryptedText__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    text: item.description,\n                    animateOn: \"view\",\n                    speed: 50,\n                    maxIterations: 15,\n                    revealDirection: \"start\",\n                    sequential: true,\n                    className: \"\",\n                    style: {\n                        color: componentData.fontColor || '#ffffff'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: componentData.fontColor || '#ffffff'\n                    },\n                    children: item.description\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this),\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex h-full w-full items-center justify-center text-white ${item.contentType === 'color' ? bgClass : ''}`,\n                    style: item.contentType === 'image' ? {\n                        backgroundColor: componentData.backgroundColor || 'transparent'\n                    } : {},\n                    children: item.contentType === 'image' && item.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: item.imageUrl,\n                        className: \"h-full w-full object-contain\",\n                        alt: item.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 15\n                    }, this) : item.contentText || item.title\n                }, `hero-content-${index}`, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this),\n                // Add custom background for image content to override the StickyScroll gradient\n                customBackground: item.contentType === 'image' ? customBackground : undefined,\n                customScrollContainerClass: \"h-[20rem] sm:h-[26rem] md:h-[28rem]\" // Add custom class for scroll container\n            };\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative bg-transparent rounded-xl border border-neutral-800 overflow-hidden w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[20rem] md:h-[30rem] bg-transparent flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || heroContent.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative bg-transparent rounded-xl border border-neutral-800 overflow-hidden w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[20rem] md:h-[30rem] bg-transparent flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-neutral-400 text-center max-w-md px-4\",\n                    children: error || 'No hero content available. Add some content in the Create page.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sticky_scroll_reveal_demo__WEBPACK_IMPORTED_MODULE_2__.StickyScrollRevealDemo, {\n                content: formatContentForDemo(),\n                fontColor: componentData.fontColor || undefined,\n                backgroundColor: componentData.backgroundColor\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Hero\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/renders/render_hero.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/renders/render_sociallinks.tsx":
/*!*******************************************************!*\
  !*** ./app/components/renders/render_sociallinks.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderSocialLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_floating_dock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/floating-dock */ \"(ssr)/./components/ui/floating-dock.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction RenderSocialLinks({ profileData, componentData, showPositionLabel = false }) {\n    const [referralCode, setReferralCode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [referralCount, setReferralCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Fetch user's referral code and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RenderSocialLinks.useEffect\": ()=>{\n            const fetchReferralStats = {\n                \"RenderSocialLinks.useEffect.fetchReferralStats\": async ()=>{\n                    try {\n                        const response = await fetch(`/api/referral/${profileData.address}`);\n                        if (response.ok) {\n                            const data = await response.json();\n                            setReferralCode(data.referralCode || null);\n                            setReferralCount(data.referralCount || 0);\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch referral stats:', error);\n                    }\n                }\n            }[\"RenderSocialLinks.useEffect.fetchReferralStats\"];\n            fetchReferralStats();\n        }\n    }[\"RenderSocialLinks.useEffect\"], [\n        profileData.address\n    ]);\n    // Copy referral code to clipboard\n    const copyReferralCode = async ()=>{\n        if (referralCode) {\n            try {\n                await navigator.clipboard.writeText(referralCode);\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (error) {\n                console.error('Failed to copy referral code:', error);\n            }\n        }\n    };\n    // Helper function to get social media icons\n    const getSocialIcon = (platform)=>{\n        // Map platform names to file names\n        const platformMap = {\n            twitter: '/twitter.gif',\n            github: '/github.gif',\n            linkedin: '/link.gif',\n            instagram: '/instagram.gif',\n            website: '/www.gif',\n            email: '/email.gif',\n            youtube: '/yt.gif',\n            twitch: '/twitch.gif',\n            telegram: '/tg.gif',\n            discord: '/discord.gif',\n            facebook: '/fb.gif',\n            cro: '/cro.gif'\n        };\n        // For custom platforms, use the link.gif icon\n        const iconPath = platform.startsWith('custom') ? '/link.gif' : platformMap[platform.toLowerCase()];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: iconPath,\n            alt: platform,\n            width: 64,\n            height: 64,\n            className: \"w-full h-full object-contain\",\n            style: {\n                background: 'transparent'\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    };\n    // Format social links if they exist\n    const formatSocialLinks = ()=>{\n        if (!componentData.socialLinks) {\n            // Display a message when socialLinks is null or undefined\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-1 text-center py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-neutral-400\",\n                    children: \"No social links added yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this);\n        }\n        try {\n            const links = typeof componentData.socialLinks === 'string' ? JSON.parse(componentData.socialLinks) : componentData.socialLinks;\n            if (!links || Object.keys(links).length === 0) {\n                // Display a message when links object is empty\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this);\n            }\n            // Define the desired order of platforms\n            const platformOrder = [\n                'twitter',\n                'discord',\n                'telegram',\n                'website',\n                'facebook',\n                'youtube',\n                'email',\n                'cro'\n            ];\n            // Add custom platforms to the end of the order array\n            const customPlatforms = Object.keys(links).filter((key)=>key.startsWith('custom'));\n            platformOrder.push(...customPlatforms);\n            // Format links for FloatingDock\n            let formattedLinks = Object.entries(links).filter(([_, url])=>url) // Filter out empty URLs\n            .map(([platform, url])=>{\n                // Convert url to string safely\n                const urlStr = String(url);\n                // Check if this is a custom link (platform starts with 'custom')\n                const isCustom = platform.startsWith('custom');\n                // For custom links, extract the custom label if it's in the format \"customLabel|url\"\n                let customLabel = '';\n                let finalUrl = urlStr;\n                if (isCustom && urlStr.includes('|')) {\n                    const parts = urlStr.split('|');\n                    if (parts.length >= 2) {\n                        customLabel = parts[0].trim();\n                        finalUrl = parts.slice(1).join('|').trim();\n                    }\n                }\n                // Special case for CRO platform\n                let title = '';\n                if (platform.toLowerCase() === 'cro') {\n                    title = 'CRO Referral';\n                } else {\n                    title = isCustom && customLabel ? customLabel : platform.charAt(0).toUpperCase() + platform.slice(1);\n                }\n                return {\n                    platform: platform.toLowerCase(),\n                    title: title,\n                    icon: getSocialIcon(platform),\n                    href: finalUrl.startsWith('http') ? finalUrl : `https://${finalUrl}`\n                };\n            });\n            // Sort links according to the desired order\n            formattedLinks.sort((a, b)=>{\n                const indexA = platformOrder.indexOf(a.platform);\n                const indexB = platformOrder.indexOf(b.platform);\n                // If both platforms are in the order array, sort by their position\n                if (indexA !== -1 && indexB !== -1) {\n                    return indexA - indexB;\n                }\n                // If only one platform is in the order array, prioritize it\n                if (indexA !== -1) return -1;\n                if (indexB !== -1) return 1;\n                // If neither platform is in the order array, maintain original order\n                return 0;\n            });\n            if (formattedLinks.length === 0) {\n                // Display a message when there are no social links with URLs\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    style: {\n                        color: componentData.fontColor || undefined\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-1\",\n                style: {\n                    color: componentData.fontColor || undefined\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_floating_dock__WEBPACK_IMPORTED_MODULE_1__.FloatingDock, {\n                    items: formattedLinks,\n                    desktopClassName: \"justify-center py-1 sm:py-1 md:py-2 flex-wrap\",\n                    backgroundColor: componentData.backgroundColor,\n                    fontColor: componentData.fontColor || undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this);\n        } catch (error) {\n            console.error('Error parsing social links:', error);\n            return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-hidden w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-2 px-2 w-full\",\n                style: {\n                    backgroundColor: componentData.backgroundColor\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-[800px] mx-auto\",\n                    children: [\n                        formatSocialLinks(),\n                        referralCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-transparent border border-neutral-700 rounded-lg px-3 py-2 flex flex-col gap-1\",\n                                style: {\n                                    backgroundColor: componentData.backgroundColor\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-neutral-400\",\n                                                children: \"My Referral Code:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-mono text-white\",\n                                                children: referralCode\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyReferralCode,\n                                                className: \"p-1 hover:bg-neutral-700 rounded transition-colors\",\n                                                title: \"Copy referral code\",\n                                                children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3 text-neutral-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-neutral-500 text-right\",\n                                        children: [\n                                            referralCount,\n                                            \" user\",\n                                            referralCount !== 1 ? 's' : '',\n                                            \" referred\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Social Links\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/renders/render_sociallinks.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/GridBgContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/GridBgContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GridBgProvider: () => (/* binding */ GridBgProvider),\n/* harmony export */   useGridBg: () => (/* binding */ useGridBg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GridBgProvider,useGridBg auto */ \n\nconst GridBgContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GridBgProvider({ children }) {\n    const [isGridBgDisabled, setIsGridBgDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridBgContext.Provider, {\n        value: {\n            isGridBgDisabled,\n            setIsGridBgDisabled\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\GridBgContext.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction useGridBg() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GridBgContext);\n    if (context === undefined) {\n        throw new Error('useGridBg must be used within a GridBgProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dHMvR3JpZEJnQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4RTtBQU85RSxNQUFNSSw4QkFBZ0JILG9EQUFhQSxDQUFnQ0k7QUFFNUQsU0FBU0MsZUFBZSxFQUFFQyxRQUFRLEVBQTJCO0lBQ2xFLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR04sK0NBQVFBLENBQUM7SUFFekQscUJBQ0UsOERBQUNDLGNBQWNNLFFBQVE7UUFDckJDLE9BQU87WUFDTEg7WUFDQUM7UUFDRjtrQkFFQ0Y7Ozs7OztBQUdQO0FBRU8sU0FBU0s7SUFDZCxNQUFNQyxVQUFVWCxpREFBVUEsQ0FBQ0U7SUFDM0IsSUFBSVMsWUFBWVIsV0FBVztRQUN6QixNQUFNLElBQUlTLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxhcHBcXGNvbnRleHRzXFxHcmlkQmdDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgR3JpZEJnQ29udGV4dFR5cGUge1xuICBpc0dyaWRCZ0Rpc2FibGVkOiBib29sZWFuO1xuICBzZXRJc0dyaWRCZ0Rpc2FibGVkOiAoZGlzYWJsZWQ6IGJvb2xlYW4pID0+IHZvaWQ7XG59XG5cbmNvbnN0IEdyaWRCZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PEdyaWRCZ0NvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgZnVuY3Rpb24gR3JpZEJnUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xuICBjb25zdCBbaXNHcmlkQmdEaXNhYmxlZCwgc2V0SXNHcmlkQmdEaXNhYmxlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgcmV0dXJuIChcbiAgICA8R3JpZEJnQ29udGV4dC5Qcm92aWRlclxuICAgICAgdmFsdWU9e3tcbiAgICAgICAgaXNHcmlkQmdEaXNhYmxlZCxcbiAgICAgICAgc2V0SXNHcmlkQmdEaXNhYmxlZFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9HcmlkQmdDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlR3JpZEJnKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChHcmlkQmdDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlR3JpZEJnIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBHcmlkQmdQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIkdyaWRCZ0NvbnRleHQiLCJ1bmRlZmluZWQiLCJHcmlkQmdQcm92aWRlciIsImNoaWxkcmVuIiwiaXNHcmlkQmdEaXNhYmxlZCIsInNldElzR3JpZEJnRGlzYWJsZWQiLCJQcm92aWRlciIsInZhbHVlIiwidXNlR3JpZEJnIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/GridBgContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/MetadataContext.tsx":
/*!******************************************!*\
  !*** ./app/contexts/MetadataContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetadataProvider: () => (/* binding */ MetadataProvider),\n/* harmony export */   useMetadata: () => (/* binding */ useMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MetadataProvider,useMetadata auto */ \n\nconst MetadataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MetadataProvider({ children }) {\n    const [bannerMetadata, setBannerMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profilePictureMetadata, setProfilePictureMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAddress, setCurrentAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to fetch banner metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchBannerMetadata = async (address)=>{\n        console.log('Banner component is deprecated, using bannerpfp instead');\n        // Try to get banner data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.bannerBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.bannerBlobUrl,\n                scale: bannerpfpData.bannerScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined\n            };\n            setBannerMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Function to fetch profile picture metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchProfilePictureMetadata = async (address, compPosition)=>{\n        console.log('ProfilePicture component is deprecated, using bannerpfp instead');\n        // Try to get profile picture data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.profileBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.profileBlobUrl,\n                scale: bannerpfpData.profileScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined,\n                shape: bannerpfpData.profileShape || 'circular'\n            };\n            setProfilePictureMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Track in-progress fetches to prevent duplicate calls\n    const fetchingAddresses = new Set();\n    // Function to fetch bannerpfp metadata\n    const fetchBannerPfpMetadata = async (address)=>{\n        // If we already have metadata for this address, return it\n        if (bannerPfpMetadata && currentAddress === address) {\n            console.log('MetadataContext: Using cached bannerpfp metadata for', address);\n            return bannerPfpMetadata;\n        }\n        // If we're already fetching this address, wait for it to complete\n        if (fetchingAddresses.has(address)) {\n            console.log('MetadataContext: Already fetching bannerpfp metadata for', address);\n            // Wait a bit and return the current metadata (which should be updated by the in-progress fetch)\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            return bannerPfpMetadata;\n        }\n        // Mark this address as being fetched\n        fetchingAddresses.add(address);\n        try {\n            console.log('MetadataContext: Fetching bannerpfp metadata for', address);\n            const response = await fetch(`/api/bannerpfp/${address}`);\n            if (!response.ok) {\n                // If the response is not OK, log the error but don't throw\n                // This allows the UI to continue rendering even if metadata is missing\n                console.warn(`Failed to fetch bannerpfp metadata: ${response.status} ${response.statusText}`);\n                // For 404 errors, we'll create a default metadata object\n                if (response.status === 404) {\n                    console.log('Creating default bannerpfp metadata since none exists');\n                    const defaultMetadata = {\n                        profileName: address.substring(0, 8),\n                        profileShape: 'circular',\n                        profileHorizontalPosition: 50,\n                        profileNameHorizontalPosition: 50,\n                        profileNameStyle: 'typewriter'\n                    };\n                    setBannerPfpMetadata(defaultMetadata);\n                    setCurrentAddress(address);\n                    return defaultMetadata;\n                }\n                return null;\n            }\n            const metadata = await response.json();\n            console.log('MetadataContext: Received bannerpfp metadata:', metadata);\n            if (metadata) {\n                console.log('MetadataContext: Setting bannerpfp metadata:', metadata);\n                setBannerPfpMetadata(metadata);\n                setCurrentAddress(address);\n                return metadata;\n            }\n        } catch (error) {\n            console.error('Failed to load bannerpfp metadata:', error);\n            // Create a default metadata object on error\n            const defaultMetadata = {\n                profileName: address.substring(0, 8),\n                profileShape: 'circular',\n                profileHorizontalPosition: 50,\n                profileNameHorizontalPosition: 50,\n                profileNameStyle: 'typewriter'\n            };\n            setBannerPfpMetadata(defaultMetadata);\n            setCurrentAddress(address);\n            return defaultMetadata;\n        } finally{\n            // Remove this address from the fetching set\n            fetchingAddresses.delete(address);\n        }\n        return null;\n    };\n    // Function to clear metadata (useful when changing addresses)\n    const clearMetadata = ()=>{\n        setBannerMetadata(null);\n        setProfilePictureMetadata(null);\n        setBannerPfpMetadata(null);\n        setCurrentAddress(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetadataContext.Provider, {\n        value: {\n            bannerMetadata,\n            profilePictureMetadata,\n            bannerPfpMetadata,\n            fetchBannerMetadata,\n            fetchProfilePictureMetadata,\n            fetchBannerPfpMetadata,\n            clearMetadata\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\MetadataContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\nfunction useMetadata() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MetadataContext);\n    if (context === undefined) {\n        throw new Error('useMetadata must be used within a MetadataProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/MetadataContext.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(ssr)/./config/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(ssr)/./components/ThemeProvider.tsx\");\n/* harmony import */ var _app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/contexts/GridBgContext */ \"(ssr)/./app/contexts/GridBgContext.tsx\");\n/* harmony import */ var _hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useWalletConnectionPersistence */ \"(ssr)/./hooks/useWalletConnectionPersistence.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Create a client outside the component\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1\n        }\n    }\n});\n// Inner component to use hooks after WagmiProvider is mounted\nfunction ProvidersContent({ children }) {\n    // Use the wallet connection persistence hook\n    (0,_hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__.useWalletConnectionPersistence)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__.MetadataProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__.GridBgProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Providers.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Providers.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_8__.WagmiProvider, {\n        config: _config__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: false,\n                forcedTheme: \"dark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProvidersContent, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ThemeProvider.tsx":
/*!**************************************!*\
  !*** ./components/ThemeProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxjb21wb25lbnRzXFxUaGVtZVByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gJ25leHQtdGhlbWVzJ1xuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/DecryptedText.tsx":
/*!*****************************************!*\
  !*** ./components/ui/DecryptedText.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DecryptedText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst styles = {\n    wrapper: {\n        display: 'inline-block',\n        whiteSpace: 'pre-wrap'\n    },\n    srOnly: {\n        position: 'absolute',\n        width: '1px',\n        height: '1px',\n        padding: 0,\n        margin: '-1px',\n        overflow: 'hidden',\n        clip: 'rect(0,0,0,0)',\n        border: 0\n    }\n};\nfunction DecryptedText({ text, speed = 50, maxIterations = 10, sequential = false, revealDirection = 'start', useOriginalCharsOnly = false, characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*()_+', className = '', parentClassName = '', encryptedClassName = '', animateOn = 'hover', style, ...props }) {\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(text);\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrambling, setIsScrambling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [revealedIndices, setRevealedIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [hasAnimated, setHasAnimated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const getRandomChar = (originalChar)=>{\n        if (originalChar === ' ') return ' ';\n        if (useOriginalCharsOnly) {\n            const uniqueChars = Array.from(new Set(text.split(''))).filter((char)=>char !== ' ');\n            return uniqueChars[Math.floor(Math.random() * uniqueChars.length)] || originalChar;\n        }\n        return characters[Math.floor(Math.random() * characters.length)];\n    };\n    const scrambleText = ()=>{\n        if (isScrambling) return;\n        setIsScrambling(true);\n        setRevealedIndices(new Set());\n        const textArray = text.split('');\n        const iterations = Array(textArray.length).fill(0);\n        let completedChars = 0;\n        const interval = setInterval(()=>{\n            setDisplayText(textArray.map((char, index)=>{\n                if (char === ' ') return ' ';\n                if (iterations[index] < maxIterations) {\n                    iterations[index]++;\n                    return getRandomChar(char);\n                }\n                if (!revealedIndices.has(index)) {\n                    setRevealedIndices((prev)=>new Set(prev).add(index));\n                    completedChars++;\n                }\n                return char;\n            }).join(''));\n            if (completedChars >= textArray.filter((char)=>char !== ' ').length) {\n                clearInterval(interval);\n                setIsScrambling(false);\n                setHasAnimated(true);\n            }\n        }, speed);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DecryptedText.useEffect\": ()=>{\n            if (animateOn === 'view' && !hasAnimated) {\n                const observer = new IntersectionObserver({\n                    \"DecryptedText.useEffect\": ([entry])=>{\n                        if (entry.isIntersecting) {\n                            scrambleText();\n                            observer.disconnect();\n                        }\n                    }\n                }[\"DecryptedText.useEffect\"], {\n                    threshold: 0.1\n                });\n                if (containerRef.current) {\n                    observer.observe(containerRef.current);\n                }\n                return ({\n                    \"DecryptedText.useEffect\": ()=>observer.disconnect()\n                })[\"DecryptedText.useEffect\"];\n            }\n        }\n    }[\"DecryptedText.useEffect\"], [\n        animateOn,\n        hasAnimated\n    ]);\n    const handleMouseEnter = ()=>{\n        if (animateOn === 'hover') {\n            setIsHovering(true);\n            scrambleText();\n        }\n    };\n    const handleMouseLeave = ()=>{\n        if (animateOn === 'hover') {\n            setIsHovering(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: parentClassName,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: styles.srOnly,\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\DecryptedText.tsx\",\n                lineNumber: 143,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                ref: containerRef,\n                style: {\n                    ...styles.wrapper,\n                    ...style\n                },\n                className: `${className} ${isScrambling ? encryptedClassName : ''}`,\n                onMouseEnter: handleMouseEnter,\n                onMouseLeave: handleMouseLeave,\n                \"aria-hidden\": \"true\",\n                ...props,\n                children: displayText\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\DecryptedText.tsx\",\n                lineNumber: 144,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\DecryptedText.tsx\",\n        lineNumber: 142,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/DecryptedText.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/floating-dock.tsx":
/*!*****************************************!*\
  !*** ./components/ui/floating-dock.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingDock: () => (/* binding */ FloatingDock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ FloatingDock auto */ \n\n\n\nconst FloatingDock = ({ items, desktopClassName, mobileClassName, backgroundColor, fontColor })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex flex-wrap justify-center gap-2 sm:gap-2 md:gap-3 lg:gap-3\", desktopClassName),\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: item.href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105\",\n                    style: {\n                        backgroundColor: backgroundColor || 'rgba(255, 255, 255, 0.1)',\n                        color: fontColor || '#ffffff',\n                        border: '1px solid rgba(255, 255, 255, 0.2)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: item.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium whitespace-nowrap\",\n                            children: item.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 21\n                }, undefined)\n            }, item.title, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 30,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 28,\n        columnNumber: 9\n    }, undefined);\n};\nconst FloatingDockDesktop = ({ items, className })=>{\n    let mouseX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useMotionValue)(Infinity);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        onMouseMove: (e)=>mouseX.set(e.pageX),\n        onMouseLeave: ()=>mouseX.set(Infinity),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"mx-auto hidden h-16 items-end gap-4 rounded-2xl bg-gray-50 px-4 pb-3 md:flex dark:bg-neutral-900\", className),\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconContainer, {\n                mouseX: mouseX,\n                ...item\n            }, item.title, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                lineNumber: 74,\n                columnNumber: 17\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 65,\n        columnNumber: 9\n    }, undefined);\n};\nfunction IconContainer({ mouseX, title, icon, href }) {\n    let ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    let distance = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(mouseX, {\n        \"IconContainer.useTransform[distance]\": (val)=>{\n            let bounds = ref.current?.getBoundingClientRect() ?? {\n                x: 0,\n                width: 0\n            };\n            return val - bounds.x - bounds.width / 2;\n        }\n    }[\"IconContainer.useTransform[distance]\"]);\n    let widthTransform = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        40,\n        80,\n        40\n    ]);\n    let heightTransform = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        40,\n        80,\n        40\n    ]);\n    let widthTransformIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        20,\n        40,\n        20\n    ]);\n    let heightTransformIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(distance, [\n        -150,\n        0,\n        150\n    ], [\n        20,\n        40,\n        20\n    ]);\n    let width = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(widthTransform, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    let height = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(heightTransform, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    let widthIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(widthTransformIcon, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    let heightIcon = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(heightTransformIcon, {\n        mass: 0.1,\n        stiffness: 150,\n        damping: 12\n    });\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            ref: ref,\n            style: {\n                width,\n                height\n            },\n            onMouseEnter: ()=>setHovered(true),\n            onMouseLeave: ()=>setHovered(false),\n            className: \"relative flex aspect-square items-center justify-center rounded-full bg-transparent border border-neutral-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10,\n                            x: \"-50%\"\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            x: \"-50%\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: 2,\n                            x: \"-50%\"\n                        },\n                        className: \"absolute -top-8 left-1/2 w-fit rounded-md border border-gray-200 bg-gray-100 px-2 py-0.5 text-xs whitespace-pre text-neutral-700 dark:border-neutral-900 dark:bg-neutral-800 dark:text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    style: {\n                        width: widthIcon,\n                        height: heightIcon\n                    },\n                    className: \"flex items-center justify-center\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n            lineNumber: 131,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\floating-dock.tsx\",\n        lineNumber: 130,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/floating-dock.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sticky-scroll-reveal-demo.tsx":
/*!*****************************************************!*\
  !*** ./components/ui/sticky-scroll-reveal-demo.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StickyScrollRevealDemo: () => (/* binding */ StickyScrollRevealDemo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _sticky_scroll_reveal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sticky-scroll-reveal */ \"(ssr)/./components/ui/sticky-scroll-reveal.tsx\");\n/* __next_internal_client_entry_do_not_use__ StickyScrollRevealDemo auto */ \n\n\nconst defaultContent = [\n    {\n        title: \"Collaborative Editing\",\n        description: \"Work together in real time with your team, clients, and stakeholders. Collaborate on documents, share ideas, and make decisions quickly. With our platform, you can streamline your workflow and increase productivity.\",\n        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,var(--cyan-500),var(--emerald-500))] text-white\",\n            children: \"Collaborative Editing\"\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        title: \"Real time changes\",\n        description: \"See changes as they happen. With our platform, you can track every modification in real time. No more confusion about the latest version of your project. Say goodbye to the chaos of version control and embrace the simplicity of real-time updates.\",\n        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full w-full items-center justify-center text-white\",\n            style: {\n                backgroundColor: 'rgba(17, 17, 17, 0.8)'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: \"/linear.webp\",\n                width: 300,\n                height: 300,\n                className: \"h-full w-full object-contain\",\n                alt: \"linear board demo\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        title: \"Version control\",\n        description: \"Experience real-time updates and never stress about version control again. Our platform ensures that you're always working on the most recent version of your project, eliminating the need for constant manual updates. Stay in the loop, keep your team aligned, and maintain the flow of your work without any interruptions.\",\n        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,var(--orange-500),var(--yellow-500))] text-white\",\n            children: \"Version control\"\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        title: \"Running out of content\",\n        description: \"Experience real-time updates and never stress about version control again. Our platform ensures that you're always working on the most recent version of your project, eliminating the need for constant manual updates. Stay in the loop, keep your team aligned, and maintain the flow of your work without any interruptions.\",\n        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,var(--cyan-500),var(--emerald-500))] text-white\",\n            children: \"Running out of content\"\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, undefined)\n    }\n];\nfunction StickyScrollRevealDemo({ content = defaultContent, fontColor = '#ffffff', backgroundColor }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full py-0 sm:py-1 mx-0 px-0 overflow-x-hidden overflow-y-visible relative\",\n        style: {\n            backgroundColor: backgroundColor || 'transparent'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sticky_scroll_reveal__WEBPACK_IMPORTED_MODULE_2__.StickyScroll, {\n            content: content,\n            fontColor: fontColor\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal-demo.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sticky-scroll-reveal-demo.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sticky-scroll-reveal.tsx":
/*!************************************************!*\
  !*** ./components/ui/sticky-scroll-reveal.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StickyScroll: () => (/* binding */ StickyScroll)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ StickyScroll auto */ \n\n\n\n\nconst StickyScroll = ({ content, contentClassName, fontColor })=>{\n    const [activeCard, setActiveCard] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        container: ref,\n        offset: [\n            \"start start\",\n            \"end end\"\n        ]\n    });\n    const cardLength = content.length;\n    (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useMotionValueEvent)(scrollYProgress, \"change\", {\n        \"StickyScroll.useMotionValueEvent\": (latest)=>{\n            const cardsBreakpoints = content.map({\n                \"StickyScroll.useMotionValueEvent.cardsBreakpoints\": (_, index)=>index / cardLength\n            }[\"StickyScroll.useMotionValueEvent.cardsBreakpoints\"]);\n            const closestBreakpointIndex = cardsBreakpoints.reduce({\n                \"StickyScroll.useMotionValueEvent.closestBreakpointIndex\": (acc, breakpoint, index)=>{\n                    const distance = Math.abs(latest - breakpoint);\n                    if (distance < Math.abs(latest - cardsBreakpoints[acc])) {\n                        return index;\n                    }\n                    return acc;\n                }\n            }[\"StickyScroll.useMotionValueEvent.closestBreakpointIndex\"], 0);\n            setActiveCard(closestBreakpointIndex);\n        }\n    }[\"StickyScroll.useMotionValueEvent\"]);\n    const backgroundGradient = `linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))`;\n    const sectionRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    // Check if there's only one section\n    const isSingleSection = content.length === 1;\n    // For single section, use a completely different layout\n    if (isSingleSection) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            animate: {},\n            className: \"relative flex flex-row h-[20rem] sm:h-[26rem] md:h-[28rem] justify-center space-x-0 sm:space-x-2 md:space-x-4 py-0 sm:py-6 md:py-10 px-0 sm:px-1 md:px-3 scroll-smooth w-full m-0 xs:mr-2 overflow-x-hidden overflow-y-scroll custom-scrollbar\",\n            style: {},\n            ref: ref,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"div relative flex items-start px-2 sm:px-3 md:px-4 pr-4 sm:pr-4 md:pr-5 w-full m-0 overflow-visible text-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-[12rem] sm:max-w-[16rem] md:max-w-xs overflow-visible\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"-mt-0 sm:-mt-12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                className: \"text-lg sm:text-xl md:text-2xl font-bold\",\n                                style: {\n                                    color: fontColor || '#ffffff'\n                                },\n                                children: content[0].title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                className: \"text-xs sm:text-sm md:text-base mt-3 sm:mt-4 md:mt-6 max-w-full sm:max-w-[12rem] md:max-w-xs line-clamp-4 sm:line-clamp-5 md:line-clamp-6\",\n                                style: {\n                                    color: fontColor || '#ffffff'\n                                },\n                                children: content[0].description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center sticky top-0 h-full overflow-visible pr-0 container-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: content[0]?.customBackground || backgroundGradient\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-52 w-52 sm:h-64 sm:w-64 md:w-72 md:h-72 overflow-hidden bg-transparent xs:mr-2 content-container\", contentClassName),\n                        children: content[0] && content[0].content ? content[0].content : null\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n            lineNumber: 54,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        animate: {},\n        className: \"relative flex flex-row h-[20rem] sm:h-[26rem] md:h-[28rem] justify-center space-x-0 sm:space-x-2 md:space-x-4 py-0 sm:py-6 md:py-10 px-0 sm:px-1 md:px-3 scroll-smooth w-full m-0 xs:mr-2 overflow-x-hidden overflow-y-scroll custom-scrollbar\",\n        style: {},\n        ref: ref,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"div relative flex items-start px-2 sm:px-3 md:px-4 pr-4 sm:pr-4 md:pr-5 w-full m-0 overflow-visible text-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-[12rem] sm:max-w-[16rem] md:max-w-xs overflow-visible\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"-mt-0 sm:-mt-12\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 21\n                        }, undefined),\n                        content.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-24 sm:my-24 md:my-32\",\n                                ref: (el)=>{\n                                    if (sectionRefs.current) {\n                                        sectionRefs.current[index] = el;\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: activeCard === index ? 1 : 0.3\n                                        },\n                                        className: \"text-lg sm:text-xl md:text-2xl font-bold\",\n                                        style: {\n                                            color: fontColor || '#ffffff'\n                                        },\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: activeCard === index ? 1 : 0.3\n                                        },\n                                        className: \"text-xs sm:text-sm md:text-base mt-3 sm:mt-4 md:mt-6 max-w-full sm:max-w-[12rem] md:max-w-xs line-clamp-4 sm:line-clamp-5 md:line-clamp-6\",\n                                        style: {\n                                            color: fontColor || '#ffffff'\n                                        },\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, item.title + index, true, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[2rem] sm:h-[8rem]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                lineNumber: 105,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center sticky top-0 h-full overflow-visible pr-0 container-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: content[activeCard]?.customBackground || backgroundGradient\n                    },\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-52 w-52 sm:h-64 sm:w-64 md:w-72 md:h-72 overflow-hidden bg-transparent xs:mr-2 content-container\", contentClassName),\n                    children: content[activeCard] && content[activeCard].content ? content[activeCard].content : null\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n                lineNumber: 149,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ui\\\\sticky-scroll-reveal.tsx\",\n        lineNumber: 99,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sticky-scroll-reveal.tsx\n");

/***/ }),

/***/ "(ssr)/./config/index.tsx":
/*!**************************!*\
  !*** ./config/index.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   networks: () => (/* binding */ networks),\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit-adapter-solana */ \"(ssr)/./node_modules/@reown/appkit-adapter-solana/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n/* __next_internal_client_entry_do_not_use__ projectId,metadata,networks,wagmiAdapter,config auto */ \n\n\n\nconst projectId = \"63cb69e4a11f991fe106897d3eede1ed\";\nif (!projectId) {\n    throw new Error(\"Project ID is not defined\");\n}\n// Validate project ID format\nif (projectId.length !== 32) {\n    console.warn('Project ID may be invalid - should be 32 characters');\n}\nconst metadata = {\n    name: 'Web3Socials',\n    description: 'Web3 Social Platform',\n    url:  false ? 0 : 'https://web3socials.fun',\n    icons: [\n        'https://avatars.githubusercontent.com/u/179229932'\n    ]\n};\n// Temporarily reduce networks to test 403 error - you can add more back later\nconst networks = [\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronos,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.mainnet,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.arbitrum,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.sepolia,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.solana,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronoszkEVM\n];\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    ssr: true,\n    projectId,\n    networks\n});\nconst solanaWeb3JsAdapter = new _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__.SolanaAdapter();\nconst generalConfig = {\n    projectId,\n    networks,\n    metadata,\n    themeMode: 'dark',\n    themeVariables: {\n        '--w3m-accent': '#000000'\n    }\n};\n// Prevent ethereum object conflicts\nif (false) {}\n(0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter,\n        solanaWeb3JsAdapter\n    ],\n    ...generalConfig,\n    features: {\n        swaps: false,\n        onramp: false,\n        email: true,\n        socials: false,\n        history: false,\n        analytics: false,\n        allWallets: true,\n        send: false\n    },\n    // Disable features that might cause network requests\n    featuredWalletIds: []\n});\nconst config = wagmiAdapter.wagmiConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./config/index.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useWalletConnectionPersistence.ts":
/*!*************************************************!*\
  !*** ./hooks/useWalletConnectionPersistence.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWalletConnectionPersistence: () => (/* binding */ useWalletConnectionPersistence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useWalletConnectionPersistence auto */ \n\n\n/**\n * Simplified hook to log wallet connection status for debugging\n * AppKit handles connection persistence automatically\n */ function useWalletConnectionPersistence() {\n    const { isConnected, address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Log connection status for debugging only\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWalletConnectionPersistence.useEffect\": ()=>{\n            console.log('[Wallet Connection] Path changed to:', pathname);\n            console.log('[Wallet Connection] isConnected:', isConnected);\n            console.log('[Wallet Connection] address:', address);\n        }\n    }[\"useWalletConnectionPersistence.useEffect\"], [\n        pathname,\n        isConnected,\n        address\n    ]);\n    return {\n        isConnected,\n        address\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ob29rcy91c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7b0ZBRWtDO0FBQ3FCO0FBQ1Q7QUFFOUM7OztDQUdDLEdBQ00sU0FBU0c7SUFDZCxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdKLHFFQUFnQkE7SUFDakQsTUFBTUssV0FBV0osNERBQVdBO0lBRTVCLDJDQUEyQztJQUMzQ0YsZ0RBQVNBO29EQUFDO1lBQ1JPLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NGO1lBQ3BEQyxRQUFRQyxHQUFHLENBQUMsb0NBQW9DSjtZQUNoREcsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0g7UUFDOUM7bURBQUc7UUFBQ0M7UUFBVUY7UUFBYUM7S0FBUTtJQUVuQyxPQUFPO1FBQUVEO1FBQWFDO0lBQVE7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGhvb2tzXFx1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBcHBLaXRBY2NvdW50IH0gZnJvbSAnQHJlb3duL2FwcGtpdC9yZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbi8qKlxuICogU2ltcGxpZmllZCBob29rIHRvIGxvZyB3YWxsZXQgY29ubmVjdGlvbiBzdGF0dXMgZm9yIGRlYnVnZ2luZ1xuICogQXBwS2l0IGhhbmRsZXMgY29ubmVjdGlvbiBwZXJzaXN0ZW5jZSBhdXRvbWF0aWNhbGx5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UoKSB7XG4gIGNvbnN0IHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfSA9IHVzZUFwcEtpdEFjY291bnQoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIC8vIExvZyBjb25uZWN0aW9uIHN0YXR1cyBmb3IgZGVidWdnaW5nIG9ubHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBQYXRoIGNoYW5nZWQgdG86JywgcGF0aG5hbWUpO1xuICAgIGNvbnNvbGUubG9nKCdbV2FsbGV0IENvbm5lY3Rpb25dIGlzQ29ubmVjdGVkOicsIGlzQ29ubmVjdGVkKTtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBhZGRyZXNzOicsIGFkZHJlc3MpO1xuICB9LCBbcGF0aG5hbWUsIGlzQ29ubmVjdGVkLCBhZGRyZXNzXSk7XG5cbiAgcmV0dXJuIHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBcHBLaXRBY2NvdW50IiwidXNlUGF0aG5hbWUiLCJ1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UiLCJpc0Nvbm5lY3RlZCIsImFkZHJlc3MiLCJwYXRobmFtZSIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useWalletConnectionPersistence.ts\n");

/***/ }),

/***/ "(ssr)/./lib/profileStatus.ts":
/*!******************************!*\
  !*** ./lib/profileStatus.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkProfileStatus: () => (/* binding */ checkProfileStatus),\n/* harmony export */   clearProfileStatusCache: () => (/* binding */ clearProfileStatusCache)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ checkProfileStatus,clearProfileStatusCache auto */ // Cache for profile status to avoid repeated fetches\nlet profileStatusCache = {};\n/**\n * Fetch profile status from the database\n * @param addressOrName Address or name of the profile to check\n * @returns Promise with profile status\n */ async function checkProfileStatus(addressOrName) {\n    // Return from cache if available\n    if (profileStatusCache[addressOrName]) {\n        const cached = profileStatusCache[addressOrName];\n        return {\n            ...cached,\n            message: getStatusMessage(cached.status)\n        };\n    }\n    try {\n        // Fetch profile status from API\n        const response = await fetch(`/api/profile/check-status?identifier=${addressOrName}`);\n        if (!response.ok) {\n            throw new Error(`Failed to fetch profile status: ${response.statusText}`);\n        }\n        const data = await response.json();\n        // Cache the result\n        profileStatusCache[addressOrName] = {\n            status: data.status,\n            isApproved: data.status === 'approved',\n            address: data.address,\n            name: data.name,\n            transactionHash: data.transactionHash,\n            expiryDate: data.expiryDate\n        };\n        return {\n            status: data.status,\n            isApproved: data.status === 'approved',\n            message: getStatusMessage(data.status),\n            address: data.address,\n            name: data.name,\n            transactionHash: data.transactionHash,\n            expiryDate: data.expiryDate\n        };\n    } catch (error) {\n        console.error(`Error fetching profile status for ${addressOrName}:`, error);\n        // Return default values on error\n        return {\n            status: 'error',\n            isApproved: false,\n            message: 'Error checking profile status. Please try again later.'\n        };\n    }\n}\n/**\n * Get a user-friendly message based on profile status\n */ function getStatusMessage(status) {\n    switch(status){\n        case 'approved':\n            return 'Profile is approved and accessible.';\n        case 'new':\n            return 'This profile is new and waiting for approval. Please burn tokens and provide a transaction hash for verification.';\n        case 'in-progress':\n            return 'This profile is being processed. Please check back later.';\n        case 'pending':\n            return 'This profile is pending approval. Please check back later.';\n        case 'deleted':\n            return 'This profile has been deleted.';\n        case 'expired':\n            return 'This profile has expired. Please contact an admin to renew it.';\n        default:\n            return 'Profile status is unknown.';\n    }\n}\n/**\n * Clear the profile status cache\n */ function clearProfileStatusCache() {\n    profileStatusCache = {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/profileStatus.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/ClientPage.tsx */ \"(ssr)/./app/[name]/ClientPage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1QyU1Qm5hbWUlNUQlNUMlNUNDbGllbnRQYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcYXBwXFxcXFtuYW1lXVxcXFxDbGllbnRQYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(ssr)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pino-pretty":
/*!******************************!*\
  !*** external "pino-pretty" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("pino-pretty");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/lit-html","vendor-chunks/@lit","vendor-chunks/lit","vendor-chunks/next","vendor-chunks/viem","vendor-chunks/@walletconnect","vendor-chunks/@noble","vendor-chunks/@wagmi","vendor-chunks/ox","vendor-chunks/abitype","vendor-chunks/@solana","vendor-chunks/tr46","vendor-chunks/rpc-websockets","vendor-chunks/ws","vendor-chunks/bn.js","vendor-chunks/sonner","vendor-chunks/@tanstack","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/valtio","vendor-chunks/superstruct","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/big.js","vendor-chunks/borsh","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/text-encoding-utf-8","vendor-chunks/zustand","vendor-chunks/uuid","vendor-chunks/dayjs","vendor-chunks/eventemitter3","vendor-chunks/lit-element","vendor-chunks/detect-browser","vendor-chunks/jayson","vendor-chunks/@wallet-standard","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/quick-format-unescaped","vendor-chunks/proxy-compare","vendor-chunks/mipd","vendor-chunks/@swc","vendor-chunks/destr","vendor-chunks/derive-valtio","vendor-chunks/utf-8-validate","vendor-chunks/safe-buffer","vendor-chunks/wagmi","vendor-chunks/atomic-sleep","vendor-chunks/bufferutil","vendor-chunks/bs58","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();