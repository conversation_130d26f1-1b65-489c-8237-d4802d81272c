/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[name]/page";
exports.ids = ["app/[name]/page"];
exports.modules = {

/***/ "(rsc)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\app\\[name]\\ClientPage.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/[name]/layout.tsx":
/*!*******************************!*\
  !*** ./app/[name]/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileLayout)\n/* harmony export */ });\nfunction ProfileLayout({ children }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW25hbWVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLGNBQWMsRUFDcENDLFFBQVEsRUFHVDtJQUNDLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcW25hbWVdXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2ZpbGVMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIGNoaWxkcmVuO1xufVxuIl0sIm5hbWVzIjpbIlByb2ZpbGVMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/[name]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[name]/page.tsx":
/*!*****************************!*\
  !*** ./app/[name]/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ClientPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ClientPage */ \"(rsc)/./app/[name]/ClientPage.tsx\");\n\n\nasync function Page({ params }) {\n    // Await the params if they are provided as a Promise\n    const resolvedParams = params ? await params : {\n        name: ''\n    };\n    // Ensure name is never undefined\n    const name = resolvedParams.name || '';\n    // Page component received name parameter\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientPage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        name: name\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW25hbWVdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNDO0FBRXZCLGVBQWVDLEtBQUssRUFDakNDLE1BQU0sRUFHUDtJQUNDLHFEQUFxRDtJQUNyRCxNQUFNQyxpQkFBaUJELFNBQVMsTUFBTUEsU0FBUztRQUFFRSxNQUFNO0lBQUc7SUFFMUQsaUNBQWlDO0lBQ2pDLE1BQU1BLE9BQU9ELGVBQWVDLElBQUksSUFBSTtJQUNwQyx5Q0FBeUM7SUFFekMscUJBQU8sOERBQUNKLG1EQUFVQTtRQUFDSSxNQUFNQTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxhcHBcXFtuYW1lXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENsaWVudFBhZ2UgZnJvbSAnLi9DbGllbnRQYWdlJztcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gUGFnZSh7XG4gIHBhcmFtcyxcbn06IHtcbiAgcGFyYW1zPzogUHJvbWlzZTx7IG5hbWU6IHN0cmluZyB9PlxufSkge1xuICAvLyBBd2FpdCB0aGUgcGFyYW1zIGlmIHRoZXkgYXJlIHByb3ZpZGVkIGFzIGEgUHJvbWlzZVxuICBjb25zdCByZXNvbHZlZFBhcmFtcyA9IHBhcmFtcyA/IGF3YWl0IHBhcmFtcyA6IHsgbmFtZTogJycgfTtcblxuICAvLyBFbnN1cmUgbmFtZSBpcyBuZXZlciB1bmRlZmluZWRcbiAgY29uc3QgbmFtZSA9IHJlc29sdmVkUGFyYW1zLm5hbWUgfHwgJyc7XG4gIC8vIFBhZ2UgY29tcG9uZW50IHJlY2VpdmVkIG5hbWUgcGFyYW1ldGVyXG5cbiAgcmV0dXJuIDxDbGllbnRQYWdlIG5hbWU9e25hbWV9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkNsaWVudFBhZ2UiLCJQYWdlIiwicGFyYW1zIiwicmVzb2x2ZWRQYXJhbXMiLCJuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[name]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8cebb05c8e82\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4Y2ViYjA1YzhlODJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_SimpleNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SimpleNavbar */ \"(rsc)/./components/SimpleNavbar.tsx\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./components/Providers.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Web3Socials Profiles - View Only\",\n    description: \"View Web3 profiles\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} antialiased overflow-x-hidden`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    height: '48px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\components\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/SimpleNavbar.tsx":
/*!*************************************!*\
  !*** ./components/SimpleNavbar.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\components\\SimpleNavbar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/layout.tsx */ \"(rsc)/./app/[name]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/page.tsx */ \"(rsc)/./app/[name]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[name]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[name]/page\",\n        pathname: \"/[name]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/ClientPage.tsx */ \"(rsc)/./app/[name]/ClientPage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1QyU1Qm5hbWUlNUQlNUMlNUNDbGllbnRQYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcYXBwXFxcXFtuYW1lXVxcXFxDbGllbnRQYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CSimpleNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CSimpleNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(rsc)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SimpleNavbar.tsx */ \"(rsc)/./components/SimpleNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CSimpleNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/profileStatus */ \"(ssr)/./lib/profileStatus.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ClientPage({ name }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statusChecked, setStatusChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = (0,_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata)();\n    const [bannerMetadata, setBannerMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profilePictureMetadata, setProfilePictureMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // First check profile status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            async function checkStatus() {\n                try {\n                    console.log('[ClientPage] Checking status for:', name);\n                    const statusResult = await (0,_lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__.checkProfileStatus)(name);\n                    console.log('[ClientPage] Status result:', statusResult);\n                    // Check if this is the user's own profile\n                    const isOwnProfile = address && statusResult.address && address.toLowerCase() === statusResult.address.toLowerCase();\n                    console.log('[ClientPage] Is own profile:', isOwnProfile);\n                    // If profile is not approved, redirect to error page\n                    // We no longer make an exception for the profile owner\n                    if (!statusResult.isApproved && statusResult.status !== 'not-found') {\n                        console.log('[ClientPage] Redirecting to error page for status:', statusResult.status);\n                        // Use the name parameter instead of address\n                        const nameParam = statusResult.name ? `&name=${statusResult.name}` : `&name=${name}`;\n                        window.location.href = `/profile-error?status=${statusResult.status}${nameParam}`;\n                        return;\n                    }\n                    setStatusChecked(true);\n                } catch (error) {\n                    console.error('[ClientPage] Error checking profile status:', error);\n                    setStatusChecked(true); // Continue anyway if status check fails\n                }\n            }\n            checkStatus();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        address\n    ]);\n    // Then fetch profile data once status is checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            if (!statusChecked) return; // Wait until status check is complete\n            async function fetchProfileData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('Fetching profile for name:', name);\n                    console.log('Current pathname:', pathname);\n                    // Fetch profile data by name\n                    // Always treat the parameter as a name, not an address\n                    const response = await fetch(`/api/profile/${name}`);\n                    if (!response.ok) {\n                        // Handle errors with more specific messages\n                        const errorResponse = await response.json();\n                        if (response.status === 404) {\n                            // For 404 errors, show a user-friendly message\n                            if (errorResponse.error === 'No profiles exist in the database yet') {\n                                setError('No profiles have been created yet. Be the first to create one!');\n                            } else if (errorResponse.error === 'Profile components not properly initialized') {\n                                setError('Profile system is not properly initialized. Please contact support.');\n                            } else {\n                                setError(`Profile \"${name}\" not found`);\n                            }\n                            setLoading(false);\n                            return; // Exit early without throwing an error\n                        }\n                        // For other errors, log and throw\n                        console.error('Failed to fetch profile:', errorResponse);\n                        throw new Error(errorResponse.error || 'Failed to load profile');\n                    }\n                    const data = await response.json();\n                    setProfileData(data);\n                    // Fetch bannerpfp metadata\n                    if (data.address) {\n                        try {\n                            // Fetch bannerpfp metadata first\n                            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);\n                            if (bannerPfpMeta) {\n                                setBannerPfpMetadata(bannerPfpMeta);\n                                // Now fetch banner and profile picture metadata which will use the bannerpfp data\n                                const bannerMeta = await fetchBannerMetadata(data.address);\n                                if (bannerMeta) {\n                                    setBannerMetadata(bannerMeta);\n                                }\n                                const profilePicMeta = await fetchProfilePictureMetadata(data.address);\n                                if (profilePicMeta) {\n                                    setProfilePictureMetadata(profilePicMeta);\n                                }\n                            }\n                        } catch (metadataError) {\n                            console.error('Error fetching component metadata:', metadataError);\n                        // Continue showing the profile even if metadata fetch fails\n                        }\n                    }\n                } catch (err) {\n                    console.error('Error loading profile:', err);\n                    setError(err.message || 'An error occurred while loading the profile');\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchProfileData();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        fetchBannerMetadata,\n        fetchProfilePictureMetadata,\n        statusChecked\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen flex flex-col items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-4xl mx-auto px-4 sm:px-6 pt-24 pb-8 z-10 relative\",\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center min-h-[300px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 156,\n                columnNumber: 11\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-red-400 font-medium mb-2\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 11\n            }, this) : profileData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-0 border border-neutral-700 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: [\n                                \"Profile: \",\n                                profileData.name || name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-400 mb-4\",\n                            children: [\n                                \"Address: \",\n                                profileData.address\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-400 mb-4\",\n                            children: [\n                                \"Chain: \",\n                                profileData.chain\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-2\",\n                                    children: \"Components:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 17\n                                }, this),\n                                profileData.components.filter((c)=>c.hidden !== 'Y').sort((a, b)=>parseInt(a.order) - parseInt(b.order)).map((component, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-neutral-800 p-4 mb-2 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white\",\n                                                children: [\n                                                    \"Type: \",\n                                                    component.componentType\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neutral-400\",\n                                                children: [\n                                                    \"Order: \",\n                                                    component.order\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 23\n                                            }, this),\n                                            component.profileName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neutral-400\",\n                                                children: [\n                                                    \"Profile Name: \",\n                                                    component.profileName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 21\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 171,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-yellow-400 font-medium mb-2\",\n                        children: \"Profile Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: [\n                            'The profile \"',\n                            name,\n                            \"\\\" doesn't exist or has been removed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 200,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[name]/ClientPage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/GridBgContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/GridBgContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GridBgProvider: () => (/* binding */ GridBgProvider),\n/* harmony export */   useGridBg: () => (/* binding */ useGridBg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GridBgProvider,useGridBg auto */ \n\nconst GridBgContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GridBgProvider({ children }) {\n    const [isGridBgDisabled, setIsGridBgDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridBgContext.Provider, {\n        value: {\n            isGridBgDisabled,\n            setIsGridBgDisabled\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\GridBgContext.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction useGridBg() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GridBgContext);\n    if (context === undefined) {\n        throw new Error('useGridBg must be used within a GridBgProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/GridBgContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/MetadataContext.tsx":
/*!******************************************!*\
  !*** ./app/contexts/MetadataContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetadataProvider: () => (/* binding */ MetadataProvider),\n/* harmony export */   useMetadata: () => (/* binding */ useMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MetadataProvider,useMetadata auto */ \n\nconst MetadataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MetadataProvider({ children }) {\n    const [bannerMetadata, setBannerMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profilePictureMetadata, setProfilePictureMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAddress, setCurrentAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to fetch banner metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchBannerMetadata = async (address)=>{\n        console.log('Banner component is deprecated, using bannerpfp instead');\n        // Try to get banner data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.bannerBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.bannerBlobUrl,\n                scale: bannerpfpData.bannerScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined\n            };\n            setBannerMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Function to fetch profile picture metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchProfilePictureMetadata = async (address, compPosition)=>{\n        console.log('ProfilePicture component is deprecated, using bannerpfp instead');\n        // Try to get profile picture data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.profileBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.profileBlobUrl,\n                scale: bannerpfpData.profileScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined,\n                shape: bannerpfpData.profileShape || 'circular'\n            };\n            setProfilePictureMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Track in-progress fetches to prevent duplicate calls\n    const fetchingAddresses = new Set();\n    // Function to fetch bannerpfp metadata\n    const fetchBannerPfpMetadata = async (address)=>{\n        // If we already have metadata for this address, return it\n        if (bannerPfpMetadata && currentAddress === address) {\n            console.log('MetadataContext: Using cached bannerpfp metadata for', address);\n            return bannerPfpMetadata;\n        }\n        // If we're already fetching this address, wait for it to complete\n        if (fetchingAddresses.has(address)) {\n            console.log('MetadataContext: Already fetching bannerpfp metadata for', address);\n            // Wait a bit and return the current metadata (which should be updated by the in-progress fetch)\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            return bannerPfpMetadata;\n        }\n        // Mark this address as being fetched\n        fetchingAddresses.add(address);\n        try {\n            console.log('MetadataContext: Fetching bannerpfp metadata for', address);\n            const response = await fetch(`/api/bannerpfp/${address}`);\n            if (!response.ok) {\n                // If the response is not OK, log the error but don't throw\n                // This allows the UI to continue rendering even if metadata is missing\n                console.warn(`Failed to fetch bannerpfp metadata: ${response.status} ${response.statusText}`);\n                // For 404 errors, we'll create a default metadata object\n                if (response.status === 404) {\n                    console.log('Creating default bannerpfp metadata since none exists');\n                    const defaultMetadata = {\n                        profileName: address.substring(0, 8),\n                        profileShape: 'circular',\n                        profileHorizontalPosition: 50,\n                        profileNameHorizontalPosition: 50,\n                        profileNameStyle: 'typewriter'\n                    };\n                    setBannerPfpMetadata(defaultMetadata);\n                    setCurrentAddress(address);\n                    return defaultMetadata;\n                }\n                return null;\n            }\n            const metadata = await response.json();\n            console.log('MetadataContext: Received bannerpfp metadata:', metadata);\n            if (metadata) {\n                console.log('MetadataContext: Setting bannerpfp metadata:', metadata);\n                setBannerPfpMetadata(metadata);\n                setCurrentAddress(address);\n                return metadata;\n            }\n        } catch (error) {\n            console.error('Failed to load bannerpfp metadata:', error);\n            // Create a default metadata object on error\n            const defaultMetadata = {\n                profileName: address.substring(0, 8),\n                profileShape: 'circular',\n                profileHorizontalPosition: 50,\n                profileNameHorizontalPosition: 50,\n                profileNameStyle: 'typewriter'\n            };\n            setBannerPfpMetadata(defaultMetadata);\n            setCurrentAddress(address);\n            return defaultMetadata;\n        } finally{\n            // Remove this address from the fetching set\n            fetchingAddresses.delete(address);\n        }\n        return null;\n    };\n    // Function to clear metadata (useful when changing addresses)\n    const clearMetadata = ()=>{\n        setBannerMetadata(null);\n        setProfilePictureMetadata(null);\n        setBannerPfpMetadata(null);\n        setCurrentAddress(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetadataContext.Provider, {\n        value: {\n            bannerMetadata,\n            profilePictureMetadata,\n            bannerPfpMetadata,\n            fetchBannerMetadata,\n            fetchProfilePictureMetadata,\n            fetchBannerPfpMetadata,\n            clearMetadata\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\MetadataContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\nfunction useMetadata() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MetadataContext);\n    if (context === undefined) {\n        throw new Error('useMetadata must be used within a MetadataProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/MetadataContext.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(ssr)/./config/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(ssr)/./components/ThemeProvider.tsx\");\n/* harmony import */ var _app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/contexts/GridBgContext */ \"(ssr)/./app/contexts/GridBgContext.tsx\");\n/* harmony import */ var _hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useWalletConnectionPersistence */ \"(ssr)/./hooks/useWalletConnectionPersistence.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Create a client outside the component\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1\n        }\n    }\n});\n// Inner component to use hooks after WagmiProvider is mounted\nfunction ProvidersContent({ children }) {\n    // Use the wallet connection persistence hook\n    (0,_hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__.useWalletConnectionPersistence)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__.MetadataProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__.GridBgProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Providers.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Providers.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_8__.WagmiProvider, {\n        config: _config__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: false,\n                forcedTheme: \"dark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProvidersContent, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/SimpleNavbar.tsx":
/*!*************************************!*\
  !*** ./components/SimpleNavbar.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SimpleNavbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-neutral-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-semibold text-lg\",\n                            children: \"Web3Socials Profiles\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-neutral-400 text-sm\",\n                        children: \"View Only\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\SimpleNavbar.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1NpbXBsZU5hdmJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTBCO0FBRVgsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNDO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNFOzRCQUFLRixXQUFVO3NDQUFtQzs7Ozs7Ozs7Ozs7a0NBSXJELDhEQUFDQzt3QkFBSUQsV0FBVTtrQ0FBMkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPcEQiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGNvbXBvbmVudHNcXFNpbXBsZU5hdmJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaW1wbGVOYXZiYXIoKSB7XG4gIHJldHVybiAoXG4gICAgPG5hdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCBsZWZ0LTAgcmlnaHQtMCB6LTUwIGJnLWJsYWNrLzgwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtODAwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGgtMTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICBXZWIzU29jaWFscyBQcm9maWxlc1xuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICBWaWV3IE9ubHlcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L25hdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNpbXBsZU5hdmJhciIsIm5hdiIsImNsYXNzTmFtZSIsImRpdiIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/SimpleNavbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ThemeProvider.tsx":
/*!**************************************!*\
  !*** ./components/ThemeProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxjb21wb25lbnRzXFxUaGVtZVByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gJ25leHQtdGhlbWVzJ1xuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./config/index.tsx":
/*!**************************!*\
  !*** ./config/index.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   networks: () => (/* binding */ networks),\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit-adapter-solana */ \"(ssr)/./node_modules/@reown/appkit-adapter-solana/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n/* __next_internal_client_entry_do_not_use__ projectId,metadata,networks,wagmiAdapter,config auto */ \n\n\n\nconst projectId = \"63cb69e4a11f991fe106897d3eede1ed\";\nif (!projectId) {\n    throw new Error(\"Project ID is not defined\");\n}\n// Validate project ID format\nif (projectId.length !== 32) {\n    console.warn('Project ID may be invalid - should be 32 characters');\n}\nconst metadata = {\n    name: 'Web3Socials',\n    description: 'Web3 Social Platform',\n    url:  false ? 0 : 'https://web3socials.fun',\n    icons: [\n        'https://avatars.githubusercontent.com/u/179229932'\n    ]\n};\n// Temporarily reduce networks to test 403 error - you can add more back later\nconst networks = [\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronos,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.mainnet,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.arbitrum,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.sepolia,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.solana,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronoszkEVM\n];\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    ssr: true,\n    projectId,\n    networks\n});\nconst solanaWeb3JsAdapter = new _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__.SolanaAdapter();\nconst generalConfig = {\n    projectId,\n    networks,\n    metadata,\n    themeMode: 'dark',\n    themeVariables: {\n        '--w3m-accent': '#000000'\n    }\n};\n// Prevent ethereum object conflicts\nif (false) {}\n(0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter,\n        solanaWeb3JsAdapter\n    ],\n    ...generalConfig,\n    features: {\n        swaps: false,\n        onramp: false,\n        email: true,\n        socials: false,\n        history: false,\n        analytics: false,\n        allWallets: true,\n        send: false\n    },\n    // Disable features that might cause network requests\n    featuredWalletIds: []\n});\nconst config = wagmiAdapter.wagmiConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb25maWcvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztxR0FDa0Q7QUFDUztBQUVDO0FBUzVCO0FBR3pCLE1BQU1TLFlBQVlDLGtDQUFrQyxDQUFDO0FBRTVELElBQUksQ0FBQ0QsV0FBVztJQUNkLE1BQU0sSUFBSUksTUFBTTtBQUNsQjtBQUVBLDZCQUE2QjtBQUM3QixJQUFJSixVQUFVSyxNQUFNLEtBQUssSUFBSTtJQUMzQkMsUUFBUUMsSUFBSSxDQUFDO0FBQ2Y7QUFDTyxNQUFNQyxXQUFXO0lBQ3BCQyxNQUFNO0lBQ05DLGFBQWE7SUFDYkMsS0FBSyxNQUE2QixHQUFHQyxDQUFzQixHQUFHO0lBQzlERyxPQUFPO1FBQUM7S0FBb0Q7QUFDOUQsRUFBQztBQUVILDhFQUE4RTtBQUN2RSxNQUFNQyxXQUFXO0lBQUNqQiwwREFBTUE7SUFBQ0wsMkRBQU9BO0lBQ3JDQyw0REFBUUE7SUFDUkMsMkRBQU9BO0lBQ1BDLDBEQUFNQTtJQUNOQywrREFBV0E7Q0FBQyxDQUF3QztBQUUvQyxNQUFNbUIsZUFBZSxJQUFJekIscUVBQVlBLENBQUM7SUFDM0MwQixLQUFLO0lBQ0xsQjtJQUNBZ0I7QUFDRixHQUFHO0FBRUgsTUFBTUcsc0JBQXNCLElBQUkxQix1RUFBYUE7QUFFN0MsTUFBTTJCLGdCQUFnQjtJQUNwQnBCO0lBQ0FnQjtJQUNBUjtJQUNBYSxXQUFXO0lBQ1hDLGdCQUFnQjtRQUNkLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUEsb0NBQW9DO0FBQ3BDLElBQUksS0FBNkIsRUFBRSxFQU9sQztBQUVEL0IsaUVBQVlBLENBQUM7SUFDWGtDLFVBQVU7UUFBQ1I7UUFBY0U7S0FBb0I7SUFDN0MsR0FBR0MsYUFBYTtJQUNoQk0sVUFBVTtRQUNSQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLE1BQU07SUFDTjtJQUNGLHFEQUFxRDtJQUNyREMsbUJBQW1CLEVBQUU7QUFDdkI7QUFFTyxNQUFNQyxTQUFTbkIsYUFBYW9CLFdBQVciLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGNvbmZpZ1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGNyZWF0ZUFwcEtpdCB9IGZyb20gJ0ByZW93bi9hcHBraXQvcmVhY3QnXG5pbXBvcnQgeyBXYWdtaUFkYXB0ZXIgfSBmcm9tIFwiQHJlb3duL2FwcGtpdC1hZGFwdGVyLXdhZ21pXCI7XG5pbXBvcnQgdHlwZSB7IEFwcEtpdE5ldHdvcmsgfSBmcm9tICdAcmVvd24vYXBwa2l0L25ldHdvcmtzJ1xuaW1wb3J0IHsgU29sYW5hQWRhcHRlciB9IGZyb20gJ0ByZW93bi9hcHBraXQtYWRhcHRlci1zb2xhbmEnXG5cbmltcG9ydCB7XG4gIG1haW5uZXQsXG4gIGFyYml0cnVtLFxuICBzZXBvbGlhLFxuICBzb2xhbmEsXG4gIGNyb25vc3prRVZNLFxuICBjcm9ub3Ncbn0gZnJvbSBcIkByZW93bi9hcHBraXQvbmV0d29ya3NcIjtcblxuXG5leHBvcnQgY29uc3QgcHJvamVjdElkID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUFJPSkVDVF9JRDtcblxuaWYgKCFwcm9qZWN0SWQpIHtcbiAgdGhyb3cgbmV3IEVycm9yKFwiUHJvamVjdCBJRCBpcyBub3QgZGVmaW5lZFwiKTtcbn1cblxuLy8gVmFsaWRhdGUgcHJvamVjdCBJRCBmb3JtYXRcbmlmIChwcm9qZWN0SWQubGVuZ3RoICE9PSAzMikge1xuICBjb25zb2xlLndhcm4oJ1Byb2plY3QgSUQgbWF5IGJlIGludmFsaWQgLSBzaG91bGQgYmUgMzIgY2hhcmFjdGVycycpO1xufVxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICAgIG5hbWU6ICdXZWIzU29jaWFscycsXG4gICAgZGVzY3JpcHRpb246ICdXZWIzIFNvY2lhbCBQbGF0Zm9ybScsXG4gICAgdXJsOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gOiAnaHR0cHM6Ly93ZWIzc29jaWFscy5mdW4nLFxuICAgIGljb25zOiBbJ2h0dHBzOi8vYXZhdGFycy5naXRodWJ1c2VyY29udGVudC5jb20vdS8xNzkyMjk5MzInXVxuICB9XG5cbi8vIFRlbXBvcmFyaWx5IHJlZHVjZSBuZXR3b3JrcyB0byB0ZXN0IDQwMyBlcnJvciAtIHlvdSBjYW4gYWRkIG1vcmUgYmFjayBsYXRlclxuZXhwb3J0IGNvbnN0IG5ldHdvcmtzID0gW2Nyb25vcyxtYWlubmV0LFxuICBhcmJpdHJ1bSxcbiAgc2Vwb2xpYSxcbiAgc29sYW5hLFxuICBjcm9ub3N6a0VWTV0gYXMgW0FwcEtpdE5ldHdvcmssIC4uLkFwcEtpdE5ldHdvcmtbXV07XG5cbmV4cG9ydCBjb25zdCB3YWdtaUFkYXB0ZXIgPSBuZXcgV2FnbWlBZGFwdGVyKHtcbiAgc3NyOiB0cnVlLFxuICBwcm9qZWN0SWQsXG4gIG5ldHdvcmtzXG59KTtcblxuY29uc3Qgc29sYW5hV2ViM0pzQWRhcHRlciA9IG5ldyBTb2xhbmFBZGFwdGVyKClcblxuY29uc3QgZ2VuZXJhbENvbmZpZyA9IHtcbiAgcHJvamVjdElkLFxuICBuZXR3b3JrcyxcbiAgbWV0YWRhdGEsXG4gIHRoZW1lTW9kZTogJ2RhcmsnIGFzIGNvbnN0LFxuICB0aGVtZVZhcmlhYmxlczoge1xuICAgICctLXczbS1hY2NlbnQnOiAnIzAwMDAwMCcsXG4gIH1cbn1cblxuLy8gUHJldmVudCBldGhlcmV1bSBvYmplY3QgY29uZmxpY3RzXG5pZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgLy8gQ2xlYXIgYW55IGV4aXN0aW5nIGV0aGVyZXVtIHByb3BlcnR5IGNvbmZsaWN0c1xuICB0cnkge1xuICAgIGRlbGV0ZSAod2luZG93IGFzIGFueSkuZXRoZXJldW07XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICAvLyBJZ25vcmUgaWYgcHJvcGVydHkgY2FuJ3QgYmUgZGVsZXRlZFxuICB9XG59XG5cbmNyZWF0ZUFwcEtpdCh7XG4gIGFkYXB0ZXJzOiBbd2FnbWlBZGFwdGVyLCBzb2xhbmFXZWIzSnNBZGFwdGVyXSxcbiAgLi4uZ2VuZXJhbENvbmZpZyxcbiAgZmVhdHVyZXM6IHtcbiAgICBzd2FwczogZmFsc2UsXG4gICAgb25yYW1wOiBmYWxzZSxcbiAgICBlbWFpbDogdHJ1ZSxcbiAgICBzb2NpYWxzOiBmYWxzZSxcbiAgICBoaXN0b3J5OiBmYWxzZSxcbiAgICBhbmFseXRpY3M6IGZhbHNlLFxuICAgIGFsbFdhbGxldHM6IHRydWUsXG4gICAgc2VuZDogZmFsc2UsXG4gICAgfSxcbiAgLy8gRGlzYWJsZSBmZWF0dXJlcyB0aGF0IG1pZ2h0IGNhdXNlIG5ldHdvcmsgcmVxdWVzdHNcbiAgZmVhdHVyZWRXYWxsZXRJZHM6IFtdXG59KVxuXG5leHBvcnQgY29uc3QgY29uZmlnID0gd2FnbWlBZGFwdGVyLndhZ21pQ29uZmlnXG4iXSwibmFtZXMiOlsiY3JlYXRlQXBwS2l0IiwiV2FnbWlBZGFwdGVyIiwiU29sYW5hQWRhcHRlciIsIm1haW5uZXQiLCJhcmJpdHJ1bSIsInNlcG9saWEiLCJzb2xhbmEiLCJjcm9ub3N6a0VWTSIsImNyb25vcyIsInByb2plY3RJZCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19QUk9KRUNUX0lEIiwiRXJyb3IiLCJsZW5ndGgiLCJjb25zb2xlIiwid2FybiIsIm1ldGFkYXRhIiwibmFtZSIsImRlc2NyaXB0aW9uIiwidXJsIiwid2luZG93IiwibG9jYXRpb24iLCJvcmlnaW4iLCJpY29ucyIsIm5ldHdvcmtzIiwid2FnbWlBZGFwdGVyIiwic3NyIiwic29sYW5hV2ViM0pzQWRhcHRlciIsImdlbmVyYWxDb25maWciLCJ0aGVtZU1vZGUiLCJ0aGVtZVZhcmlhYmxlcyIsImV0aGVyZXVtIiwiZSIsImFkYXB0ZXJzIiwiZmVhdHVyZXMiLCJzd2FwcyIsIm9ucmFtcCIsImVtYWlsIiwic29jaWFscyIsImhpc3RvcnkiLCJhbmFseXRpY3MiLCJhbGxXYWxsZXRzIiwic2VuZCIsImZlYXR1cmVkV2FsbGV0SWRzIiwiY29uZmlnIiwid2FnbWlDb25maWciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./config/index.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useWalletConnectionPersistence.ts":
/*!*************************************************!*\
  !*** ./hooks/useWalletConnectionPersistence.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWalletConnectionPersistence: () => (/* binding */ useWalletConnectionPersistence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useWalletConnectionPersistence auto */ \n\n\n/**\n * Simplified hook to log wallet connection status for debugging\n * AppKit handles connection persistence automatically\n */ function useWalletConnectionPersistence() {\n    const { isConnected, address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Log connection status for debugging only\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWalletConnectionPersistence.useEffect\": ()=>{\n            console.log('[Wallet Connection] Path changed to:', pathname);\n            console.log('[Wallet Connection] isConnected:', isConnected);\n            console.log('[Wallet Connection] address:', address);\n        }\n    }[\"useWalletConnectionPersistence.useEffect\"], [\n        pathname,\n        isConnected,\n        address\n    ]);\n    return {\n        isConnected,\n        address\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ob29rcy91c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7b0ZBRWtDO0FBQ3FCO0FBQ1Q7QUFFOUM7OztDQUdDLEdBQ00sU0FBU0c7SUFDZCxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdKLHFFQUFnQkE7SUFDakQsTUFBTUssV0FBV0osNERBQVdBO0lBRTVCLDJDQUEyQztJQUMzQ0YsZ0RBQVNBO29EQUFDO1lBQ1JPLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NGO1lBQ3BEQyxRQUFRQyxHQUFHLENBQUMsb0NBQW9DSjtZQUNoREcsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0g7UUFDOUM7bURBQUc7UUFBQ0M7UUFBVUY7UUFBYUM7S0FBUTtJQUVuQyxPQUFPO1FBQUVEO1FBQWFDO0lBQVE7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGhvb2tzXFx1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBcHBLaXRBY2NvdW50IH0gZnJvbSAnQHJlb3duL2FwcGtpdC9yZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbi8qKlxuICogU2ltcGxpZmllZCBob29rIHRvIGxvZyB3YWxsZXQgY29ubmVjdGlvbiBzdGF0dXMgZm9yIGRlYnVnZ2luZ1xuICogQXBwS2l0IGhhbmRsZXMgY29ubmVjdGlvbiBwZXJzaXN0ZW5jZSBhdXRvbWF0aWNhbGx5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UoKSB7XG4gIGNvbnN0IHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfSA9IHVzZUFwcEtpdEFjY291bnQoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIC8vIExvZyBjb25uZWN0aW9uIHN0YXR1cyBmb3IgZGVidWdnaW5nIG9ubHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBQYXRoIGNoYW5nZWQgdG86JywgcGF0aG5hbWUpO1xuICAgIGNvbnNvbGUubG9nKCdbV2FsbGV0IENvbm5lY3Rpb25dIGlzQ29ubmVjdGVkOicsIGlzQ29ubmVjdGVkKTtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBhZGRyZXNzOicsIGFkZHJlc3MpO1xuICB9LCBbcGF0aG5hbWUsIGlzQ29ubmVjdGVkLCBhZGRyZXNzXSk7XG5cbiAgcmV0dXJuIHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBcHBLaXRBY2NvdW50IiwidXNlUGF0aG5hbWUiLCJ1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UiLCJpc0Nvbm5lY3RlZCIsImFkZHJlc3MiLCJwYXRobmFtZSIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useWalletConnectionPersistence.ts\n");

/***/ }),

/***/ "(ssr)/./lib/profileStatus.ts":
/*!******************************!*\
  !*** ./lib/profileStatus.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkProfileStatus: () => (/* binding */ checkProfileStatus),\n/* harmony export */   clearProfileStatusCache: () => (/* binding */ clearProfileStatusCache)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ checkProfileStatus,clearProfileStatusCache auto */ // Cache for profile status to avoid repeated fetches\nlet profileStatusCache = {};\n/**\n * Fetch profile status from the database\n * @param addressOrName Address or name of the profile to check\n * @returns Promise with profile status\n */ async function checkProfileStatus(addressOrName) {\n    // Return from cache if available\n    if (profileStatusCache[addressOrName]) {\n        const cached = profileStatusCache[addressOrName];\n        return {\n            ...cached,\n            message: getStatusMessage(cached.status)\n        };\n    }\n    try {\n        // Fetch profile status from API\n        const response = await fetch(`/api/profile/check-status?identifier=${addressOrName}`);\n        if (!response.ok) {\n            throw new Error(`Failed to fetch profile status: ${response.statusText}`);\n        }\n        const data = await response.json();\n        // Cache the result\n        profileStatusCache[addressOrName] = {\n            status: data.status,\n            isApproved: data.status === 'approved',\n            address: data.address,\n            name: data.name,\n            transactionHash: data.transactionHash,\n            expiryDate: data.expiryDate\n        };\n        return {\n            status: data.status,\n            isApproved: data.status === 'approved',\n            message: getStatusMessage(data.status),\n            address: data.address,\n            name: data.name,\n            transactionHash: data.transactionHash,\n            expiryDate: data.expiryDate\n        };\n    } catch (error) {\n        console.error(`Error fetching profile status for ${addressOrName}:`, error);\n        // Return default values on error\n        return {\n            status: 'error',\n            isApproved: false,\n            message: 'Error checking profile status. Please try again later.'\n        };\n    }\n}\n/**\n * Get a user-friendly message based on profile status\n */ function getStatusMessage(status) {\n    switch(status){\n        case 'approved':\n            return 'Profile is approved and accessible.';\n        case 'new':\n            return 'This profile is new and waiting for approval. Please burn tokens and provide a transaction hash for verification.';\n        case 'in-progress':\n            return 'This profile is being processed. Please check back later.';\n        case 'pending':\n            return 'This profile is pending approval. Please check back later.';\n        case 'deleted':\n            return 'This profile has been deleted.';\n        case 'expired':\n            return 'This profile has expired. Please contact an admin to renew it.';\n        default:\n            return 'Profile status is unknown.';\n    }\n}\n/**\n * Clear the profile status cache\n */ function clearProfileStatusCache() {\n    profileStatusCache = {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/profileStatus.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/ClientPage.tsx */ \"(ssr)/./app/[name]/ClientPage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1QyU1Qm5hbWUlNUQlNUMlNUNDbGllbnRQYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcYXBwXFxcXFtuYW1lXVxcXFxDbGllbnRQYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CSimpleNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CSimpleNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(ssr)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SimpleNavbar.tsx */ \"(ssr)/./components/SimpleNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CSimpleNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pino-pretty":
/*!******************************!*\
  !*** external "pino-pretty" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("pino-pretty");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/lit-html","vendor-chunks/@lit","vendor-chunks/lit","vendor-chunks/viem","vendor-chunks/next","vendor-chunks/@walletconnect","vendor-chunks/@noble","vendor-chunks/@wagmi","vendor-chunks/ox","vendor-chunks/abitype","vendor-chunks/@solana","vendor-chunks/tr46","vendor-chunks/rpc-websockets","vendor-chunks/ws","vendor-chunks/bn.js","vendor-chunks/sonner","vendor-chunks/@tanstack","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/valtio","vendor-chunks/superstruct","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/big.js","vendor-chunks/borsh","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/text-encoding-utf-8","vendor-chunks/zustand","vendor-chunks/uuid","vendor-chunks/dayjs","vendor-chunks/eventemitter3","vendor-chunks/lit-element","vendor-chunks/detect-browser","vendor-chunks/jayson","vendor-chunks/@wallet-standard","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/quick-format-unescaped","vendor-chunks/proxy-compare","vendor-chunks/mipd","vendor-chunks/@swc","vendor-chunks/destr","vendor-chunks/derive-valtio","vendor-chunks/utf-8-validate","vendor-chunks/safe-buffer","vendor-chunks/wagmi","vendor-chunks/atomic-sleep","vendor-chunks/bufferutil","vendor-chunks/bs58","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();