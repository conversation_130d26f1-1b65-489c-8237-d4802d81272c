/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[name]/page";
exports.ids = ["app/[name]/page"];
exports.modules = {

/***/ "(rsc)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\app\\[name]\\ClientPage.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/[name]/layout.tsx":
/*!*******************************!*\
  !*** ./app/[name]/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileLayout)\n/* harmony export */ });\nfunction ProfileLayout({ children }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW25hbWVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLGNBQWMsRUFDcENDLFFBQVEsRUFHVDtJQUNDLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcW25hbWVdXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2ZpbGVMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIGNoaWxkcmVuO1xufVxuIl0sIm5hbWVzIjpbIlByb2ZpbGVMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/[name]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[name]/page.tsx":
/*!*****************************!*\
  !*** ./app/[name]/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ClientPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ClientPage */ \"(rsc)/./app/[name]/ClientPage.tsx\");\n\n\nasync function Page({ params }) {\n    // Await the params if they are provided as a Promise\n    const resolvedParams = params ? await params : {\n        name: ''\n    };\n    // Ensure name is never undefined\n    const name = resolvedParams.name || '';\n    // Page component received name parameter\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientPage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        name: name\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW25hbWVdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNDO0FBRXZCLGVBQWVDLEtBQUssRUFDakNDLE1BQU0sRUFHUDtJQUNDLHFEQUFxRDtJQUNyRCxNQUFNQyxpQkFBaUJELFNBQVMsTUFBTUEsU0FBUztRQUFFRSxNQUFNO0lBQUc7SUFFMUQsaUNBQWlDO0lBQ2pDLE1BQU1BLE9BQU9ELGVBQWVDLElBQUksSUFBSTtJQUNwQyx5Q0FBeUM7SUFFekMscUJBQU8sOERBQUNKLG1EQUFVQTtRQUFDSSxNQUFNQTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxhcHBcXFtuYW1lXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENsaWVudFBhZ2UgZnJvbSAnLi9DbGllbnRQYWdlJztcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gUGFnZSh7XG4gIHBhcmFtcyxcbn06IHtcbiAgcGFyYW1zPzogUHJvbWlzZTx7IG5hbWU6IHN0cmluZyB9PlxufSkge1xuICAvLyBBd2FpdCB0aGUgcGFyYW1zIGlmIHRoZXkgYXJlIHByb3ZpZGVkIGFzIGEgUHJvbWlzZVxuICBjb25zdCByZXNvbHZlZFBhcmFtcyA9IHBhcmFtcyA/IGF3YWl0IHBhcmFtcyA6IHsgbmFtZTogJycgfTtcblxuICAvLyBFbnN1cmUgbmFtZSBpcyBuZXZlciB1bmRlZmluZWRcbiAgY29uc3QgbmFtZSA9IHJlc29sdmVkUGFyYW1zLm5hbWUgfHwgJyc7XG4gIC8vIFBhZ2UgY29tcG9uZW50IHJlY2VpdmVkIG5hbWUgcGFyYW1ldGVyXG5cbiAgcmV0dXJuIDxDbGllbnRQYWdlIG5hbWU9e25hbWV9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkNsaWVudFBhZ2UiLCJQYWdlIiwicGFyYW1zIiwicmVzb2x2ZWRQYXJhbXMiLCJuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[name]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8cebb05c8e82\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4Y2ViYjA1YzhlODJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./components/Providers.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Web3Socials Profiles - View Only\",\n    description: \"View Web3 profiles\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased overflow-x-hidden`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\WebPages\\Web3Socials\\profiles-view\\components\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/layout.tsx */ \"(rsc)/./app/[name]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/page.tsx */ \"(rsc)/./app/[name]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[name]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[name]/page\",\n        pathname: \"/[name]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/ClientPage.tsx */ \"(rsc)/./app/[name]/ClientPage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1QyU1Qm5hbWUlNUQlNUMlNUNDbGllbnRQYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcYXBwXFxcXFtuYW1lXVxcXFxDbGllbnRQYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(rsc)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[name]/ClientPage.tsx":
/*!***********************************!*\
  !*** ./app/[name]/ClientPage.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/profileStatus */ \"(ssr)/./lib/profileStatus.ts\");\n/* harmony import */ var _app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/components/renders/render_hero */ \"(ssr)/./app/components/renders/render_hero.tsx\");\n/* harmony import */ var _app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/renders/render_sociallinks */ \"(ssr)/./app/components/renders/render_sociallinks.tsx\");\n/* harmony import */ var _app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/renders/render_bannerpfp */ \"(ssr)/./app/components/renders/render_bannerpfp.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ClientPage({ name }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_3__.useAppKitAccount)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [statusChecked, setStatusChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = (0,_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_4__.useMetadata)();\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // First check profile status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            async function checkStatus() {\n                try {\n                    console.log('[ClientPage] Checking status for:', name);\n                    const statusResult = await (0,_lib_profileStatus__WEBPACK_IMPORTED_MODULE_5__.checkProfileStatus)(name);\n                    console.log('[ClientPage] Status result:', statusResult);\n                    // Check if this is the user's own profile\n                    const isOwnProfile = address && statusResult.address && address.toLowerCase() === statusResult.address.toLowerCase();\n                    console.log('[ClientPage] Is own profile:', isOwnProfile);\n                    // If profile is not approved, redirect to error page\n                    // We no longer make an exception for the profile owner\n                    if (!statusResult.isApproved && statusResult.status !== 'not-found') {\n                        console.log('[ClientPage] Redirecting to error page for status:', statusResult.status);\n                        // Use the name parameter instead of address\n                        const nameParam = statusResult.name ? `&name=${statusResult.name}` : `&name=${name}`;\n                        window.location.href = `/profile-error?status=${statusResult.status}${nameParam}`;\n                        return;\n                    }\n                    setStatusChecked(true);\n                } catch (error) {\n                    console.error('[ClientPage] Error checking profile status:', error);\n                    setStatusChecked(true); // Continue anyway if status check fails\n                }\n            }\n            checkStatus();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        address\n    ]);\n    // Then fetch profile data once status is checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientPage.useEffect\": ()=>{\n            if (!statusChecked) return; // Wait until status check is complete\n            async function fetchProfileData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('Fetching profile for name:', name);\n                    console.log('Current pathname:', pathname);\n                    // Fetch profile data by name\n                    // Always treat the parameter as a name, not an address\n                    const response = await fetch(`/api/profile/${name}`);\n                    if (!response.ok) {\n                        // Handle errors with more specific messages\n                        const errorResponse = await response.json();\n                        if (response.status === 404) {\n                            // For 404 errors, show a user-friendly message\n                            if (errorResponse.error === 'No profiles exist in the database yet') {\n                                setError('No profiles have been created yet. Be the first to create one!');\n                            } else if (errorResponse.error === 'Profile components not properly initialized') {\n                                setError('Profile system is not properly initialized. Please contact support.');\n                            } else {\n                                setError(`Profile \"${name}\" not found`);\n                            }\n                            setLoading(false);\n                            return; // Exit early without throwing an error\n                        }\n                        // For other errors, log and throw\n                        console.error('Failed to fetch profile:', errorResponse);\n                        throw new Error(errorResponse.error || 'Failed to load profile');\n                    }\n                    const data = await response.json();\n                    setProfileData(data);\n                    // Fetch bannerpfp metadata\n                    if (data.address) {\n                        try {\n                            // Fetch bannerpfp metadata first\n                            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);\n                            if (bannerPfpMeta) {\n                                setBannerPfpMetadata(bannerPfpMeta);\n                                // Now fetch banner and profile picture metadata which will use the bannerpfp data\n                                const bannerMeta = await fetchBannerMetadata(data.address);\n                                if (bannerMeta) {\n                                    setBannerMetadata(bannerMeta);\n                                }\n                                const profilePicMeta = await fetchProfilePictureMetadata(data.address);\n                                if (profilePicMeta) {\n                                    setProfilePictureMetadata(profilePicMeta);\n                                }\n                            }\n                        } catch (metadataError) {\n                            console.error('Error fetching component metadata:', metadataError);\n                        // Continue showing the profile even if metadata fetch fails\n                        }\n                    }\n                } catch (err) {\n                    console.error('Error loading profile:', err);\n                    setError(err.message || 'An error occurred while loading the profile');\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchProfileData();\n        }\n    }[\"ClientPage.useEffect\"], [\n        name,\n        fetchBannerMetadata,\n        fetchProfilePictureMetadata,\n        statusChecked\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen flex flex-col items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-4xl mx-auto px-4 sm:px-6 pb-8 z-10 relative\",\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center min-h-[300px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 157,\n                columnNumber: 11\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-red-400 font-medium mb-2\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this) : profileData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-0 border border-neutral-700 overflow-hidden\",\n                children: profileData.components.filter((c)=>c.hidden !== 'Y').sort((a, b)=>parseInt(a.order) - parseInt(b.order)).map((component, index)=>{\n                    // Skip old component types\n                    if (component.componentType === 'banner' || component.componentType === 'profilePicture') {\n                        return null;\n                    } else if (component.componentType === 'details') {\n                        // Skip rendering deprecated 'details' component\n                        return null;\n                    } else if (component.componentType === 'hero') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_hero__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 23\n                            }, this)\n                        }, `hero-${component.order}`, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'socialLinks') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_sociallinks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                profileData: {\n                                    address: profileData.address,\n                                    chain: profileData.chain,\n                                    name: profileData.name || '',\n                                    bio: ''\n                                },\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 23\n                            }, this)\n                        }, `socialLinks-${component.order}`, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 21\n                        }, this);\n                    } else if (component.componentType === 'bannerpfp') {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-neutral-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_renders_render_bannerpfp__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                address: profileData.address,\n                                componentData: {\n                                    ...component,\n                                    address: profileData.address,\n                                    chain: profileData.chain\n                                },\n                                showPositionLabel: false,\n                                profileName: profileData.name || '',\n                                profileBio: ''\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 23\n                            }, this)\n                        }, `bannerpfp-${component.order}`, false, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 21\n                        }, this);\n                    }\n                    return null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 172,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-yellow-400 font-medium mb-2\",\n                        children: \"Profile Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-300 mb-4\",\n                        children: [\n                            'The profile \"',\n                            name,\n                            \"\\\" doesn't exist or has been removed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors\",\n                        children: \"Go Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n                lineNumber: 237,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\[name]\\\\ClientPage.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[name]/ClientPage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/renders/render_bannerpfp.tsx":
/*!*****************************************************!*\
  !*** ./app/components/renders/render_bannerpfp.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderBannerPfp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction RenderBannerPfp({ address, componentData, showPositionLabel = false, profileName: propProfileName, profileBio: propProfileBio }) {\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileName, setProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileBio, setProfileBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profileHorizontalPosition, setProfileHorizontalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [profileNameHorizontalPosition, setProfileNameHorizontalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [profileShape, setProfileShape] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('circular');\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const getBorderRadiusClass = ()=>{\n        switch(profileShape){\n            case 'rectangular':\n                return 'rounded-none';\n            case 'squarish':\n                return 'rounded-md';\n            case 'circular':\n            default:\n                return 'rounded-full';\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderBannerPfp.useEffect\": ()=>{\n            const loadBannerPfpData = {\n                \"RenderBannerPfp.useEffect.loadBannerPfpData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const response = await fetch(`/api/bannerpfp/${address}`);\n                        if (!response.ok) {\n                            throw new Error('Failed to load banner/profile data');\n                        }\n                        const data = await response.json();\n                        setBannerPfpMetadata(data);\n                        // Set profile data from API response or props\n                        setProfileName(data.profileName || propProfileName || address.substring(0, 8));\n                        setProfileBio(data.profileBio || propProfileBio || '');\n                        setProfileHorizontalPosition(data.profileHorizontalPosition || 50);\n                        setProfileNameHorizontalPosition(data.profileNameHorizontalPosition || 50);\n                        setProfileShape(data.profileShape || 'circular');\n                    } catch (error) {\n                        console.error('Error loading banner/profile data:', error);\n                        setError('Failed to load banner/profile data');\n                        // Set fallback data\n                        setProfileName(propProfileName || address.substring(0, 8));\n                        setProfileBio(propProfileBio || '');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"RenderBannerPfp.useEffect.loadBannerPfpData\"];\n            if (address) {\n                loadBannerPfpData();\n            }\n        }\n    }[\"RenderBannerPfp.useEffect\"], [\n        address,\n        propProfileName,\n        propProfileBio\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !bannerPfpMetadata) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-neutral-400 p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-hidden w-full min-w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: componentData.backgroundColor,\n                    width: '100%',\n                    minWidth: '100%',\n                    boxSizing: 'border-box',\n                    paddingBottom: '0.5rem'\n                },\n                className: \"w-full min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full\",\n                        style: {\n                            marginBottom: profileShape === 'rectangular' ? '9rem' : '8rem',\n                            width: '100%'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-48 md:h-64 relative overflow-hidden\",\n                                ref: containerRef,\n                                style: {\n                                    width: '100%',\n                                    minWidth: '100%'\n                                },\n                                children: bannerPfpMetadata?.bannerUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: `url(${bannerPfpMetadata.bannerUrl})`,\n                                        backgroundSize: 'cover',\n                                        backgroundPosition: 'center',\n                                        transform: `translate(${bannerPfpMetadata.bannerPosition?.x || 0}px, ${bannerPfpMetadata.bannerPosition?.y || 0}px) scale(${bannerPfpMetadata.bannerScale || 1})`,\n                                        transformOrigin: 'center'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute flex justify-center\",\n                                style: {\n                                    bottom: profileShape === 'rectangular' ? '-4.5rem' : '-4rem',\n                                    left: `${profileHorizontalPosition}%`,\n                                    transform: 'translateX(-50%)',\n                                    zIndex: 10\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative`,\n                                    children: bannerPfpMetadata?.profileUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: bannerPfpMetadata.profileUrl,\n                                        alt: \"Profile\",\n                                        className: \"w-full h-full object-cover\",\n                                        style: {\n                                            transform: `scale(${bannerPfpMetadata.profileScale || 1})`,\n                                            transformOrigin: 'center'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-neutral-800 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 text-xs\",\n                                            children: \"No Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center w-full\",\n                        style: {\n                            left: `${profileNameHorizontalPosition}%`,\n                            transform: 'translateX(-50%)',\n                            position: 'relative'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center px-4\",\n                            children: [\n                                profileName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-2\",\n                                    style: {\n                                        color: componentData.fontColor || '#ffffff'\n                                    },\n                                    children: profileName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                profileBio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-300\",\n                                    style: {\n                                        color: componentData.fontColor || '#ffffff'\n                                    },\n                                    children: profileBio\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-purple-900/30 text-purple-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Banner/PFP\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_bannerpfp.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/renders/render_bannerpfp.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/renders/render_hero.tsx":
/*!************************************************!*\
  !*** ./app/components/renders/render_hero.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderHero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction RenderHero({ address, componentData, showPositionLabel = false }) {\n    const [heroContent, setHeroContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderHero.useEffect\": ()=>{\n            const loadHeroContent = {\n                \"RenderHero.useEffect.loadHeroContent\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        // Fetch hero content from API\n                        const response = await fetch(`/api/hero/${address}`);\n                        if (!response.ok) {\n                            throw new Error('Failed to load hero content');\n                        }\n                        const data = await response.json();\n                        if (data && data.heroContent && Array.isArray(data.heroContent)) {\n                            // Ensure all loaded content has textEffect field for backward compatibility\n                            const contentWithTextEffect = data.heroContent.map({\n                                \"RenderHero.useEffect.loadHeroContent.contentWithTextEffect\": (item)=>({\n                                        ...item,\n                                        textEffect: item.textEffect || 'decrypted'\n                                    })\n                            }[\"RenderHero.useEffect.loadHeroContent.contentWithTextEffect\"]);\n                            setHeroContent(contentWithTextEffect);\n                        } else {\n                            setError('No hero content found');\n                        }\n                    } catch (error) {\n                        console.error('Error loading hero content:', error);\n                        setError('Failed to load hero content');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"RenderHero.useEffect.loadHeroContent\"];\n            if (address) {\n                loadHeroContent();\n            }\n        }\n    }[\"RenderHero.useEffect\"], [\n        address\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || heroContent.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-neutral-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error || 'No hero content available'\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 overflow-visible w-full max-w-[100vw] sm:max-w-[96vw] md:max-w-[94vw] mx-auto p-0\",\n        style: {\n            backgroundColor: componentData.backgroundColor || 'transparent'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: heroContent.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 last:mb-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: item.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg mb-4\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: item.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            item.contentType === 'image' && item.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-64 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.imageUrl,\n                                    className: \"w-full h-full object-contain\",\n                                    alt: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            item.contentText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: item.contentText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-green-900/30 text-green-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Hero\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_hero.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/renders/render_hero.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/renders/render_sociallinks.tsx":
/*!*******************************************************!*\
  !*** ./app/components/renders/render_sociallinks.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RenderSocialLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction RenderSocialLinks({ profileData, componentData, showPositionLabel = false }) {\n    const [referralCode, setReferralCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [referralCount, setReferralCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RenderSocialLinks.useEffect\": ()=>{\n            const fetchReferralData = {\n                \"RenderSocialLinks.useEffect.fetchReferralData\": async ()=>{\n                    try {\n                        const response = await fetch(`/api/referral/${profileData.address}`);\n                        if (response.ok) {\n                            const data = await response.json();\n                            setReferralCode(data.referralCode);\n                            setReferralCount(data.referralCount || 0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching referral data:', error);\n                    }\n                }\n            }[\"RenderSocialLinks.useEffect.fetchReferralData\"];\n            if (profileData.address) {\n                fetchReferralData();\n            }\n        }\n    }[\"RenderSocialLinks.useEffect\"], [\n        profileData.address\n    ]);\n    const copyReferralCode = async ()=>{\n        if (referralCode) {\n            try {\n                await navigator.clipboard.writeText(referralCode);\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (error) {\n                console.error('Failed to copy referral code:', error);\n            }\n        }\n    };\n    // Helper function to get social media icons\n    const getSocialIcon = (platform)=>{\n        // Simple text-based icons for view-only mode\n        const platformIcons = {\n            twitter: '🐦',\n            github: '🐙',\n            linkedin: '💼',\n            instagram: '📷',\n            website: '🌐',\n            email: '📧',\n            youtube: '📺',\n            twitch: '🎮',\n            telegram: '✈️',\n            discord: '💬',\n            facebook: '📘',\n            cro: '💎'\n        };\n        return platformIcons[platform.toLowerCase()] || '🔗';\n    };\n    // Format social links if they exist\n    const formatSocialLinks = ()=>{\n        if (!componentData.socialLinks) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-1 text-center py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-neutral-400\",\n                    children: \"No social links added yet\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this);\n        }\n        try {\n            const links = typeof componentData.socialLinks === 'string' ? JSON.parse(componentData.socialLinks) : componentData.socialLinks;\n            if (!links || Object.keys(links).length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this);\n            }\n            // Format links for display\n            const formattedLinks = Object.entries(links).filter(([_, url])=>url) // Filter out empty URLs\n            .map(([platform, url])=>{\n                const urlStr = String(url);\n                const isCustom = platform.startsWith('custom');\n                let customLabel = '';\n                let finalUrl = urlStr;\n                if (isCustom && urlStr.includes('|')) {\n                    const parts = urlStr.split('|');\n                    if (parts.length >= 2) {\n                        customLabel = parts[0].trim();\n                        finalUrl = parts.slice(1).join('|').trim();\n                    }\n                }\n                const displayName = isCustom ? customLabel || 'Custom Link' : platform;\n                const icon = getSocialIcon(platform);\n                return {\n                    platform,\n                    url: finalUrl,\n                    displayName,\n                    icon\n                };\n            });\n            if (formattedLinks.length === 0) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-1 text-center py-2\",\n                    style: {\n                        color: componentData.fontColor || undefined\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-400\",\n                        children: \"No social links added yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-4 p-4\",\n                style: {\n                    color: componentData.fontColor || undefined\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4\",\n                        children: formattedLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center gap-2 px-3 py-2 bg-neutral-800 hover:bg-neutral-700 rounded-lg transition-colors\",\n                                style: {\n                                    color: componentData.fontColor || '#ffffff'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: link.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: link.displayName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    referralCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-neutral-800/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-400 mb-2\",\n                                    children: \"Referral Code\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"px-3 py-1 bg-neutral-700 rounded text-sm font-mono\",\n                                            children: referralCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: copyReferralCode,\n                                            className: \"p-1 hover:bg-neutral-600 rounded transition-colors\",\n                                            title: \"Copy referral code\",\n                                            children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-4 h-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this),\n                                referralCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-neutral-500 mt-1\",\n                                    children: [\n                                        referralCount,\n                                        \" referral\",\n                                        referralCount !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this);\n        } catch (error) {\n            console.error('Error parsing social links:', error);\n            return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border border-neutral-800 w-full\",\n        style: {\n            backgroundColor: componentData.backgroundColor || 'transparent'\n        },\n        children: [\n            formatSocialLinks(),\n            showPositionLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 right-2 bg-blue-900/30 text-blue-400 px-2 py-1 rounded-full text-xs font-medium\",\n                children: \"Social Links\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\components\\\\renders\\\\render_sociallinks.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/renders/render_sociallinks.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/GridBgContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/GridBgContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GridBgProvider: () => (/* binding */ GridBgProvider),\n/* harmony export */   useGridBg: () => (/* binding */ useGridBg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GridBgProvider,useGridBg auto */ \n\nconst GridBgContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GridBgProvider({ children }) {\n    const [isGridBgDisabled, setIsGridBgDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridBgContext.Provider, {\n        value: {\n            isGridBgDisabled,\n            setIsGridBgDisabled\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\GridBgContext.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction useGridBg() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GridBgContext);\n    if (context === undefined) {\n        throw new Error('useGridBg must be used within a GridBgProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/GridBgContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/contexts/MetadataContext.tsx":
/*!******************************************!*\
  !*** ./app/contexts/MetadataContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetadataProvider: () => (/* binding */ MetadataProvider),\n/* harmony export */   useMetadata: () => (/* binding */ useMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MetadataProvider,useMetadata auto */ \n\nconst MetadataContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MetadataProvider({ children }) {\n    const [bannerMetadata, setBannerMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profilePictureMetadata, setProfilePictureMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bannerPfpMetadata, setBannerPfpMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAddress, setCurrentAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to fetch banner metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchBannerMetadata = async (address)=>{\n        console.log('Banner component is deprecated, using bannerpfp instead');\n        // Try to get banner data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.bannerBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.bannerBlobUrl,\n                scale: bannerpfpData.bannerScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined\n            };\n            setBannerMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Function to fetch profile picture metadata - DEPRECATED\n    // Now using bannerpfp component instead\n    const fetchProfilePictureMetadata = async (address, compPosition)=>{\n        console.log('ProfilePicture component is deprecated, using bannerpfp instead');\n        // Try to get profile picture data from bannerpfp metadata\n        const bannerpfpData = await fetchBannerPfpMetadata(address);\n        if (bannerpfpData && bannerpfpData.profileBlobUrl) {\n            const completeMetadata = {\n                blobUrl: bannerpfpData.profileBlobUrl,\n                scale: bannerpfpData.profileScale || 1,\n                position: {\n                    x: 0,\n                    y: 0\n                },\n                naturalSize: undefined,\n                shape: bannerpfpData.profileShape || 'circular'\n            };\n            setProfilePictureMetadata(completeMetadata);\n            return completeMetadata;\n        }\n        return null;\n    };\n    // Track in-progress fetches to prevent duplicate calls\n    const fetchingAddresses = new Set();\n    // Function to fetch bannerpfp metadata\n    const fetchBannerPfpMetadata = async (address)=>{\n        // If we already have metadata for this address, return it\n        if (bannerPfpMetadata && currentAddress === address) {\n            console.log('MetadataContext: Using cached bannerpfp metadata for', address);\n            return bannerPfpMetadata;\n        }\n        // If we're already fetching this address, wait for it to complete\n        if (fetchingAddresses.has(address)) {\n            console.log('MetadataContext: Already fetching bannerpfp metadata for', address);\n            // Wait a bit and return the current metadata (which should be updated by the in-progress fetch)\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            return bannerPfpMetadata;\n        }\n        // Mark this address as being fetched\n        fetchingAddresses.add(address);\n        try {\n            console.log('MetadataContext: Fetching bannerpfp metadata for', address);\n            const response = await fetch(`/api/bannerpfp/${address}`);\n            if (!response.ok) {\n                // If the response is not OK, log the error but don't throw\n                // This allows the UI to continue rendering even if metadata is missing\n                console.warn(`Failed to fetch bannerpfp metadata: ${response.status} ${response.statusText}`);\n                // For 404 errors, we'll create a default metadata object\n                if (response.status === 404) {\n                    console.log('Creating default bannerpfp metadata since none exists');\n                    const defaultMetadata = {\n                        profileName: address.substring(0, 8),\n                        profileShape: 'circular',\n                        profileHorizontalPosition: 50,\n                        profileNameHorizontalPosition: 50,\n                        profileNameStyle: 'typewriter'\n                    };\n                    setBannerPfpMetadata(defaultMetadata);\n                    setCurrentAddress(address);\n                    return defaultMetadata;\n                }\n                return null;\n            }\n            const metadata = await response.json();\n            console.log('MetadataContext: Received bannerpfp metadata:', metadata);\n            if (metadata) {\n                console.log('MetadataContext: Setting bannerpfp metadata:', metadata);\n                setBannerPfpMetadata(metadata);\n                setCurrentAddress(address);\n                return metadata;\n            }\n        } catch (error) {\n            console.error('Failed to load bannerpfp metadata:', error);\n            // Create a default metadata object on error\n            const defaultMetadata = {\n                profileName: address.substring(0, 8),\n                profileShape: 'circular',\n                profileHorizontalPosition: 50,\n                profileNameHorizontalPosition: 50,\n                profileNameStyle: 'typewriter'\n            };\n            setBannerPfpMetadata(defaultMetadata);\n            setCurrentAddress(address);\n            return defaultMetadata;\n        } finally{\n            // Remove this address from the fetching set\n            fetchingAddresses.delete(address);\n        }\n        return null;\n    };\n    // Function to clear metadata (useful when changing addresses)\n    const clearMetadata = ()=>{\n        setBannerMetadata(null);\n        setProfilePictureMetadata(null);\n        setBannerPfpMetadata(null);\n        setCurrentAddress(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetadataContext.Provider, {\n        value: {\n            bannerMetadata,\n            profilePictureMetadata,\n            bannerPfpMetadata,\n            fetchBannerMetadata,\n            fetchProfilePictureMetadata,\n            fetchBannerPfpMetadata,\n            clearMetadata\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\app\\\\contexts\\\\MetadataContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\nfunction useMetadata() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MetadataContext);\n    if (context === undefined) {\n        throw new Error('useMetadata must be used within a MetadataProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contexts/MetadataContext.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Providers.tsx":
/*!**********************************!*\
  !*** ./components/Providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(ssr)/./config/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/contexts/MetadataContext */ \"(ssr)/./app/contexts/MetadataContext.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(ssr)/./components/ThemeProvider.tsx\");\n/* harmony import */ var _app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/contexts/GridBgContext */ \"(ssr)/./app/contexts/GridBgContext.tsx\");\n/* harmony import */ var _hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useWalletConnectionPersistence */ \"(ssr)/./hooks/useWalletConnectionPersistence.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Create a client outside the component\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: false,\n            retry: 1\n        }\n    }\n});\n// Inner component to use hooks after WagmiProvider is mounted\nfunction ProvidersContent({ children }) {\n    // Use the wallet connection persistence hook\n    (0,_hooks_useWalletConnectionPersistence__WEBPACK_IMPORTED_MODULE_6__.useWalletConnectionPersistence)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_MetadataContext__WEBPACK_IMPORTED_MODULE_3__.MetadataProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_contexts_GridBgContext__WEBPACK_IMPORTED_MODULE_5__.GridBgProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Providers.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Providers.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_8__.WagmiProvider, {\n        config: _config__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: false,\n                forcedTheme: \"dark\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProvidersContent, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\Providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ThemeProvider.tsx":
/*!**************************************!*\
  !*** ./components/ThemeProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\WebPages\\\\Web3Socials\\\\profiles-view\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJDOlxcV2ViUGFnZXNcXFdlYjNTb2NpYWxzXFxwcm9maWxlcy12aWV3XFxjb21wb25lbnRzXFxUaGVtZVByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gJ25leHQtdGhlbWVzJ1xuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./config/index.tsx":
/*!**************************!*\
  !*** ./config/index.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   networks: () => (/* binding */ networks),\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reown/appkit-adapter-solana */ \"(ssr)/./node_modules/@reown/appkit-adapter-solana/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n/* __next_internal_client_entry_do_not_use__ projectId,metadata,networks,wagmiAdapter,config auto */ \n\n\n\nconst projectId = \"63cb69e4a11f991fe106897d3eede1ed\";\nif (!projectId) {\n    throw new Error(\"Project ID is not defined\");\n}\n// Validate project ID format\nif (projectId.length !== 32) {\n    console.warn('Project ID may be invalid - should be 32 characters');\n}\nconst metadata = {\n    name: 'Web3Socials',\n    description: 'Web3 Social Platform',\n    url:  false ? 0 : 'https://web3socials.fun',\n    icons: [\n        'https://avatars.githubusercontent.com/u/179229932'\n    ]\n};\n// Temporarily reduce networks to test 403 error - you can add more back later\nconst networks = [\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronos,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.mainnet,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.arbitrum,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.sepolia,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.solana,\n    _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.cronoszkEVM\n];\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    ssr: true,\n    projectId,\n    networks\n});\nconst solanaWeb3JsAdapter = new _reown_appkit_adapter_solana__WEBPACK_IMPORTED_MODULE_3__.SolanaAdapter();\nconst generalConfig = {\n    projectId,\n    networks,\n    metadata,\n    themeMode: 'dark',\n    themeVariables: {\n        '--w3m-accent': '#000000'\n    }\n};\n// Prevent ethereum object conflicts\nif (false) {}\n(0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter,\n        solanaWeb3JsAdapter\n    ],\n    ...generalConfig,\n    features: {\n        swaps: false,\n        onramp: false,\n        email: true,\n        socials: false,\n        history: false,\n        analytics: false,\n        allWallets: true,\n        send: false\n    },\n    // Disable features that might cause network requests\n    featuredWalletIds: []\n});\nconst config = wagmiAdapter.wagmiConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./config/index.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useWalletConnectionPersistence.ts":
/*!*************************************************!*\
  !*** ./hooks/useWalletConnectionPersistence.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWalletConnectionPersistence: () => (/* binding */ useWalletConnectionPersistence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useWalletConnectionPersistence auto */ \n\n\n/**\n * Simplified hook to log wallet connection status for debugging\n * AppKit handles connection persistence automatically\n */ function useWalletConnectionPersistence() {\n    const { isConnected, address } = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_1__.useAppKitAccount)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Log connection status for debugging only\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWalletConnectionPersistence.useEffect\": ()=>{\n            console.log('[Wallet Connection] Path changed to:', pathname);\n            console.log('[Wallet Connection] isConnected:', isConnected);\n            console.log('[Wallet Connection] address:', address);\n        }\n    }[\"useWalletConnectionPersistence.useEffect\"], [\n        pathname,\n        isConnected,\n        address\n    ]);\n    return {\n        isConnected,\n        address\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ob29rcy91c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7b0ZBRWtDO0FBQ3FCO0FBQ1Q7QUFFOUM7OztDQUdDLEdBQ00sU0FBU0c7SUFDZCxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdKLHFFQUFnQkE7SUFDakQsTUFBTUssV0FBV0osNERBQVdBO0lBRTVCLDJDQUEyQztJQUMzQ0YsZ0RBQVNBO29EQUFDO1lBQ1JPLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NGO1lBQ3BEQyxRQUFRQyxHQUFHLENBQUMsb0NBQW9DSjtZQUNoREcsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0g7UUFDOUM7bURBQUc7UUFBQ0M7UUFBVUY7UUFBYUM7S0FBUTtJQUVuQyxPQUFPO1FBQUVEO1FBQWFDO0lBQVE7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXGhvb2tzXFx1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBcHBLaXRBY2NvdW50IH0gZnJvbSAnQHJlb3duL2FwcGtpdC9yZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbi8qKlxuICogU2ltcGxpZmllZCBob29rIHRvIGxvZyB3YWxsZXQgY29ubmVjdGlvbiBzdGF0dXMgZm9yIGRlYnVnZ2luZ1xuICogQXBwS2l0IGhhbmRsZXMgY29ubmVjdGlvbiBwZXJzaXN0ZW5jZSBhdXRvbWF0aWNhbGx5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UoKSB7XG4gIGNvbnN0IHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfSA9IHVzZUFwcEtpdEFjY291bnQoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIC8vIExvZyBjb25uZWN0aW9uIHN0YXR1cyBmb3IgZGVidWdnaW5nIG9ubHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBQYXRoIGNoYW5nZWQgdG86JywgcGF0aG5hbWUpO1xuICAgIGNvbnNvbGUubG9nKCdbV2FsbGV0IENvbm5lY3Rpb25dIGlzQ29ubmVjdGVkOicsIGlzQ29ubmVjdGVkKTtcbiAgICBjb25zb2xlLmxvZygnW1dhbGxldCBDb25uZWN0aW9uXSBhZGRyZXNzOicsIGFkZHJlc3MpO1xuICB9LCBbcGF0aG5hbWUsIGlzQ29ubmVjdGVkLCBhZGRyZXNzXSk7XG5cbiAgcmV0dXJuIHsgaXNDb25uZWN0ZWQsIGFkZHJlc3MgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBcHBLaXRBY2NvdW50IiwidXNlUGF0aG5hbWUiLCJ1c2VXYWxsZXRDb25uZWN0aW9uUGVyc2lzdGVuY2UiLCJpc0Nvbm5lY3RlZCIsImFkZHJlc3MiLCJwYXRobmFtZSIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useWalletConnectionPersistence.ts\n");

/***/ }),

/***/ "(ssr)/./lib/profileStatus.ts":
/*!******************************!*\
  !*** ./lib/profileStatus.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkProfileStatus: () => (/* binding */ checkProfileStatus),\n/* harmony export */   clearProfileStatusCache: () => (/* binding */ clearProfileStatusCache)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ checkProfileStatus,clearProfileStatusCache auto */ // Cache for profile status to avoid repeated fetches\nlet profileStatusCache = {};\n/**\n * Fetch profile status from the database\n * @param addressOrName Address or name of the profile to check\n * @returns Promise with profile status\n */ async function checkProfileStatus(addressOrName) {\n    // Return from cache if available\n    if (profileStatusCache[addressOrName]) {\n        const cached = profileStatusCache[addressOrName];\n        return {\n            ...cached,\n            message: getStatusMessage(cached.status)\n        };\n    }\n    try {\n        // Fetch profile status from API\n        const response = await fetch(`/api/profile/check-status?identifier=${addressOrName}`);\n        if (!response.ok) {\n            throw new Error(`Failed to fetch profile status: ${response.statusText}`);\n        }\n        const data = await response.json();\n        // Cache the result\n        profileStatusCache[addressOrName] = {\n            status: data.status,\n            isApproved: data.status === 'approved',\n            address: data.address,\n            name: data.name,\n            transactionHash: data.transactionHash,\n            expiryDate: data.expiryDate\n        };\n        return {\n            status: data.status,\n            isApproved: data.status === 'approved',\n            message: getStatusMessage(data.status),\n            address: data.address,\n            name: data.name,\n            transactionHash: data.transactionHash,\n            expiryDate: data.expiryDate\n        };\n    } catch (error) {\n        console.error(`Error fetching profile status for ${addressOrName}:`, error);\n        // Return default values on error\n        return {\n            status: 'error',\n            isApproved: false,\n            message: 'Error checking profile status. Please try again later.'\n        };\n    }\n}\n/**\n * Get a user-friendly message based on profile status\n */ function getStatusMessage(status) {\n    switch(status){\n        case 'approved':\n            return 'Profile is approved and accessible.';\n        case 'new':\n            return 'This profile is new and waiting for approval. Please burn tokens and provide a transaction hash for verification.';\n        case 'in-progress':\n            return 'This profile is being processed. Please check back later.';\n        case 'pending':\n            return 'This profile is pending approval. Please check back later.';\n        case 'deleted':\n            return 'This profile has been deleted.';\n        case 'expired':\n            return 'This profile has expired. Please contact an admin to renew it.';\n        default:\n            return 'Profile status is unknown.';\n    }\n}\n/**\n * Clear the profile status cache\n */ function clearProfileStatusCache() {\n    profileStatusCache = {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/profileStatus.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[name]/ClientPage.tsx */ \"(ssr)/./app/[name]/ClientPage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXZWJQYWdlcyU1QyU1Q1dlYjNTb2NpYWxzJTVDJTVDcHJvZmlsZXMtdmlldyU1QyU1Q2FwcCU1QyU1QyU1Qm5hbWUlNUQlNUMlNUNDbGllbnRQYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFdlYlBhZ2VzXFxcXFdlYjNTb2NpYWxzXFxcXHByb2ZpbGVzLXZpZXdcXFxcYXBwXFxcXFtuYW1lXVxcXFxDbGllbnRQYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5C%5Bname%5D%5C%5CClientPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Providers.tsx */ \"(ssr)/./components/Providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWebPages%5C%5CWeb3Socials%5C%5Cprofiles-view%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pino-pretty":
/*!******************************!*\
  !*** external "pino-pretty" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("pino-pretty");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/lit-html","vendor-chunks/@lit","vendor-chunks/lit","vendor-chunks/next","vendor-chunks/viem","vendor-chunks/@walletconnect","vendor-chunks/@noble","vendor-chunks/@wagmi","vendor-chunks/ox","vendor-chunks/abitype","vendor-chunks/@solana","vendor-chunks/tr46","vendor-chunks/rpc-websockets","vendor-chunks/ws","vendor-chunks/bn.js","vendor-chunks/sonner","vendor-chunks/@tanstack","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/valtio","vendor-chunks/superstruct","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/big.js","vendor-chunks/borsh","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/text-encoding-utf-8","vendor-chunks/zustand","vendor-chunks/uuid","vendor-chunks/dayjs","vendor-chunks/eventemitter3","vendor-chunks/lit-element","vendor-chunks/detect-browser","vendor-chunks/jayson","vendor-chunks/@wallet-standard","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/quick-format-unescaped","vendor-chunks/proxy-compare","vendor-chunks/mipd","vendor-chunks/@swc","vendor-chunks/destr","vendor-chunks/derive-valtio","vendor-chunks/utf-8-validate","vendor-chunks/safe-buffer","vendor-chunks/wagmi","vendor-chunks/atomic-sleep","vendor-chunks/bufferutil","vendor-chunks/bs58","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Bname%5D%2Fpage&page=%2F%5Bname%5D%2Fpage&appPaths=%2F%5Bname%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bname%5D%2Fpage.tsx&appDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWebPages%5CWeb3Socials%5Cprofiles-view&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();