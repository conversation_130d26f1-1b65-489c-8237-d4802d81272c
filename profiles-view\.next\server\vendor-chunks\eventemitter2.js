/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventemitter2";
exports.ids = ["vendor-chunks/eventemitter2"];
exports.modules = {

/***/ "(ssr)/./node_modules/eventemitter2/lib/eventemitter2.js":
/*!*********************************************************!*\
  !*** ./node_modules/eventemitter2/lib/eventemitter2.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;/*!\n * EventEmitter2\n * https://github.com/hij1nx/EventEmitter2\n *\n * Copyright (c) 2013 hij1nx\n * Licensed under the MIT license.\n */\n;!function(undefined) {\n  var hasOwnProperty= Object.hasOwnProperty;\n  var isArray = Array.isArray ? Array.isArray : function _isArray(obj) {\n    return Object.prototype.toString.call(obj) === \"[object Array]\";\n  };\n  var defaultMaxListeners = 10;\n  var nextTickSupported= typeof process=='object' && typeof process.nextTick=='function';\n  var symbolsSupported= typeof Symbol==='function';\n  var reflectSupported= typeof Reflect === 'object';\n  var setImmediateSupported= typeof setImmediate === 'function';\n  var _setImmediate= setImmediateSupported ? setImmediate : setTimeout;\n  var ownKeys= symbolsSupported? (reflectSupported && typeof Reflect.ownKeys==='function'? Reflect.ownKeys : function(obj){\n    var arr= Object.getOwnPropertyNames(obj);\n    arr.push.apply(arr, Object.getOwnPropertySymbols(obj));\n    return arr;\n  }) : Object.keys;\n\n  function init() {\n    this._events = {};\n    if (this._conf) {\n      configure.call(this, this._conf);\n    }\n  }\n\n  function configure(conf) {\n    if (conf) {\n      this._conf = conf;\n\n      conf.delimiter && (this.delimiter = conf.delimiter);\n\n      if(conf.maxListeners!==undefined){\n          this._maxListeners= conf.maxListeners;\n      }\n\n      conf.wildcard && (this.wildcard = conf.wildcard);\n      conf.newListener && (this._newListener = conf.newListener);\n      conf.removeListener && (this._removeListener = conf.removeListener);\n      conf.verboseMemoryLeak && (this.verboseMemoryLeak = conf.verboseMemoryLeak);\n      conf.ignoreErrors && (this.ignoreErrors = conf.ignoreErrors);\n\n      if (this.wildcard) {\n        this.listenerTree = {};\n      }\n    }\n  }\n\n  function logPossibleMemoryLeak(count, eventName) {\n    var errorMsg = '(node) warning: possible EventEmitter memory ' +\n        'leak detected. ' + count + ' listeners added. ' +\n        'Use emitter.setMaxListeners() to increase limit.';\n\n    if(this.verboseMemoryLeak){\n      errorMsg += ' Event name: ' + eventName + '.';\n    }\n\n    if(typeof process !== 'undefined' && process.emitWarning){\n      var e = new Error(errorMsg);\n      e.name = 'MaxListenersExceededWarning';\n      e.emitter = this;\n      e.count = count;\n      process.emitWarning(e);\n    } else {\n      console.error(errorMsg);\n\n      if (console.trace){\n        console.trace();\n      }\n    }\n  }\n\n  var toArray = function (a, b, c) {\n    var n = arguments.length;\n    switch (n) {\n      case 0:\n        return [];\n      case 1:\n        return [a];\n      case 2:\n        return [a, b];\n      case 3:\n        return [a, b, c];\n      default:\n        var arr = new Array(n);\n        while (n--) {\n          arr[n] = arguments[n];\n        }\n        return arr;\n    }\n  };\n\n  function toObject(keys, values) {\n    var obj = {};\n    var key;\n    var len = keys.length;\n    var valuesCount = values ? values.length : 0;\n    for (var i = 0; i < len; i++) {\n      key = keys[i];\n      obj[key] = i < valuesCount ? values[i] : undefined;\n    }\n    return obj;\n  }\n\n  function TargetObserver(emitter, target, options) {\n    this._emitter = emitter;\n    this._target = target;\n    this._listeners = {};\n    this._listenersCount = 0;\n\n    var on, off;\n\n    if (options.on || options.off) {\n      on = options.on;\n      off = options.off;\n    }\n\n    if (target.addEventListener) {\n      on = target.addEventListener;\n      off = target.removeEventListener;\n    } else if (target.addListener) {\n      on = target.addListener;\n      off = target.removeListener;\n    } else if (target.on) {\n      on = target.on;\n      off = target.off;\n    }\n\n    if (!on && !off) {\n      throw Error('target does not implement any known event API');\n    }\n\n    if (typeof on !== 'function') {\n      throw TypeError('on method must be a function');\n    }\n\n    if (typeof off !== 'function') {\n      throw TypeError('off method must be a function');\n    }\n\n    this._on = on;\n    this._off = off;\n\n    var _observers= emitter._observers;\n    if(_observers){\n      _observers.push(this);\n    }else{\n      emitter._observers= [this];\n    }\n  }\n\n  Object.assign(TargetObserver.prototype, {\n    subscribe: function(event, localEvent, reducer){\n      var observer= this;\n      var target= this._target;\n      var emitter= this._emitter;\n      var listeners= this._listeners;\n      var handler= function(){\n        var args= toArray.apply(null, arguments);\n        var eventObj= {\n          data: args,\n          name: localEvent,\n          original: event\n        };\n        if(reducer){\n          var result= reducer.call(target, eventObj);\n          if(result!==false){\n            emitter.emit.apply(emitter, [eventObj.name].concat(args))\n          }\n          return;\n        }\n        emitter.emit.apply(emitter, [localEvent].concat(args));\n      };\n\n\n      if(listeners[event]){\n        throw Error('Event \\'' + event + '\\' is already listening');\n      }\n\n      this._listenersCount++;\n\n      if(emitter._newListener && emitter._removeListener && !observer._onNewListener){\n\n        this._onNewListener = function (_event) {\n          if (_event === localEvent && listeners[event] === null) {\n            listeners[event] = handler;\n            observer._on.call(target, event, handler);\n          }\n        };\n\n        emitter.on('newListener', this._onNewListener);\n\n        this._onRemoveListener= function(_event){\n          if(_event === localEvent && !emitter.hasListeners(_event) && listeners[event]){\n            listeners[event]= null;\n            observer._off.call(target, event, handler);\n          }\n        };\n\n        listeners[event]= null;\n\n        emitter.on('removeListener', this._onRemoveListener);\n      }else{\n        listeners[event]= handler;\n        observer._on.call(target, event, handler);\n      }\n    },\n\n    unsubscribe: function(event){\n      var observer= this;\n      var listeners= this._listeners;\n      var emitter= this._emitter;\n      var handler;\n      var events;\n      var off= this._off;\n      var target= this._target;\n      var i;\n\n      if(event && typeof event!=='string'){\n        throw TypeError('event must be a string');\n      }\n\n      function clearRefs(){\n        if(observer._onNewListener){\n          emitter.off('newListener', observer._onNewListener);\n          emitter.off('removeListener', observer._onRemoveListener);\n          observer._onNewListener= null;\n          observer._onRemoveListener= null;\n        }\n        var index= findTargetIndex.call(emitter, observer);\n        emitter._observers.splice(index, 1);\n      }\n\n      if(event){\n        handler= listeners[event];\n        if(!handler) return;\n        off.call(target, event, handler);\n        delete listeners[event];\n        if(!--this._listenersCount){\n          clearRefs();\n        }\n      }else{\n        events= ownKeys(listeners);\n        i= events.length;\n        while(i-->0){\n          event= events[i];\n          off.call(target, event, listeners[event]);\n        }\n        this._listeners= {};\n        this._listenersCount= 0;\n        clearRefs();\n      }\n    }\n  });\n\n  function resolveOptions(options, schema, reducers, allowUnknown) {\n    var computedOptions = Object.assign({}, schema);\n\n    if (!options) return computedOptions;\n\n    if (typeof options !== 'object') {\n      throw TypeError('options must be an object')\n    }\n\n    var keys = Object.keys(options);\n    var length = keys.length;\n    var option, value;\n    var reducer;\n\n    function reject(reason) {\n      throw Error('Invalid \"' + option + '\" option value' + (reason ? '. Reason: ' + reason : ''))\n    }\n\n    for (var i = 0; i < length; i++) {\n      option = keys[i];\n      if (!allowUnknown && !hasOwnProperty.call(schema, option)) {\n        throw Error('Unknown \"' + option + '\" option');\n      }\n      value = options[option];\n      if (value !== undefined) {\n        reducer = reducers[option];\n        computedOptions[option] = reducer ? reducer(value, reject) : value;\n      }\n    }\n    return computedOptions;\n  }\n\n  function constructorReducer(value, reject) {\n    if (typeof value !== 'function' || !value.hasOwnProperty('prototype')) {\n      reject('value must be a constructor');\n    }\n    return value;\n  }\n\n  function makeTypeReducer(types) {\n    var message= 'value must be type of ' + types.join('|');\n    var len= types.length;\n    var firstType= types[0];\n    var secondType= types[1];\n\n    if (len === 1) {\n      return function (v, reject) {\n        if (typeof v === firstType) {\n          return v;\n        }\n        reject(message);\n      }\n    }\n\n    if (len === 2) {\n      return function (v, reject) {\n        var kind= typeof v;\n        if (kind === firstType || kind === secondType) return v;\n        reject(message);\n      }\n    }\n\n    return function (v, reject) {\n      var kind = typeof v;\n      var i = len;\n      while (i-- > 0) {\n        if (kind === types[i]) return v;\n      }\n      reject(message);\n    }\n  }\n\n  var functionReducer= makeTypeReducer(['function']);\n\n  var objectFunctionReducer= makeTypeReducer(['object', 'function']);\n\n  function makeCancelablePromise(Promise, executor, options) {\n    var isCancelable;\n    var callbacks;\n    var timer= 0;\n    var subscriptionClosed;\n\n    var promise = new Promise(function (resolve, reject, onCancel) {\n      options= resolveOptions(options, {\n        timeout: 0,\n        overload: false\n      }, {\n        timeout: function(value, reject){\n          value*= 1;\n          if (typeof value !== 'number' || value < 0 || !Number.isFinite(value)) {\n            reject('timeout must be a positive number');\n          }\n          return value;\n        }\n      });\n\n      isCancelable = !options.overload && typeof Promise.prototype.cancel === 'function' && typeof onCancel === 'function';\n\n      function cleanup() {\n        if (callbacks) {\n          callbacks = null;\n        }\n        if (timer) {\n          clearTimeout(timer);\n          timer = 0;\n        }\n      }\n\n      var _resolve= function(value){\n        cleanup();\n        resolve(value);\n      };\n\n      var _reject= function(err){\n        cleanup();\n        reject(err);\n      };\n\n      if (isCancelable) {\n        executor(_resolve, _reject, onCancel);\n      } else {\n        callbacks = [function(reason){\n          _reject(reason || Error('canceled'));\n        }];\n        executor(_resolve, _reject, function (cb) {\n          if (subscriptionClosed) {\n            throw Error('Unable to subscribe on cancel event asynchronously')\n          }\n          if (typeof cb !== 'function') {\n            throw TypeError('onCancel callback must be a function');\n          }\n          callbacks.push(cb);\n        });\n        subscriptionClosed= true;\n      }\n\n      if (options.timeout > 0) {\n        timer= setTimeout(function(){\n          var reason= Error('timeout');\n          reason.code = 'ETIMEDOUT'\n          timer= 0;\n          promise.cancel(reason);\n          reject(reason);\n        }, options.timeout);\n      }\n    });\n\n    if (!isCancelable) {\n      promise.cancel = function (reason) {\n        if (!callbacks) {\n          return;\n        }\n        var length = callbacks.length;\n        for (var i = 1; i < length; i++) {\n          callbacks[i](reason);\n        }\n        // internal callback to reject the promise\n        callbacks[0](reason);\n        callbacks = null;\n      };\n    }\n\n    return promise;\n  }\n\n  function findTargetIndex(observer) {\n    var observers = this._observers;\n    if(!observers){\n      return -1;\n    }\n    var len = observers.length;\n    for (var i = 0; i < len; i++) {\n      if (observers[i]._target === observer) return i;\n    }\n    return -1;\n  }\n\n  // Attention, function return type now is array, always !\n  // It has zero elements if no any matches found and one or more\n  // elements (leafs) if there are matches\n  //\n  function searchListenerTree(handlers, type, tree, i, typeLength) {\n    if (!tree) {\n      return null;\n    }\n\n    if (i === 0) {\n      var kind = typeof type;\n      if (kind === 'string') {\n        var ns, n, l = 0, j = 0, delimiter = this.delimiter, dl = delimiter.length;\n        if ((n = type.indexOf(delimiter)) !== -1) {\n          ns = new Array(5);\n          do {\n            ns[l++] = type.slice(j, n);\n            j = n + dl;\n          } while ((n = type.indexOf(delimiter, j)) !== -1);\n\n          ns[l++] = type.slice(j);\n          type = ns;\n          typeLength = l;\n        } else {\n          type = [type];\n          typeLength = 1;\n        }\n      } else if (kind === 'object') {\n        typeLength = type.length;\n      } else {\n        type = [type];\n        typeLength = 1;\n      }\n    }\n\n    var listeners= null, branch, xTree, xxTree, isolatedBranch, endReached, currentType = type[i],\n        nextType = type[i + 1], branches, _listeners;\n\n    if (i === typeLength) {\n      //\n      // If at the end of the event(s) list and the tree has listeners\n      // invoke those listeners.\n      //\n\n      if(tree._listeners) {\n        if (typeof tree._listeners === 'function') {\n          handlers && handlers.push(tree._listeners);\n          listeners = [tree];\n        } else {\n          handlers && handlers.push.apply(handlers, tree._listeners);\n          listeners = [tree];\n        }\n      }\n    } else {\n\n      if (currentType === '*') {\n        //\n        // If the event emitted is '*' at this part\n        // or there is a concrete match at this patch\n        //\n        branches = ownKeys(tree);\n        n = branches.length;\n        while (n-- > 0) {\n          branch = branches[n];\n          if (branch !== '_listeners') {\n            _listeners = searchListenerTree(handlers, type, tree[branch], i + 1, typeLength);\n            if (_listeners) {\n              if (listeners) {\n                listeners.push.apply(listeners, _listeners);\n              } else {\n                listeners = _listeners;\n              }\n            }\n          }\n        }\n        return listeners;\n      } else if (currentType === '**') {\n        endReached = (i + 1 === typeLength || (i + 2 === typeLength && nextType === '*'));\n        if (endReached && tree._listeners) {\n          // The next element has a _listeners, add it to the handlers.\n          listeners = searchListenerTree(handlers, type, tree, typeLength, typeLength);\n        }\n\n        branches = ownKeys(tree);\n        n = branches.length;\n        while (n-- > 0) {\n          branch = branches[n];\n          if (branch !== '_listeners') {\n            if (branch === '*' || branch === '**') {\n              if (tree[branch]._listeners && !endReached) {\n                _listeners = searchListenerTree(handlers, type, tree[branch], typeLength, typeLength);\n                if (_listeners) {\n                  if (listeners) {\n                    listeners.push.apply(listeners, _listeners);\n                  } else {\n                    listeners = _listeners;\n                  }\n                }\n              }\n              _listeners = searchListenerTree(handlers, type, tree[branch], i, typeLength);\n            } else if (branch === nextType) {\n              _listeners = searchListenerTree(handlers, type, tree[branch], i + 2, typeLength);\n            } else {\n              // No match on this one, shift into the tree but not in the type array.\n              _listeners = searchListenerTree(handlers, type, tree[branch], i, typeLength);\n            }\n            if (_listeners) {\n              if (listeners) {\n                listeners.push.apply(listeners, _listeners);\n              } else {\n                listeners = _listeners;\n              }\n            }\n          }\n        }\n        return listeners;\n      } else if (tree[currentType]) {\n        listeners = searchListenerTree(handlers, type, tree[currentType], i + 1, typeLength);\n      }\n    }\n\n      xTree = tree['*'];\n    if (xTree) {\n      //\n      // If the listener tree will allow any match for this part,\n      // then recursively explore all branches of the tree\n      //\n      searchListenerTree(handlers, type, xTree, i + 1, typeLength);\n    }\n\n    xxTree = tree['**'];\n    if (xxTree) {\n      if (i < typeLength) {\n        if (xxTree._listeners) {\n          // If we have a listener on a '**', it will catch all, so add its handler.\n          searchListenerTree(handlers, type, xxTree, typeLength, typeLength);\n        }\n\n        // Build arrays of matching next branches and others.\n        branches= ownKeys(xxTree);\n        n= branches.length;\n        while(n-->0){\n          branch= branches[n];\n          if (branch !== '_listeners') {\n            if (branch === nextType) {\n              // We know the next element will match, so jump twice.\n              searchListenerTree(handlers, type, xxTree[branch], i + 2, typeLength);\n            } else if (branch === currentType) {\n              // Current node matches, move into the tree.\n              searchListenerTree(handlers, type, xxTree[branch], i + 1, typeLength);\n            } else {\n              isolatedBranch = {};\n              isolatedBranch[branch] = xxTree[branch];\n              searchListenerTree(handlers, type, {'**': isolatedBranch}, i + 1, typeLength);\n            }\n          }\n        }\n      } else if (xxTree._listeners) {\n        // We have reached the end and still on a '**'\n        searchListenerTree(handlers, type, xxTree, typeLength, typeLength);\n      } else if (xxTree['*'] && xxTree['*']._listeners) {\n        searchListenerTree(handlers, type, xxTree['*'], typeLength, typeLength);\n      }\n    }\n\n    return listeners;\n  }\n\n  function growListenerTree(type, listener, prepend) {\n    var len = 0, j = 0, i, delimiter = this.delimiter, dl= delimiter.length, ns;\n\n    if(typeof type==='string') {\n      if ((i = type.indexOf(delimiter)) !== -1) {\n        ns = new Array(5);\n        do {\n          ns[len++] = type.slice(j, i);\n          j = i + dl;\n        } while ((i = type.indexOf(delimiter, j)) !== -1);\n\n        ns[len++] = type.slice(j);\n      }else{\n        ns= [type];\n        len= 1;\n      }\n    }else{\n      ns= type;\n      len= type.length;\n    }\n\n    //\n    // Looks for two consecutive '**', if so, don't add the event at all.\n    //\n    if (len > 1) {\n      for (i = 0; i + 1 < len; i++) {\n        if (ns[i] === '**' && ns[i + 1] === '**') {\n          return;\n        }\n      }\n    }\n\n\n\n    var tree = this.listenerTree, name;\n\n    for (i = 0; i < len; i++) {\n      name = ns[i];\n\n      tree = tree[name] || (tree[name] = {});\n\n      if (i === len - 1) {\n        if (!tree._listeners) {\n          tree._listeners = listener;\n        } else {\n          if (typeof tree._listeners === 'function') {\n            tree._listeners = [tree._listeners];\n          }\n\n          if (prepend) {\n            tree._listeners.unshift(listener);\n          } else {\n            tree._listeners.push(listener);\n          }\n\n          if (\n              !tree._listeners.warned &&\n              this._maxListeners > 0 &&\n              tree._listeners.length > this._maxListeners\n          ) {\n            tree._listeners.warned = true;\n            logPossibleMemoryLeak.call(this, tree._listeners.length, name);\n          }\n        }\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function collectTreeEvents(tree, events, root, asArray){\n     var branches= ownKeys(tree);\n     var i= branches.length;\n     var branch, branchName, path;\n     var hasListeners= tree['_listeners'];\n     var isArrayPath;\n\n     while(i-->0){\n         branchName= branches[i];\n\n         branch= tree[branchName];\n\n         if(branchName==='_listeners'){\n             path= root;\n         }else {\n             path = root ? root.concat(branchName) : [branchName];\n         }\n\n         isArrayPath= asArray || typeof branchName==='symbol';\n\n         hasListeners && events.push(isArrayPath? path : path.join(this.delimiter));\n\n         if(typeof branch==='object'){\n             collectTreeEvents.call(this, branch, events, path, isArrayPath);\n         }\n     }\n\n     return events;\n  }\n\n  function recursivelyGarbageCollect(root) {\n    var keys = ownKeys(root);\n    var i= keys.length;\n    var obj, key, flag;\n    while(i-->0){\n      key = keys[i];\n      obj = root[key];\n\n      if(obj){\n          flag= true;\n          if(key !== '_listeners' && !recursivelyGarbageCollect(obj)){\n             delete root[key];\n          }\n      }\n    }\n\n    return flag;\n  }\n\n  function Listener(emitter, event, listener){\n    this.emitter= emitter;\n    this.event= event;\n    this.listener= listener;\n  }\n\n  Listener.prototype.off= function(){\n    this.emitter.off(this.event, this.listener);\n    return this;\n  };\n\n  function setupListener(event, listener, options){\n      if (options === true) {\n        promisify = true;\n      } else if (options === false) {\n        async = true;\n      } else {\n        if (!options || typeof options !== 'object') {\n          throw TypeError('options should be an object or true');\n        }\n        var async = options.async;\n        var promisify = options.promisify;\n        var nextTick = options.nextTick;\n        var objectify = options.objectify;\n      }\n\n      if (async || nextTick || promisify) {\n        var _listener = listener;\n        var _origin = listener._origin || listener;\n\n        if (nextTick && !nextTickSupported) {\n          throw Error('process.nextTick is not supported');\n        }\n\n        if (promisify === undefined) {\n          promisify = listener.constructor.name === 'AsyncFunction';\n        }\n\n        listener = function () {\n          var args = arguments;\n          var context = this;\n          var event = this.event;\n\n          return promisify ? (nextTick ? Promise.resolve() : new Promise(function (resolve) {\n            _setImmediate(resolve);\n          }).then(function () {\n            context.event = event;\n            return _listener.apply(context, args)\n          })) : (nextTick ? process.nextTick : _setImmediate)(function () {\n            context.event = event;\n            _listener.apply(context, args)\n          });\n        };\n\n        listener._async = true;\n        listener._origin = _origin;\n      }\n\n    return [listener, objectify? new Listener(this, event, listener): this];\n  }\n\n  function EventEmitter(conf) {\n    this._events = {};\n    this._newListener = false;\n    this._removeListener = false;\n    this.verboseMemoryLeak = false;\n    configure.call(this, conf);\n  }\n\n  EventEmitter.EventEmitter2 = EventEmitter; // backwards compatibility for exporting EventEmitter property\n\n  EventEmitter.prototype.listenTo= function(target, events, options){\n    if(typeof target!=='object'){\n      throw TypeError('target musts be an object');\n    }\n\n    var emitter= this;\n\n    options = resolveOptions(options, {\n      on: undefined,\n      off: undefined,\n      reducers: undefined\n    }, {\n      on: functionReducer,\n      off: functionReducer,\n      reducers: objectFunctionReducer\n    });\n\n    function listen(events){\n      if(typeof events!=='object'){\n        throw TypeError('events must be an object');\n      }\n\n      var reducers= options.reducers;\n      var index= findTargetIndex.call(emitter, target);\n      var observer;\n\n      if(index===-1){\n        observer= new TargetObserver(emitter, target, options);\n      }else{\n        observer= emitter._observers[index];\n      }\n\n      var keys= ownKeys(events);\n      var len= keys.length;\n      var event;\n      var isSingleReducer= typeof reducers==='function';\n\n      for(var i=0; i<len; i++){\n        event= keys[i];\n        observer.subscribe(\n            event,\n            events[event] || event,\n            isSingleReducer ? reducers : reducers && reducers[event]\n        );\n      }\n    }\n\n    isArray(events)?\n        listen(toObject(events)) :\n        (typeof events==='string'? listen(toObject(events.split(/\\s+/))): listen(events));\n\n    return this;\n  };\n\n  EventEmitter.prototype.stopListeningTo = function (target, event) {\n    var observers = this._observers;\n\n    if(!observers){\n      return false;\n    }\n\n    var i = observers.length;\n    var observer;\n    var matched= false;\n\n    if(target && typeof target!=='object'){\n      throw TypeError('target should be an object');\n    }\n\n    while (i-- > 0) {\n      observer = observers[i];\n      if (!target || observer._target === target) {\n        observer.unsubscribe(event);\n        matched= true;\n      }\n    }\n\n    return matched;\n  };\n\n  // By default EventEmitters will print a warning if more than\n  // 10 listeners are added to it. This is a useful default which\n  // helps finding memory leaks.\n  //\n  // Obviously not all Emitters should be limited to 10. This function allows\n  // that to be increased. Set to zero for unlimited.\n\n  EventEmitter.prototype.delimiter = '.';\n\n  EventEmitter.prototype.setMaxListeners = function(n) {\n    if (n !== undefined) {\n      this._maxListeners = n;\n      if (!this._conf) this._conf = {};\n      this._conf.maxListeners = n;\n    }\n  };\n\n  EventEmitter.prototype.getMaxListeners = function() {\n    return this._maxListeners;\n  };\n\n  EventEmitter.prototype.event = '';\n\n  EventEmitter.prototype.once = function(event, fn, options) {\n    return this._once(event, fn, false, options);\n  };\n\n  EventEmitter.prototype.prependOnceListener = function(event, fn, options) {\n    return this._once(event, fn, true, options);\n  };\n\n  EventEmitter.prototype._once = function(event, fn, prepend, options) {\n    return this._many(event, 1, fn, prepend, options);\n  };\n\n  EventEmitter.prototype.many = function(event, ttl, fn, options) {\n    return this._many(event, ttl, fn, false, options);\n  };\n\n  EventEmitter.prototype.prependMany = function(event, ttl, fn, options) {\n    return this._many(event, ttl, fn, true, options);\n  };\n\n  EventEmitter.prototype._many = function(event, ttl, fn, prepend, options) {\n    var self = this;\n\n    if (typeof fn !== 'function') {\n      throw new Error('many only accepts instances of Function');\n    }\n\n    function listener() {\n      if (--ttl === 0) {\n        self.off(event, listener);\n      }\n      return fn.apply(this, arguments);\n    }\n\n    listener._origin = fn;\n\n    return this._on(event, listener, prepend, options);\n  };\n\n  EventEmitter.prototype.emit = function() {\n    if (!this._events && !this._all) {\n      return false;\n    }\n\n    this._events || init.call(this);\n\n    var type = arguments[0], ns, wildcard= this.wildcard;\n    var args,l,i,j, containsSymbol;\n\n    if (type === 'newListener' && !this._newListener) {\n      if (!this._events.newListener) {\n        return false;\n      }\n    }\n\n    if (wildcard) {\n      ns= type;\n      if(type!=='newListener' && type!=='removeListener'){\n        if (typeof type === 'object') {\n          l = type.length;\n          if (symbolsSupported) {\n            for (i = 0; i < l; i++) {\n              if (typeof type[i] === 'symbol') {\n                containsSymbol = true;\n                break;\n              }\n            }\n          }\n          if (!containsSymbol) {\n            type = type.join(this.delimiter);\n          }\n        }\n      }\n    }\n\n    var al = arguments.length;\n    var handler;\n\n    if (this._all && this._all.length) {\n      handler = this._all.slice();\n\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler[i].call(this, type);\n          break;\n        case 2:\n          handler[i].call(this, type, arguments[1]);\n          break;\n        case 3:\n          handler[i].call(this, type, arguments[1], arguments[2]);\n          break;\n        default:\n          handler[i].apply(this, arguments);\n        }\n      }\n    }\n\n    if (wildcard) {\n      handler = [];\n      searchListenerTree.call(this, handler, ns, this.listenerTree, 0, l);\n    } else {\n      handler = this._events[type];\n      if (typeof handler === 'function') {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler.call(this);\n          break;\n        case 2:\n          handler.call(this, arguments[1]);\n          break;\n        case 3:\n          handler.call(this, arguments[1], arguments[2]);\n          break;\n        default:\n          args = new Array(al - 1);\n          for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n          handler.apply(this, args);\n        }\n        return true;\n      } else if (handler) {\n        // need to make copy of handlers because list can change in the middle\n        // of emit call\n        handler = handler.slice();\n      }\n    }\n\n    if (handler && handler.length) {\n      if (al > 3) {\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n      }\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler[i].call(this);\n          break;\n        case 2:\n          handler[i].call(this, arguments[1]);\n          break;\n        case 3:\n          handler[i].call(this, arguments[1], arguments[2]);\n          break;\n        default:\n          handler[i].apply(this, args);\n        }\n      }\n      return true;\n    } else if (!this.ignoreErrors && !this._all && type === 'error') {\n      if (arguments[1] instanceof Error) {\n        throw arguments[1]; // Unhandled 'error' event\n      } else {\n        throw new Error(\"Uncaught, unspecified 'error' event.\");\n      }\n    }\n\n    return !!this._all;\n  };\n\n  EventEmitter.prototype.emitAsync = function() {\n    if (!this._events && !this._all) {\n      return false;\n    }\n\n    this._events || init.call(this);\n\n    var type = arguments[0], wildcard= this.wildcard, ns, containsSymbol;\n    var args,l,i,j;\n\n    if (type === 'newListener' && !this._newListener) {\n        if (!this._events.newListener) { return Promise.resolve([false]); }\n    }\n\n    if (wildcard) {\n      ns= type;\n      if(type!=='newListener' && type!=='removeListener'){\n        if (typeof type === 'object') {\n          l = type.length;\n          if (symbolsSupported) {\n            for (i = 0; i < l; i++) {\n              if (typeof type[i] === 'symbol') {\n                containsSymbol = true;\n                break;\n              }\n            }\n          }\n          if (!containsSymbol) {\n            type = type.join(this.delimiter);\n          }\n        }\n      }\n    }\n\n    var promises= [];\n\n    var al = arguments.length;\n    var handler;\n\n    if (this._all) {\n      for (i = 0, l = this._all.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          promises.push(this._all[i].call(this, type));\n          break;\n        case 2:\n          promises.push(this._all[i].call(this, type, arguments[1]));\n          break;\n        case 3:\n          promises.push(this._all[i].call(this, type, arguments[1], arguments[2]));\n          break;\n        default:\n          promises.push(this._all[i].apply(this, arguments));\n        }\n      }\n    }\n\n    if (wildcard) {\n      handler = [];\n      searchListenerTree.call(this, handler, ns, this.listenerTree, 0);\n    } else {\n      handler = this._events[type];\n    }\n\n    if (typeof handler === 'function') {\n      this.event = type;\n      switch (al) {\n      case 1:\n        promises.push(handler.call(this));\n        break;\n      case 2:\n        promises.push(handler.call(this, arguments[1]));\n        break;\n      case 3:\n        promises.push(handler.call(this, arguments[1], arguments[2]));\n        break;\n      default:\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n        promises.push(handler.apply(this, args));\n      }\n    } else if (handler && handler.length) {\n      handler = handler.slice();\n      if (al > 3) {\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n      }\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          promises.push(handler[i].call(this));\n          break;\n        case 2:\n          promises.push(handler[i].call(this, arguments[1]));\n          break;\n        case 3:\n          promises.push(handler[i].call(this, arguments[1], arguments[2]));\n          break;\n        default:\n          promises.push(handler[i].apply(this, args));\n        }\n      }\n    } else if (!this.ignoreErrors && !this._all && type === 'error') {\n      if (arguments[1] instanceof Error) {\n        return Promise.reject(arguments[1]); // Unhandled 'error' event\n      } else {\n        return Promise.reject(\"Uncaught, unspecified 'error' event.\");\n      }\n    }\n\n    return Promise.all(promises);\n  };\n\n  EventEmitter.prototype.on = function(type, listener, options) {\n    return this._on(type, listener, false, options);\n  };\n\n  EventEmitter.prototype.prependListener = function(type, listener, options) {\n    return this._on(type, listener, true, options);\n  };\n\n  EventEmitter.prototype.onAny = function(fn) {\n    return this._onAny(fn, false);\n  };\n\n  EventEmitter.prototype.prependAny = function(fn) {\n    return this._onAny(fn, true);\n  };\n\n  EventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n  EventEmitter.prototype._onAny = function(fn, prepend){\n    if (typeof fn !== 'function') {\n      throw new Error('onAny only accepts instances of Function');\n    }\n\n    if (!this._all) {\n      this._all = [];\n    }\n\n    // Add the function to the event listener collection.\n    if(prepend){\n      this._all.unshift(fn);\n    }else{\n      this._all.push(fn);\n    }\n\n    return this;\n  };\n\n  EventEmitter.prototype._on = function(type, listener, prepend, options) {\n    if (typeof type === 'function') {\n      this._onAny(type, listener);\n      return this;\n    }\n\n    if (typeof listener !== 'function') {\n      throw new Error('on only accepts instances of Function');\n    }\n    this._events || init.call(this);\n\n    var returnValue= this, temp;\n\n    if (options !== undefined) {\n      temp = setupListener.call(this, type, listener, options);\n      listener = temp[0];\n      returnValue = temp[1];\n    }\n\n    // To avoid recursion in the case that type == \"newListeners\"! Before\n    // adding it to the listeners, first emit \"newListeners\".\n    if (this._newListener) {\n      this.emit('newListener', type, listener);\n    }\n\n    if (this.wildcard) {\n      growListenerTree.call(this, type, listener, prepend);\n      return returnValue;\n    }\n\n    if (!this._events[type]) {\n      // Optimize the case of one listener. Don't need the extra array object.\n      this._events[type] = listener;\n    } else {\n      if (typeof this._events[type] === 'function') {\n        // Change to array.\n        this._events[type] = [this._events[type]];\n      }\n\n      // If we've already got an array, just add\n      if(prepend){\n        this._events[type].unshift(listener);\n      }else{\n        this._events[type].push(listener);\n      }\n\n      // Check for listener leak\n      if (\n        !this._events[type].warned &&\n        this._maxListeners > 0 &&\n        this._events[type].length > this._maxListeners\n      ) {\n        this._events[type].warned = true;\n        logPossibleMemoryLeak.call(this, this._events[type].length, type);\n      }\n    }\n\n    return returnValue;\n  };\n\n  EventEmitter.prototype.off = function(type, listener) {\n    if (typeof listener !== 'function') {\n      throw new Error('removeListener only takes instances of Function');\n    }\n\n    var handlers,leafs=[];\n\n    if(this.wildcard) {\n      var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n      leafs = searchListenerTree.call(this, null, ns, this.listenerTree, 0);\n      if(!leafs) return this;\n    } else {\n      // does not use listeners(), so no side effect of creating _events[type]\n      if (!this._events[type]) return this;\n      handlers = this._events[type];\n      leafs.push({_listeners:handlers});\n    }\n\n    for (var iLeaf=0; iLeaf<leafs.length; iLeaf++) {\n      var leaf = leafs[iLeaf];\n      handlers = leaf._listeners;\n      if (isArray(handlers)) {\n\n        var position = -1;\n\n        for (var i = 0, length = handlers.length; i < length; i++) {\n          if (handlers[i] === listener ||\n            (handlers[i].listener && handlers[i].listener === listener) ||\n            (handlers[i]._origin && handlers[i]._origin === listener)) {\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0) {\n          continue;\n        }\n\n        if(this.wildcard) {\n          leaf._listeners.splice(position, 1);\n        }\n        else {\n          this._events[type].splice(position, 1);\n        }\n\n        if (handlers.length === 0) {\n          if(this.wildcard) {\n            delete leaf._listeners;\n          }\n          else {\n            delete this._events[type];\n          }\n        }\n        if (this._removeListener)\n          this.emit(\"removeListener\", type, listener);\n\n        return this;\n      }\n      else if (handlers === listener ||\n        (handlers.listener && handlers.listener === listener) ||\n        (handlers._origin && handlers._origin === listener)) {\n        if(this.wildcard) {\n          delete leaf._listeners;\n        }\n        else {\n          delete this._events[type];\n        }\n        if (this._removeListener)\n          this.emit(\"removeListener\", type, listener);\n      }\n    }\n\n    this.listenerTree && recursivelyGarbageCollect(this.listenerTree);\n\n    return this;\n  };\n\n  EventEmitter.prototype.offAny = function(fn) {\n    var i = 0, l = 0, fns;\n    if (fn && this._all && this._all.length > 0) {\n      fns = this._all;\n      for(i = 0, l = fns.length; i < l; i++) {\n        if(fn === fns[i]) {\n          fns.splice(i, 1);\n          if (this._removeListener)\n            this.emit(\"removeListenerAny\", fn);\n          return this;\n        }\n      }\n    } else {\n      fns = this._all;\n      if (this._removeListener) {\n        for(i = 0, l = fns.length; i < l; i++)\n          this.emit(\"removeListenerAny\", fns[i]);\n      }\n      this._all = [];\n    }\n    return this;\n  };\n\n  EventEmitter.prototype.removeListener = EventEmitter.prototype.off;\n\n  EventEmitter.prototype.removeAllListeners = function (type) {\n    if (type === undefined) {\n      !this._events || init.call(this);\n      return this;\n    }\n\n    if (this.wildcard) {\n      var leafs = searchListenerTree.call(this, null, type, this.listenerTree, 0), leaf, i;\n      if (!leafs) return this;\n      for (i = 0; i < leafs.length; i++) {\n        leaf = leafs[i];\n        leaf._listeners = null;\n      }\n      this.listenerTree && recursivelyGarbageCollect(this.listenerTree);\n    } else if (this._events) {\n      this._events[type] = null;\n    }\n    return this;\n  };\n\n  EventEmitter.prototype.listeners = function (type) {\n    var _events = this._events;\n    var keys, listeners, allListeners;\n    var i;\n    var listenerTree;\n\n    if (type === undefined) {\n      if (this.wildcard) {\n        throw Error('event name required for wildcard emitter');\n      }\n\n      if (!_events) {\n        return [];\n      }\n\n      keys = ownKeys(_events);\n      i = keys.length;\n      allListeners = [];\n      while (i-- > 0) {\n        listeners = _events[keys[i]];\n        if (typeof listeners === 'function') {\n          allListeners.push(listeners);\n        } else {\n          allListeners.push.apply(allListeners, listeners);\n        }\n      }\n      return allListeners;\n    } else {\n      if (this.wildcard) {\n        listenerTree= this.listenerTree;\n        if(!listenerTree) return [];\n        var handlers = [];\n        var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n        searchListenerTree.call(this, handlers, ns, listenerTree, 0);\n        return handlers;\n      }\n\n      if (!_events) {\n        return [];\n      }\n\n      listeners = _events[type];\n\n      if (!listeners) {\n        return [];\n      }\n      return typeof listeners === 'function' ? [listeners] : listeners;\n    }\n  };\n\n  EventEmitter.prototype.eventNames = function(nsAsArray){\n    var _events= this._events;\n    return this.wildcard? collectTreeEvents.call(this, this.listenerTree, [], null, nsAsArray) : (_events? ownKeys(_events) : []);\n  };\n\n  EventEmitter.prototype.listenerCount = function(type) {\n    return this.listeners(type).length;\n  };\n\n  EventEmitter.prototype.hasListeners = function (type) {\n    if (this.wildcard) {\n      var handlers = [];\n      var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n      searchListenerTree.call(this, handlers, ns, this.listenerTree, 0);\n      return handlers.length > 0;\n    }\n\n    var _events = this._events;\n    var _all = this._all;\n\n    return !!(_all && _all.length || _events && (type === undefined ? ownKeys(_events).length : _events[type]));\n  };\n\n  EventEmitter.prototype.listenersAny = function() {\n\n    if(this._all) {\n      return this._all;\n    }\n    else {\n      return [];\n    }\n\n  };\n\n  EventEmitter.prototype.waitFor = function (event, options) {\n    var self = this;\n    var type = typeof options;\n    if (type === 'number') {\n      options = {timeout: options};\n    } else if (type === 'function') {\n      options = {filter: options};\n    }\n\n    options= resolveOptions(options, {\n      timeout: 0,\n      filter: undefined,\n      handleError: false,\n      Promise: Promise,\n      overload: false\n    }, {\n      filter: functionReducer,\n      Promise: constructorReducer\n    });\n\n    return makeCancelablePromise(options.Promise, function (resolve, reject, onCancel) {\n      function listener() {\n        var filter= options.filter;\n        if (filter && !filter.apply(self, arguments)) {\n          return;\n        }\n        self.off(event, listener);\n        if (options.handleError) {\n          var err = arguments[0];\n          err ? reject(err) : resolve(toArray.apply(null, arguments).slice(1));\n        } else {\n          resolve(toArray.apply(null, arguments));\n        }\n      }\n\n      onCancel(function(){\n        self.off(event, listener);\n      });\n\n      self._on(event, listener, false);\n    }, {\n      timeout: options.timeout,\n      overload: options.overload\n    })\n  };\n\n  function once(emitter, name, options) {\n    options= resolveOptions(options, {\n      Promise: Promise,\n      timeout: 0,\n      overload: false\n    }, {\n      Promise: constructorReducer\n    });\n\n    var _Promise= options.Promise;\n\n    return makeCancelablePromise(_Promise, function(resolve, reject, onCancel){\n      var handler;\n      if (typeof emitter.addEventListener === 'function') {\n        handler=  function () {\n          resolve(toArray.apply(null, arguments));\n        };\n\n        onCancel(function(){\n          emitter.removeEventListener(name, handler);\n        });\n\n        emitter.addEventListener(\n            name,\n            handler,\n            {once: true}\n        );\n        return;\n      }\n\n      var eventListener = function(){\n        errorListener && emitter.removeListener('error', errorListener);\n        resolve(toArray.apply(null, arguments));\n      };\n\n      var errorListener;\n\n      if (name !== 'error') {\n        errorListener = function (err){\n          emitter.removeListener(name, eventListener);\n          reject(err);\n        };\n\n        emitter.once('error', errorListener);\n      }\n\n      onCancel(function(){\n        errorListener && emitter.removeListener('error', errorListener);\n        emitter.removeListener(name, eventListener);\n      });\n\n      emitter.once(name, eventListener);\n    }, {\n      timeout: options.timeout,\n      overload: options.overload\n    });\n  }\n\n  var prototype= EventEmitter.prototype;\n\n  Object.defineProperties(EventEmitter, {\n    defaultMaxListeners: {\n      get: function () {\n        return prototype._maxListeners;\n      },\n      set: function (n) {\n        if (typeof n !== 'number' || n < 0 || Number.isNaN(n)) {\n          throw TypeError('n must be a non-negative number')\n        }\n        prototype._maxListeners = n;\n      },\n      enumerable: true\n    },\n    once: {\n      value: once,\n      writable: true,\n      configurable: true\n    }\n  });\n\n  Object.defineProperties(prototype, {\n      _maxListeners: {\n          value: defaultMaxListeners,\n          writable: true,\n          configurable: true\n      },\n      _observers: {value: null, writable: true, configurable: true}\n  });\n\n  if (true) {\n     // AMD. Register as an anonymous module.\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n      return EventEmitter;\n    }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else { var _global; }\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eventemitter2/lib/eventemitter2.js\n");

/***/ })

};
;