"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reownSvg: () => (/* binding */ reownSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst reownSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"60\" height=\"16\" viewBox=\"0 0 60 16\" fill=\"none\"\">\n  <path d=\"M9.3335 4.66667C9.3335 2.08934 11.4229 0 14.0002 0H20.6669C23.2442 0 25.3335 2.08934 25.3335 4.66667V11.3333C25.3335 13.9106 23.2442 16 20.6669 16H14.0002C11.4229 16 9.3335 13.9106 9.3335 11.3333V4.66667Z\" fill=\"#363636\"/>\n  <path d=\"M15.6055 11.0003L17.9448 4.66699H18.6316L16.2923 11.0003H15.6055Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M0 4.33333C0 1.9401 1.9401 0 4.33333 0C6.72657 0 8.66669 1.9401 8.66669 4.33333V11.6667C8.66669 14.0599 6.72657 16 4.33333 16C1.9401 16 0 14.0599 0 11.6667V4.33333Z\" fill=\"#363636\"/>\n  <path d=\"M3.9165 9.99934V9.16602H4.74983V9.99934H3.9165Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M26 8C26 3.58172 29.3517 0 33.4863 0H52.5137C56.6483 0 60 3.58172 60 8C60 12.4183 56.6483 16 52.5137 16H33.4863C29.3517 16 26 12.4183 26 8Z\" fill=\"#363636\"/>\n  <path d=\"M49.3687 9.95834V6.26232H50.0213V6.81966C50.256 6.40899 50.7326 6.16699 51.2606 6.16699C52.0599 6.16699 52.6173 6.67299 52.6173 7.65566V9.95834H51.972V7.69234C51.972 7.04696 51.6053 6.70966 51.07 6.70966C50.4906 6.70966 50.0213 7.17168 50.0213 7.82433V9.95834H49.3687Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M45.2538 9.95773L44.5718 6.26172H45.1877L45.6717 9.31242L46.3098 7.30306H46.9184L47.5491 9.29041L48.0404 6.26172H48.6564L47.9744 9.95773H47.2411L46.6178 8.03641L45.9871 9.95773H45.2538Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M42.3709 10.0536C41.2489 10.0536 40.5889 9.21765 40.5889 8.1103C40.5889 7.01035 41.2489 6.16699 42.3709 6.16699C43.4929 6.16699 44.1529 7.01035 44.1529 8.1103C44.1529 9.21765 43.4929 10.0536 42.3709 10.0536ZM42.3709 9.51096C43.1775 9.51096 43.4856 8.82164 43.4856 8.10296C43.4856 7.39163 43.1775 6.70966 42.3709 6.70966C41.5642 6.70966 41.2562 7.39163 41.2562 8.10296C41.2562 8.82164 41.5642 9.51096 42.3709 9.51096Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M38.2805 10.0536C37.1952 10.0536 36.5132 9.22499 36.5132 8.1103C36.5132 7.00302 37.1952 6.16699 38.2805 6.16699C39.1972 6.16699 40.0038 6.68766 39.9159 8.27896H37.1805C37.2319 8.96103 37.5472 9.5183 38.2805 9.5183C38.7718 9.5183 39.0945 9.21765 39.2045 8.87299H39.8499C39.7472 9.48903 39.1679 10.0536 38.2805 10.0536ZM37.1952 7.78765H39.2852C39.2338 7.04696 38.8892 6.70232 38.2805 6.70232C37.6132 6.70232 37.2832 7.18635 37.1952 7.78765Z\" fill=\"#F6F6F6\"/>\n  <path d=\"M33.3828 9.95773V6.26172H34.0501V6.88506C34.2848 6.47439 34.6882 6.26172 35.1061 6.26172H35.9935V6.88506H35.0548C34.4682 6.88506 34.0501 7.26638 34.0501 8.00706V9.95773H33.3828Z\" fill=\"#F6F6F6\"/>\n</svg>`;\n//# sourceMappingURL=reown-logo.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js\n"));

/***/ })

}]);