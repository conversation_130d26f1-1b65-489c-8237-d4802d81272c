"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_viem__esm_utils_ccip_js"],{

/***/ "(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/ccip.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OffchainLookupError: () => (/* binding */ OffchainLookupError),\n/* harmony export */   OffchainLookupResponseMalformedError: () => (/* binding */ OffchainLookupResponseMalformedError),\n/* harmony export */   OffchainLookupSenderMismatchError: () => (/* binding */ OffchainLookupSenderMismatchError)\n/* harmony export */ });\n/* harmony import */ var _utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/stringify.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/utils.js\");\n\n\n\nclass OffchainLookupError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ callbackSelector, cause, data, extraData, sender, urls, }) {\n        super(cause.shortMessage ||\n            'An error occurred while fetching for an offchain result.', {\n            cause,\n            metaMessages: [\n                ...(cause.metaMessages || []),\n                cause.metaMessages?.length ? '' : [],\n                'Offchain Gateway Call:',\n                urls && [\n                    '  Gateway URL(s):',\n                    ...urls.map((url) => `    ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`),\n                ],\n                `  Sender: ${sender}`,\n                `  Data: ${data}`,\n                `  Callback selector: ${callbackSelector}`,\n                `  Extra data: ${extraData}`,\n            ].flat(),\n            name: 'OffchainLookupError',\n        });\n    }\n}\nclass OffchainLookupResponseMalformedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ result, url }) {\n        super('Offchain gateway response is malformed. Response data must be a hex value.', {\n            metaMessages: [\n                `Gateway URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                `Response: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(result)}`,\n            ],\n            name: 'OffchainLookupResponseMalformedError',\n        });\n    }\n}\nclass OffchainLookupSenderMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ sender, to }) {\n        super('Reverted sender address does not match target contract address (`to`).', {\n            metaMessages: [\n                `Contract address: ${to}`,\n                `OffchainLookup sender address: ${sender}`,\n            ],\n            name: 'OffchainLookupSenderMismatchError',\n        });\n    }\n}\n//# sourceMappingURL=ccip.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeFunctionData.js":
/*!****************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/abi/decodeFunctionData.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeFunctionData: () => (/* binding */ decodeFunctionData)\n/* harmony export */ });\n/* harmony import */ var _errors_abi_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../errors/abi.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/abi.js\");\n/* harmony import */ var _data_slice_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/slice.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/slice.js\");\n/* harmony import */ var _hash_toFunctionSelector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hash/toFunctionSelector.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/hash/toFunctionSelector.js\");\n/* harmony import */ var _decodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./decodeAbiParameters.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeAbiParameters.js\");\n/* harmony import */ var _formatAbiItem_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatAbiItem.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/formatAbiItem.js\");\n\n\n\n\n\nfunction decodeFunctionData(parameters) {\n    const { abi, data } = parameters;\n    const signature = (0,_data_slice_js__WEBPACK_IMPORTED_MODULE_0__.slice)(data, 0, 4);\n    const description = abi.find((x) => x.type === 'function' &&\n        signature === (0,_hash_toFunctionSelector_js__WEBPACK_IMPORTED_MODULE_1__.toFunctionSelector)((0,_formatAbiItem_js__WEBPACK_IMPORTED_MODULE_2__.formatAbiItem)(x)));\n    if (!description)\n        throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_3__.AbiFunctionSignatureNotFoundError(signature, {\n            docsPath: '/docs/contract/decodeFunctionData',\n        });\n    return {\n        functionName: description.name,\n        args: ('inputs' in description &&\n            description.inputs &&\n            description.inputs.length > 0\n            ? (0,_decodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_4__.decodeAbiParameters)(description.inputs, (0,_data_slice_js__WEBPACK_IMPORTED_MODULE_0__.slice)(data, 4))\n            : undefined),\n    };\n}\n//# sourceMappingURL=decodeFunctionData.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeFunctionData.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeErrorResult.js":
/*!***************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/abi/encodeErrorResult.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeErrorResult: () => (/* binding */ encodeErrorResult)\n/* harmony export */ });\n/* harmony import */ var _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/abi.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/abi.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data/concat.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _hash_toFunctionSelector_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hash/toFunctionSelector.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/hash/toFunctionSelector.js\");\n/* harmony import */ var _encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./encodeAbiParameters.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _formatAbiItem_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatAbiItem.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/formatAbiItem.js\");\n/* harmony import */ var _getAbiItem_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAbiItem.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/getAbiItem.js\");\n\n\n\n\n\n\nconst docsPath = '/docs/contract/encodeErrorResult';\nfunction encodeErrorResult(parameters) {\n    const { abi, errorName, args } = parameters;\n    let abiItem = abi[0];\n    if (errorName) {\n        const item = (0,_getAbiItem_js__WEBPACK_IMPORTED_MODULE_0__.getAbiItem)({ abi, args, name: errorName });\n        if (!item)\n            throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.AbiErrorNotFoundError(errorName, { docsPath });\n        abiItem = item;\n    }\n    if (abiItem.type !== 'error')\n        throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.AbiErrorNotFoundError(undefined, { docsPath });\n    const definition = (0,_formatAbiItem_js__WEBPACK_IMPORTED_MODULE_2__.formatAbiItem)(abiItem);\n    const signature = (0,_hash_toFunctionSelector_js__WEBPACK_IMPORTED_MODULE_3__.toFunctionSelector)(definition);\n    let data = '0x';\n    if (args && args.length > 0) {\n        if (!abiItem.inputs)\n            throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.AbiErrorInputsNotFoundError(abiItem.name, { docsPath });\n        data = (0,_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_4__.encodeAbiParameters)(abiItem.inputs, args);\n    }\n    return (0,_data_concat_js__WEBPACK_IMPORTED_MODULE_5__.concatHex)([signature, data]);\n}\n//# sourceMappingURL=encodeErrorResult.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeErrorResult.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeFunctionResult.js":
/*!******************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/abi/encodeFunctionResult.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeFunctionResult: () => (/* binding */ encodeFunctionResult)\n/* harmony export */ });\n/* harmony import */ var _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/abi.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/abi.js\");\n/* harmony import */ var _encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./encodeAbiParameters.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _getAbiItem_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAbiItem.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/getAbiItem.js\");\n\n\n\nconst docsPath = '/docs/contract/encodeFunctionResult';\nfunction encodeFunctionResult(parameters) {\n    const { abi, functionName, result } = parameters;\n    let abiItem = abi[0];\n    if (functionName) {\n        const item = (0,_getAbiItem_js__WEBPACK_IMPORTED_MODULE_0__.getAbiItem)({ abi, name: functionName });\n        if (!item)\n            throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.AbiFunctionNotFoundError(functionName, { docsPath });\n        abiItem = item;\n    }\n    if (abiItem.type !== 'function')\n        throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.AbiFunctionNotFoundError(undefined, { docsPath });\n    if (!abiItem.outputs)\n        throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.AbiFunctionOutputsNotFoundError(abiItem.name, { docsPath });\n    const values = (() => {\n        if (abiItem.outputs.length === 0)\n            return [];\n        if (abiItem.outputs.length === 1)\n            return [result];\n        if (Array.isArray(result))\n            return result;\n        throw new _errors_abi_js__WEBPACK_IMPORTED_MODULE_1__.InvalidArrayError(result);\n    })();\n    return (0,_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_2__.encodeAbiParameters)(abiItem.outputs, values);\n}\n//# sourceMappingURL=encodeFunctionResult.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeFunctionResult.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/ccip.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/utils/ccip.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ccipRequest: () => (/* binding */ ccipRequest),\n/* harmony export */   offchainLookup: () => (/* binding */ offchainLookup),\n/* harmony export */   offchainLookupAbiItem: () => (/* binding */ offchainLookupAbiItem),\n/* harmony export */   offchainLookupSignature: () => (/* binding */ offchainLookupSignature)\n/* harmony export */ });\n/* harmony import */ var _actions_public_call_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../actions/public/call.js */ \"(app-pages-browser)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors/ccip.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js\");\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abi/decodeErrorResult.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeErrorResult.js\");\n/* harmony import */ var _abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abi/encodeAbiParameters.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./address/isAddressEqual.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/isAddressEqual.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./data/concat.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./data/isHex.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ens/localBatchGatewayRequest.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/ens/localBatchGatewayRequest.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\n\n\n\nconst offchainLookupSignature = '0x556f1830';\nconst offchainLookupAbiItem = {\n    name: 'OffchainLookup',\n    type: 'error',\n    inputs: [\n        {\n            name: 'sender',\n            type: 'address',\n        },\n        {\n            name: 'urls',\n            type: 'string[]',\n        },\n        {\n            name: 'callData',\n            type: 'bytes',\n        },\n        {\n            name: 'callbackFunction',\n            type: 'bytes4',\n        },\n        {\n            name: 'extraData',\n            type: 'bytes',\n        },\n    ],\n};\nasync function offchainLookup(client, { blockNumber, blockTag, data, to, }) {\n    const { args } = (0,_abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__.decodeErrorResult)({\n        data,\n        abi: [offchainLookupAbiItem],\n    });\n    const [sender, urls, callData, callbackSelector, extraData] = args;\n    const { ccipRead } = client;\n    const ccipRequest_ = ccipRead && typeof ccipRead?.request === 'function'\n        ? ccipRead.request\n        : ccipRequest;\n    try {\n        if (!(0,_address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__.isAddressEqual)(to, sender))\n            throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupSenderMismatchError({ sender, to });\n        const result = urls.includes(_ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__.localBatchGatewayUrl)\n            ? await (0,_ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__.localBatchGatewayRequest)({\n                data: callData,\n                ccipRequest: ccipRequest_,\n            })\n            : await ccipRequest_({ data: callData, sender, urls });\n        const { data: data_ } = await (0,_actions_public_call_js__WEBPACK_IMPORTED_MODULE_4__.call)(client, {\n            blockNumber,\n            blockTag,\n            data: (0,_data_concat_js__WEBPACK_IMPORTED_MODULE_5__.concat)([\n                callbackSelector,\n                (0,_abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_6__.encodeAbiParameters)([{ type: 'bytes' }, { type: 'bytes' }], [result, extraData]),\n            ]),\n            to,\n        });\n        return data_;\n    }\n    catch (err) {\n        throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupError({\n            callbackSelector,\n            cause: err,\n            data,\n            extraData,\n            sender,\n            urls,\n        });\n    }\n}\nasync function ccipRequest({ data, sender, urls, }) {\n    let error = new Error('An unknown error occurred.');\n    for (let i = 0; i < urls.length; i++) {\n        const url = urls[i];\n        const method = url.includes('{data}') ? 'GET' : 'POST';\n        const body = method === 'POST' ? { data, sender } : undefined;\n        const headers = method === 'POST' ? { 'Content-Type': 'application/json' } : {};\n        try {\n            const response = await fetch(url.replace('{sender}', sender.toLowerCase()).replace('{data}', data), {\n                body: JSON.stringify(body),\n                headers,\n                method,\n            });\n            let result;\n            if (response.headers.get('Content-Type')?.startsWith('application/json')) {\n                result = (await response.json()).data;\n            }\n            else {\n                result = (await response.text());\n            }\n            if (!response.ok) {\n                error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_7__.HttpRequestError({\n                    body,\n                    details: result?.error\n                        ? (0,_stringify_js__WEBPACK_IMPORTED_MODULE_8__.stringify)(result.error)\n                        : response.statusText,\n                    headers: response.headers,\n                    status: response.status,\n                    url,\n                });\n                continue;\n            }\n            if (!(0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_9__.isHex)(result)) {\n                error = new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupResponseMalformedError({\n                    result,\n                    url,\n                });\n                continue;\n            }\n            return result;\n        }\n        catch (err) {\n            error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_7__.HttpRequestError({\n                body,\n                details: err.message,\n                url,\n            });\n        }\n    }\n    throw error;\n}\n//# sourceMappingURL=ccip.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/ccip.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/ens/localBatchGatewayRequest.js":
/*!**********************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/ens/localBatchGatewayRequest.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localBatchGatewayRequest: () => (/* binding */ localBatchGatewayRequest),\n/* harmony export */   localBatchGatewayUrl: () => (/* binding */ localBatchGatewayUrl)\n/* harmony export */ });\n/* harmony import */ var _constants_abis_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../constants/abis.js */ \"(app-pages-browser)/./node_modules/viem/_esm/constants/abis.js\");\n/* harmony import */ var _constants_solidity_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../constants/solidity.js */ \"(app-pages-browser)/./node_modules/viem/_esm/constants/solidity.js\");\n/* harmony import */ var _abi_decodeFunctionData_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../abi/decodeFunctionData.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeFunctionData.js\");\n/* harmony import */ var _abi_encodeErrorResult_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../abi/encodeErrorResult.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeErrorResult.js\");\n/* harmony import */ var _abi_encodeFunctionResult_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../abi/encodeFunctionResult.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeFunctionResult.js\");\n\n\n\n\n\nconst localBatchGatewayUrl = 'x-batch-gateway:true';\nasync function localBatchGatewayRequest(parameters) {\n    const { data, ccipRequest } = parameters;\n    const { args: [queries], } = (0,_abi_decodeFunctionData_js__WEBPACK_IMPORTED_MODULE_0__.decodeFunctionData)({ abi: _constants_abis_js__WEBPACK_IMPORTED_MODULE_1__.batchGatewayAbi, data });\n    const failures = [];\n    const responses = [];\n    await Promise.all(queries.map(async (query, i) => {\n        try {\n            responses[i] = await ccipRequest(query);\n            failures[i] = false;\n        }\n        catch (err) {\n            failures[i] = true;\n            responses[i] = encodeError(err);\n        }\n    }));\n    return (0,_abi_encodeFunctionResult_js__WEBPACK_IMPORTED_MODULE_2__.encodeFunctionResult)({\n        abi: _constants_abis_js__WEBPACK_IMPORTED_MODULE_1__.batchGatewayAbi,\n        functionName: 'query',\n        result: [failures, responses],\n    });\n}\nfunction encodeError(error) {\n    if (error.name === 'HttpRequestError' && error.status)\n        return (0,_abi_encodeErrorResult_js__WEBPACK_IMPORTED_MODULE_3__.encodeErrorResult)({\n            abi: _constants_abis_js__WEBPACK_IMPORTED_MODULE_1__.batchGatewayAbi,\n            errorName: 'HttpError',\n            args: [error.status, error.shortMessage],\n        });\n    return (0,_abi_encodeErrorResult_js__WEBPACK_IMPORTED_MODULE_3__.encodeErrorResult)({\n        abi: [_constants_solidity_js__WEBPACK_IMPORTED_MODULE_4__.solidityError],\n        errorName: 'Error',\n        args: ['shortMessage' in error ? error.shortMessage : error.message],\n    });\n}\n//# sourceMappingURL=localBatchGatewayRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/ens/localBatchGatewayRequest.js\n"));

/***/ })

}]);