/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lru-cache";
exports.ids = ["vendor-chunks/lru-cache"];
exports.modules = {

/***/ "(rsc)/./node_modules/lru-cache/index.js":
/*!*****************************************!*\
  !*** ./node_modules/lru-cache/index.js ***!
  \*****************************************/
/***/ ((module) => {

eval("const perf =\n  typeof performance === 'object' &&\n  performance &&\n  typeof performance.now === 'function'\n    ? performance\n    : Date\n\nconst hasAbortController = typeof AbortController === 'function'\n\n// minimal backwards-compatibility polyfill\n// this doesn't have nearly all the checks and whatnot that\n// actual AbortController/Signal has, but it's enough for\n// our purposes, and if used properly, behaves the same.\nconst AC = hasAbortController\n  ? AbortController\n  : class AbortController {\n      constructor() {\n        this.signal = new AS()\n      }\n      abort(reason = new Error('This operation was aborted')) {\n        this.signal.reason = this.signal.reason || reason\n        this.signal.aborted = true\n        this.signal.dispatchEvent({\n          type: 'abort',\n          target: this.signal,\n        })\n      }\n    }\n\nconst hasAbortSignal = typeof AbortSignal === 'function'\n// Some polyfills put this on the AC class, not global\nconst hasACAbortSignal = typeof AC.AbortSignal === 'function'\nconst AS = hasAbortSignal\n  ? AbortSignal\n  : hasACAbortSignal\n  ? AC.AbortController\n  : class AbortSignal {\n      constructor() {\n        this.reason = undefined\n        this.aborted = false\n        this._listeners = []\n      }\n      dispatchEvent(e) {\n        if (e.type === 'abort') {\n          this.aborted = true\n          this.onabort(e)\n          this._listeners.forEach(f => f(e), this)\n        }\n      }\n      onabort() {}\n      addEventListener(ev, fn) {\n        if (ev === 'abort') {\n          this._listeners.push(fn)\n        }\n      }\n      removeEventListener(ev, fn) {\n        if (ev === 'abort') {\n          this._listeners = this._listeners.filter(f => f !== fn)\n        }\n      }\n    }\n\nconst warned = new Set()\nconst deprecatedOption = (opt, instead) => {\n  const code = `LRU_CACHE_OPTION_${opt}`\n  if (shouldWarn(code)) {\n    warn(code, `${opt} option`, `options.${instead}`, LRUCache)\n  }\n}\nconst deprecatedMethod = (method, instead) => {\n  const code = `LRU_CACHE_METHOD_${method}`\n  if (shouldWarn(code)) {\n    const { prototype } = LRUCache\n    const { get } = Object.getOwnPropertyDescriptor(prototype, method)\n    warn(code, `${method} method`, `cache.${instead}()`, get)\n  }\n}\nconst deprecatedProperty = (field, instead) => {\n  const code = `LRU_CACHE_PROPERTY_${field}`\n  if (shouldWarn(code)) {\n    const { prototype } = LRUCache\n    const { get } = Object.getOwnPropertyDescriptor(prototype, field)\n    warn(code, `${field} property`, `cache.${instead}`, get)\n  }\n}\n\nconst emitWarning = (...a) => {\n  typeof process === 'object' &&\n  process &&\n  typeof process.emitWarning === 'function'\n    ? process.emitWarning(...a)\n    : console.error(...a)\n}\n\nconst shouldWarn = code => !warned.has(code)\n\nconst warn = (code, what, instead, fn) => {\n  warned.add(code)\n  const msg = `The ${what} is deprecated. Please use ${instead} instead.`\n  emitWarning(msg, 'DeprecationWarning', code, fn)\n}\n\nconst isPosInt = n => n && n === Math.floor(n) && n > 0 && isFinite(n)\n\n/* istanbul ignore next - This is a little bit ridiculous, tbh.\n * The maximum array length is 2^32-1 or thereabouts on most JS impls.\n * And well before that point, you're caching the entire world, I mean,\n * that's ~32GB of just integers for the next/prev links, plus whatever\n * else to hold that many keys and values.  Just filling the memory with\n * zeroes at init time is brutal when you get that big.\n * But why not be complete?\n * Maybe in the future, these limits will have expanded. */\nconst getUintArray = max =>\n  !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n    ? Uint8Array\n    : max <= Math.pow(2, 16)\n    ? Uint16Array\n    : max <= Math.pow(2, 32)\n    ? Uint32Array\n    : max <= Number.MAX_SAFE_INTEGER\n    ? ZeroArray\n    : null\n\nclass ZeroArray extends Array {\n  constructor(size) {\n    super(size)\n    this.fill(0)\n  }\n}\n\nclass Stack {\n  constructor(max) {\n    if (max === 0) {\n      return []\n    }\n    const UintArray = getUintArray(max)\n    this.heap = new UintArray(max)\n    this.length = 0\n  }\n  push(n) {\n    this.heap[this.length++] = n\n  }\n  pop() {\n    return this.heap[--this.length]\n  }\n}\n\nclass LRUCache {\n  constructor(options = {}) {\n    const {\n      max = 0,\n      ttl,\n      ttlResolution = 1,\n      ttlAutopurge,\n      updateAgeOnGet,\n      updateAgeOnHas,\n      allowStale,\n      dispose,\n      disposeAfter,\n      noDisposeOnSet,\n      noUpdateTTL,\n      maxSize = 0,\n      maxEntrySize = 0,\n      sizeCalculation,\n      fetchMethod,\n      fetchContext,\n      noDeleteOnFetchRejection,\n      noDeleteOnStaleGet,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n    } = options\n\n    // deprecated options, don't trigger a warning for getting them if\n    // the thing being passed in is another LRUCache we're copying.\n    const { length, maxAge, stale } =\n      options instanceof LRUCache ? {} : options\n\n    if (max !== 0 && !isPosInt(max)) {\n      throw new TypeError('max option must be a nonnegative integer')\n    }\n\n    const UintArray = max ? getUintArray(max) : Array\n    if (!UintArray) {\n      throw new Error('invalid max value: ' + max)\n    }\n\n    this.max = max\n    this.maxSize = maxSize\n    this.maxEntrySize = maxEntrySize || this.maxSize\n    this.sizeCalculation = sizeCalculation || length\n    if (this.sizeCalculation) {\n      if (!this.maxSize && !this.maxEntrySize) {\n        throw new TypeError(\n          'cannot set sizeCalculation without setting maxSize or maxEntrySize'\n        )\n      }\n      if (typeof this.sizeCalculation !== 'function') {\n        throw new TypeError('sizeCalculation set to non-function')\n      }\n    }\n\n    this.fetchMethod = fetchMethod || null\n    if (this.fetchMethod && typeof this.fetchMethod !== 'function') {\n      throw new TypeError(\n        'fetchMethod must be a function if specified'\n      )\n    }\n\n    this.fetchContext = fetchContext\n    if (!this.fetchMethod && fetchContext !== undefined) {\n      throw new TypeError(\n        'cannot set fetchContext without fetchMethod'\n      )\n    }\n\n    this.keyMap = new Map()\n    this.keyList = new Array(max).fill(null)\n    this.valList = new Array(max).fill(null)\n    this.next = new UintArray(max)\n    this.prev = new UintArray(max)\n    this.head = 0\n    this.tail = 0\n    this.free = new Stack(max)\n    this.initialFill = 1\n    this.size = 0\n\n    if (typeof dispose === 'function') {\n      this.dispose = dispose\n    }\n    if (typeof disposeAfter === 'function') {\n      this.disposeAfter = disposeAfter\n      this.disposed = []\n    } else {\n      this.disposeAfter = null\n      this.disposed = null\n    }\n    this.noDisposeOnSet = !!noDisposeOnSet\n    this.noUpdateTTL = !!noUpdateTTL\n    this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection\n    this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection\n    this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort\n    this.ignoreFetchAbort = !!ignoreFetchAbort\n\n    // NB: maxEntrySize is set to maxSize if it's set\n    if (this.maxEntrySize !== 0) {\n      if (this.maxSize !== 0) {\n        if (!isPosInt(this.maxSize)) {\n          throw new TypeError(\n            'maxSize must be a positive integer if specified'\n          )\n        }\n      }\n      if (!isPosInt(this.maxEntrySize)) {\n        throw new TypeError(\n          'maxEntrySize must be a positive integer if specified'\n        )\n      }\n      this.initializeSizeTracking()\n    }\n\n    this.allowStale = !!allowStale || !!stale\n    this.noDeleteOnStaleGet = !!noDeleteOnStaleGet\n    this.updateAgeOnGet = !!updateAgeOnGet\n    this.updateAgeOnHas = !!updateAgeOnHas\n    this.ttlResolution =\n      isPosInt(ttlResolution) || ttlResolution === 0\n        ? ttlResolution\n        : 1\n    this.ttlAutopurge = !!ttlAutopurge\n    this.ttl = ttl || maxAge || 0\n    if (this.ttl) {\n      if (!isPosInt(this.ttl)) {\n        throw new TypeError(\n          'ttl must be a positive integer if specified'\n        )\n      }\n      this.initializeTTLTracking()\n    }\n\n    // do not allow completely unbounded caches\n    if (this.max === 0 && this.ttl === 0 && this.maxSize === 0) {\n      throw new TypeError(\n        'At least one of max, maxSize, or ttl is required'\n      )\n    }\n    if (!this.ttlAutopurge && !this.max && !this.maxSize) {\n      const code = 'LRU_CACHE_UNBOUNDED'\n      if (shouldWarn(code)) {\n        warned.add(code)\n        const msg =\n          'TTL caching without ttlAutopurge, max, or maxSize can ' +\n          'result in unbounded memory consumption.'\n        emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache)\n      }\n    }\n\n    if (stale) {\n      deprecatedOption('stale', 'allowStale')\n    }\n    if (maxAge) {\n      deprecatedOption('maxAge', 'ttl')\n    }\n    if (length) {\n      deprecatedOption('length', 'sizeCalculation')\n    }\n  }\n\n  getRemainingTTL(key) {\n    return this.has(key, { updateAgeOnHas: false }) ? Infinity : 0\n  }\n\n  initializeTTLTracking() {\n    this.ttls = new ZeroArray(this.max)\n    this.starts = new ZeroArray(this.max)\n\n    this.setItemTTL = (index, ttl, start = perf.now()) => {\n      this.starts[index] = ttl !== 0 ? start : 0\n      this.ttls[index] = ttl\n      if (ttl !== 0 && this.ttlAutopurge) {\n        const t = setTimeout(() => {\n          if (this.isStale(index)) {\n            this.delete(this.keyList[index])\n          }\n        }, ttl + 1)\n        /* istanbul ignore else - unref() not supported on all platforms */\n        if (t.unref) {\n          t.unref()\n        }\n      }\n    }\n\n    this.updateItemAge = index => {\n      this.starts[index] = this.ttls[index] !== 0 ? perf.now() : 0\n    }\n\n    this.statusTTL = (status, index) => {\n      if (status) {\n        status.ttl = this.ttls[index]\n        status.start = this.starts[index]\n        status.now = cachedNow || getNow()\n        status.remainingTTL = status.now + status.ttl - status.start\n      }\n    }\n\n    // debounce calls to perf.now() to 1s so we're not hitting\n    // that costly call repeatedly.\n    let cachedNow = 0\n    const getNow = () => {\n      const n = perf.now()\n      if (this.ttlResolution > 0) {\n        cachedNow = n\n        const t = setTimeout(\n          () => (cachedNow = 0),\n          this.ttlResolution\n        )\n        /* istanbul ignore else - not available on all platforms */\n        if (t.unref) {\n          t.unref()\n        }\n      }\n      return n\n    }\n\n    this.getRemainingTTL = key => {\n      const index = this.keyMap.get(key)\n      if (index === undefined) {\n        return 0\n      }\n      return this.ttls[index] === 0 || this.starts[index] === 0\n        ? Infinity\n        : this.starts[index] +\n            this.ttls[index] -\n            (cachedNow || getNow())\n    }\n\n    this.isStale = index => {\n      return (\n        this.ttls[index] !== 0 &&\n        this.starts[index] !== 0 &&\n        (cachedNow || getNow()) - this.starts[index] >\n          this.ttls[index]\n      )\n    }\n  }\n  updateItemAge(_index) {}\n  statusTTL(_status, _index) {}\n  setItemTTL(_index, _ttl, _start) {}\n  isStale(_index) {\n    return false\n  }\n\n  initializeSizeTracking() {\n    this.calculatedSize = 0\n    this.sizes = new ZeroArray(this.max)\n    this.removeItemSize = index => {\n      this.calculatedSize -= this.sizes[index]\n      this.sizes[index] = 0\n    }\n    this.requireSize = (k, v, size, sizeCalculation) => {\n      // provisionally accept background fetches.\n      // actual value size will be checked when they return.\n      if (this.isBackgroundFetch(v)) {\n        return 0\n      }\n      if (!isPosInt(size)) {\n        if (sizeCalculation) {\n          if (typeof sizeCalculation !== 'function') {\n            throw new TypeError('sizeCalculation must be a function')\n          }\n          size = sizeCalculation(v, k)\n          if (!isPosInt(size)) {\n            throw new TypeError(\n              'sizeCalculation return invalid (expect positive integer)'\n            )\n          }\n        } else {\n          throw new TypeError(\n            'invalid size value (must be positive integer). ' +\n              'When maxSize or maxEntrySize is used, sizeCalculation or size ' +\n              'must be set.'\n          )\n        }\n      }\n      return size\n    }\n    this.addItemSize = (index, size, status) => {\n      this.sizes[index] = size\n      if (this.maxSize) {\n        const maxSize = this.maxSize - this.sizes[index]\n        while (this.calculatedSize > maxSize) {\n          this.evict(true)\n        }\n      }\n      this.calculatedSize += this.sizes[index]\n      if (status) {\n        status.entrySize = size\n        status.totalCalculatedSize = this.calculatedSize\n      }\n    }\n  }\n  removeItemSize(_index) {}\n  addItemSize(_index, _size) {}\n  requireSize(_k, _v, size, sizeCalculation) {\n    if (size || sizeCalculation) {\n      throw new TypeError(\n        'cannot set size without setting maxSize or maxEntrySize on cache'\n      )\n    }\n  }\n\n  *indexes({ allowStale = this.allowStale } = {}) {\n    if (this.size) {\n      for (let i = this.tail; true; ) {\n        if (!this.isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.isStale(i)) {\n          yield i\n        }\n        if (i === this.head) {\n          break\n        } else {\n          i = this.prev[i]\n        }\n      }\n    }\n  }\n\n  *rindexes({ allowStale = this.allowStale } = {}) {\n    if (this.size) {\n      for (let i = this.head; true; ) {\n        if (!this.isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.isStale(i)) {\n          yield i\n        }\n        if (i === this.tail) {\n          break\n        } else {\n          i = this.next[i]\n        }\n      }\n    }\n  }\n\n  isValidIndex(index) {\n    return (\n      index !== undefined &&\n      this.keyMap.get(this.keyList[index]) === index\n    )\n  }\n\n  *entries() {\n    for (const i of this.indexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield [this.keyList[i], this.valList[i]]\n      }\n    }\n  }\n  *rentries() {\n    for (const i of this.rindexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield [this.keyList[i], this.valList[i]]\n      }\n    }\n  }\n\n  *keys() {\n    for (const i of this.indexes()) {\n      if (\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.keyList[i]\n      }\n    }\n  }\n  *rkeys() {\n    for (const i of this.rindexes()) {\n      if (\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.keyList[i]\n      }\n    }\n  }\n\n  *values() {\n    for (const i of this.indexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.valList[i]\n      }\n    }\n  }\n  *rvalues() {\n    for (const i of this.rindexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.valList[i]\n      }\n    }\n  }\n\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  find(fn, getOptions) {\n    for (const i of this.indexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      if (fn(value, this.keyList[i], this)) {\n        return this.get(this.keyList[i], getOptions)\n      }\n    }\n  }\n\n  forEach(fn, thisp = this) {\n    for (const i of this.indexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.keyList[i], this)\n    }\n  }\n\n  rforEach(fn, thisp = this) {\n    for (const i of this.rindexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.keyList[i], this)\n    }\n  }\n\n  get prune() {\n    deprecatedMethod('prune', 'purgeStale')\n    return this.purgeStale\n  }\n\n  purgeStale() {\n    let deleted = false\n    for (const i of this.rindexes({ allowStale: true })) {\n      if (this.isStale(i)) {\n        this.delete(this.keyList[i])\n        deleted = true\n      }\n    }\n    return deleted\n  }\n\n  dump() {\n    const arr = []\n    for (const i of this.indexes({ allowStale: true })) {\n      const key = this.keyList[i]\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      const entry = { value }\n      if (this.ttls) {\n        entry.ttl = this.ttls[i]\n        // always dump the start relative to a portable timestamp\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = perf.now() - this.starts[i]\n        entry.start = Math.floor(Date.now() - age)\n      }\n      if (this.sizes) {\n        entry.size = this.sizes[i]\n      }\n      arr.unshift([key, entry])\n    }\n    return arr\n  }\n\n  load(arr) {\n    this.clear()\n    for (const [key, entry] of arr) {\n      if (entry.start) {\n        // entry.start is a portable timestamp, but we may be using\n        // node's performance.now(), so calculate the offset.\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = Date.now() - entry.start\n        entry.start = perf.now() - age\n      }\n      this.set(key, entry.value, entry)\n    }\n  }\n\n  dispose(_v, _k, _reason) {}\n\n  set(\n    k,\n    v,\n    {\n      ttl = this.ttl,\n      start,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      status,\n    } = {}\n  ) {\n    size = this.requireSize(k, v, size, sizeCalculation)\n    // if the item doesn't fit, don't do anything\n    // NB: maxEntrySize set to maxSize by default\n    if (this.maxEntrySize && size > this.maxEntrySize) {\n      if (status) {\n        status.set = 'miss'\n        status.maxEntrySizeExceeded = true\n      }\n      // have to delete, in case a background fetch is there already.\n      // in non-async cases, this is a no-op\n      this.delete(k)\n      return this\n    }\n    let index = this.size === 0 ? undefined : this.keyMap.get(k)\n    if (index === undefined) {\n      // addition\n      index = this.newIndex()\n      this.keyList[index] = k\n      this.valList[index] = v\n      this.keyMap.set(k, index)\n      this.next[this.tail] = index\n      this.prev[index] = this.tail\n      this.tail = index\n      this.size++\n      this.addItemSize(index, size, status)\n      if (status) {\n        status.set = 'add'\n      }\n      noUpdateTTL = false\n    } else {\n      // update\n      this.moveToTail(index)\n      const oldVal = this.valList[index]\n      if (v !== oldVal) {\n        if (this.isBackgroundFetch(oldVal)) {\n          oldVal.__abortController.abort(new Error('replaced'))\n        } else {\n          if (!noDisposeOnSet) {\n            this.dispose(oldVal, k, 'set')\n            if (this.disposeAfter) {\n              this.disposed.push([oldVal, k, 'set'])\n            }\n          }\n        }\n        this.removeItemSize(index)\n        this.valList[index] = v\n        this.addItemSize(index, size, status)\n        if (status) {\n          status.set = 'replace'\n          const oldValue =\n            oldVal && this.isBackgroundFetch(oldVal)\n              ? oldVal.__staleWhileFetching\n              : oldVal\n          if (oldValue !== undefined) status.oldValue = oldValue\n        }\n      } else if (status) {\n        status.set = 'update'\n      }\n    }\n    if (ttl !== 0 && this.ttl === 0 && !this.ttls) {\n      this.initializeTTLTracking()\n    }\n    if (!noUpdateTTL) {\n      this.setItemTTL(index, ttl, start)\n    }\n    this.statusTTL(status, index)\n    if (this.disposeAfter) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n    return this\n  }\n\n  newIndex() {\n    if (this.size === 0) {\n      return this.tail\n    }\n    if (this.size === this.max && this.max !== 0) {\n      return this.evict(false)\n    }\n    if (this.free.length !== 0) {\n      return this.free.pop()\n    }\n    // initial fill, just keep writing down the list\n    return this.initialFill++\n  }\n\n  pop() {\n    if (this.size) {\n      const val = this.valList[this.head]\n      this.evict(true)\n      return val\n    }\n  }\n\n  evict(free) {\n    const head = this.head\n    const k = this.keyList[head]\n    const v = this.valList[head]\n    if (this.isBackgroundFetch(v)) {\n      v.__abortController.abort(new Error('evicted'))\n    } else {\n      this.dispose(v, k, 'evict')\n      if (this.disposeAfter) {\n        this.disposed.push([v, k, 'evict'])\n      }\n    }\n    this.removeItemSize(head)\n    // if we aren't about to use the index, then null these out\n    if (free) {\n      this.keyList[head] = null\n      this.valList[head] = null\n      this.free.push(head)\n    }\n    this.head = this.next[head]\n    this.keyMap.delete(k)\n    this.size--\n    return head\n  }\n\n  has(k, { updateAgeOnHas = this.updateAgeOnHas, status } = {}) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined) {\n      if (!this.isStale(index)) {\n        if (updateAgeOnHas) {\n          this.updateItemAge(index)\n        }\n        if (status) status.has = 'hit'\n        this.statusTTL(status, index)\n        return true\n      } else if (status) {\n        status.has = 'stale'\n        this.statusTTL(status, index)\n      }\n    } else if (status) {\n      status.has = 'miss'\n    }\n    return false\n  }\n\n  // like get(), but without any LRU updating or TTL expiration\n  peek(k, { allowStale = this.allowStale } = {}) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined && (allowStale || !this.isStale(index))) {\n      const v = this.valList[index]\n      // either stale and allowed, or forcing a refresh of non-stale value\n      return this.isBackgroundFetch(v) ? v.__staleWhileFetching : v\n    }\n  }\n\n  backgroundFetch(k, index, options, context) {\n    const v = index === undefined ? undefined : this.valList[index]\n    if (this.isBackgroundFetch(v)) {\n      return v\n    }\n    const ac = new AC()\n    if (options.signal) {\n      options.signal.addEventListener('abort', () =>\n        ac.abort(options.signal.reason)\n      )\n    }\n    const fetchOpts = {\n      signal: ac.signal,\n      options,\n      context,\n    }\n    const cb = (v, updateCache = false) => {\n      const { aborted } = ac.signal\n      const ignoreAbort = options.ignoreFetchAbort && v !== undefined\n      if (options.status) {\n        if (aborted && !updateCache) {\n          options.status.fetchAborted = true\n          options.status.fetchError = ac.signal.reason\n          if (ignoreAbort) options.status.fetchAbortIgnored = true\n        } else {\n          options.status.fetchResolved = true\n        }\n      }\n      if (aborted && !ignoreAbort && !updateCache) {\n        return fetchFail(ac.signal.reason)\n      }\n      // either we didn't abort, and are still here, or we did, and ignored\n      if (this.valList[index] === p) {\n        if (v === undefined) {\n          if (p.__staleWhileFetching) {\n            this.valList[index] = p.__staleWhileFetching\n          } else {\n            this.delete(k)\n          }\n        } else {\n          if (options.status) options.status.fetchUpdated = true\n          this.set(k, v, fetchOpts.options)\n        }\n      }\n      return v\n    }\n    const eb = er => {\n      if (options.status) {\n        options.status.fetchRejected = true\n        options.status.fetchError = er\n      }\n      return fetchFail(er)\n    }\n    const fetchFail = er => {\n      const { aborted } = ac.signal\n      const allowStaleAborted =\n        aborted && options.allowStaleOnFetchAbort\n      const allowStale =\n        allowStaleAborted || options.allowStaleOnFetchRejection\n      const noDelete = allowStale || options.noDeleteOnFetchRejection\n      if (this.valList[index] === p) {\n        // if we allow stale on fetch rejections, then we need to ensure that\n        // the stale value is not removed from the cache when the fetch fails.\n        const del = !noDelete || p.__staleWhileFetching === undefined\n        if (del) {\n          this.delete(k)\n        } else if (!allowStaleAborted) {\n          // still replace the *promise* with the stale value,\n          // since we are done with the promise at this point.\n          // leave it untouched if we're still waiting for an\n          // aborted background fetch that hasn't yet returned.\n          this.valList[index] = p.__staleWhileFetching\n        }\n      }\n      if (allowStale) {\n        if (options.status && p.__staleWhileFetching !== undefined) {\n          options.status.returnedStale = true\n        }\n        return p.__staleWhileFetching\n      } else if (p.__returned === p) {\n        throw er\n      }\n    }\n    const pcall = (res, rej) => {\n      this.fetchMethod(k, v, fetchOpts).then(v => res(v), rej)\n      // ignored, we go until we finish, regardless.\n      // defer check until we are actually aborting,\n      // so fetchMethod can override.\n      ac.signal.addEventListener('abort', () => {\n        if (\n          !options.ignoreFetchAbort ||\n          options.allowStaleOnFetchAbort\n        ) {\n          res()\n          // when it eventually resolves, update the cache.\n          if (options.allowStaleOnFetchAbort) {\n            res = v => cb(v, true)\n          }\n        }\n      })\n    }\n    if (options.status) options.status.fetchDispatched = true\n    const p = new Promise(pcall).then(cb, eb)\n    p.__abortController = ac\n    p.__staleWhileFetching = v\n    p.__returned = null\n    if (index === undefined) {\n      // internal, don't expose status.\n      this.set(k, p, { ...fetchOpts.options, status: undefined })\n      index = this.keyMap.get(k)\n    } else {\n      this.valList[index] = p\n    }\n    return p\n  }\n\n  isBackgroundFetch(p) {\n    return (\n      p &&\n      typeof p === 'object' &&\n      typeof p.then === 'function' &&\n      Object.prototype.hasOwnProperty.call(\n        p,\n        '__staleWhileFetching'\n      ) &&\n      Object.prototype.hasOwnProperty.call(p, '__returned') &&\n      (p.__returned === p || p.__returned === null)\n    )\n  }\n\n  // this takes the union of get() and set() opts, because it does both\n  async fetch(\n    k,\n    {\n      // get options\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      // set options\n      ttl = this.ttl,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      // fetch exclusive options\n      noDeleteOnFetchRejection = this.noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection = this.allowStaleOnFetchRejection,\n      ignoreFetchAbort = this.ignoreFetchAbort,\n      allowStaleOnFetchAbort = this.allowStaleOnFetchAbort,\n      fetchContext = this.fetchContext,\n      forceRefresh = false,\n      status,\n      signal,\n    } = {}\n  ) {\n    if (!this.fetchMethod) {\n      if (status) status.fetch = 'get'\n      return this.get(k, {\n        allowStale,\n        updateAgeOnGet,\n        noDeleteOnStaleGet,\n        status,\n      })\n    }\n\n    const options = {\n      allowStale,\n      updateAgeOnGet,\n      noDeleteOnStaleGet,\n      ttl,\n      noDisposeOnSet,\n      size,\n      sizeCalculation,\n      noUpdateTTL,\n      noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n      status,\n      signal,\n    }\n\n    let index = this.keyMap.get(k)\n    if (index === undefined) {\n      if (status) status.fetch = 'miss'\n      const p = this.backgroundFetch(k, index, options, fetchContext)\n      return (p.__returned = p)\n    } else {\n      // in cache, maybe already fetching\n      const v = this.valList[index]\n      if (this.isBackgroundFetch(v)) {\n        const stale =\n          allowStale && v.__staleWhileFetching !== undefined\n        if (status) {\n          status.fetch = 'inflight'\n          if (stale) status.returnedStale = true\n        }\n        return stale ? v.__staleWhileFetching : (v.__returned = v)\n      }\n\n      // if we force a refresh, that means do NOT serve the cached value,\n      // unless we are already in the process of refreshing the cache.\n      const isStale = this.isStale(index)\n      if (!forceRefresh && !isStale) {\n        if (status) status.fetch = 'hit'\n        this.moveToTail(index)\n        if (updateAgeOnGet) {\n          this.updateItemAge(index)\n        }\n        this.statusTTL(status, index)\n        return v\n      }\n\n      // ok, it is stale or a forced refresh, and not already fetching.\n      // refresh the cache.\n      const p = this.backgroundFetch(k, index, options, fetchContext)\n      const hasStale = p.__staleWhileFetching !== undefined\n      const staleVal = hasStale && allowStale\n      if (status) {\n        status.fetch = hasStale && isStale ? 'stale' : 'refresh'\n        if (staleVal && isStale) status.returnedStale = true\n      }\n      return staleVal ? p.__staleWhileFetching : (p.__returned = p)\n    }\n  }\n\n  get(\n    k,\n    {\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      status,\n    } = {}\n  ) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined) {\n      const value = this.valList[index]\n      const fetching = this.isBackgroundFetch(value)\n      this.statusTTL(status, index)\n      if (this.isStale(index)) {\n        if (status) status.get = 'stale'\n        // delete only if not an in-flight background fetch\n        if (!fetching) {\n          if (!noDeleteOnStaleGet) {\n            this.delete(k)\n          }\n          if (status) status.returnedStale = allowStale\n          return allowStale ? value : undefined\n        } else {\n          if (status) {\n            status.returnedStale =\n              allowStale && value.__staleWhileFetching !== undefined\n          }\n          return allowStale ? value.__staleWhileFetching : undefined\n        }\n      } else {\n        if (status) status.get = 'hit'\n        // if we're currently fetching it, we don't actually have it yet\n        // it's not stale, which means this isn't a staleWhileRefetching.\n        // If it's not stale, and fetching, AND has a __staleWhileFetching\n        // value, then that means the user fetched with {forceRefresh:true},\n        // so it's safe to return that value.\n        if (fetching) {\n          return value.__staleWhileFetching\n        }\n        this.moveToTail(index)\n        if (updateAgeOnGet) {\n          this.updateItemAge(index)\n        }\n        return value\n      }\n    } else if (status) {\n      status.get = 'miss'\n    }\n  }\n\n  connect(p, n) {\n    this.prev[n] = p\n    this.next[p] = n\n  }\n\n  moveToTail(index) {\n    // if tail already, nothing to do\n    // if head, move head to next[index]\n    // else\n    //   move next[prev[index]] to next[index] (head has no prev)\n    //   move prev[next[index]] to prev[index]\n    // prev[index] = tail\n    // next[tail] = index\n    // tail = index\n    if (index !== this.tail) {\n      if (index === this.head) {\n        this.head = this.next[index]\n      } else {\n        this.connect(this.prev[index], this.next[index])\n      }\n      this.connect(this.tail, index)\n      this.tail = index\n    }\n  }\n\n  get del() {\n    deprecatedMethod('del', 'delete')\n    return this.delete\n  }\n\n  delete(k) {\n    let deleted = false\n    if (this.size !== 0) {\n      const index = this.keyMap.get(k)\n      if (index !== undefined) {\n        deleted = true\n        if (this.size === 1) {\n          this.clear()\n        } else {\n          this.removeItemSize(index)\n          const v = this.valList[index]\n          if (this.isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('deleted'))\n          } else {\n            this.dispose(v, k, 'delete')\n            if (this.disposeAfter) {\n              this.disposed.push([v, k, 'delete'])\n            }\n          }\n          this.keyMap.delete(k)\n          this.keyList[index] = null\n          this.valList[index] = null\n          if (index === this.tail) {\n            this.tail = this.prev[index]\n          } else if (index === this.head) {\n            this.head = this.next[index]\n          } else {\n            this.next[this.prev[index]] = this.next[index]\n            this.prev[this.next[index]] = this.prev[index]\n          }\n          this.size--\n          this.free.push(index)\n        }\n      }\n    }\n    if (this.disposed) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n    return deleted\n  }\n\n  clear() {\n    for (const index of this.rindexes({ allowStale: true })) {\n      const v = this.valList[index]\n      if (this.isBackgroundFetch(v)) {\n        v.__abortController.abort(new Error('deleted'))\n      } else {\n        const k = this.keyList[index]\n        this.dispose(v, k, 'delete')\n        if (this.disposeAfter) {\n          this.disposed.push([v, k, 'delete'])\n        }\n      }\n    }\n\n    this.keyMap.clear()\n    this.valList.fill(null)\n    this.keyList.fill(null)\n    if (this.ttls) {\n      this.ttls.fill(0)\n      this.starts.fill(0)\n    }\n    if (this.sizes) {\n      this.sizes.fill(0)\n    }\n    this.head = 0\n    this.tail = 0\n    this.initialFill = 1\n    this.free.length = 0\n    this.calculatedSize = 0\n    this.size = 0\n    if (this.disposed) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n  }\n\n  get reset() {\n    deprecatedMethod('reset', 'clear')\n    return this.clear\n  }\n\n  get length() {\n    deprecatedProperty('length', 'size')\n    return this.size\n  }\n\n  static get AbortController() {\n    return AC\n  }\n  static get AbortSignal() {\n    return AS\n  }\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lru-cache/index.js\n");

/***/ })

};
;