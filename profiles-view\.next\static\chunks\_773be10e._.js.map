{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/lib/profileStatus.ts"], "sourcesContent": ["\"use client\";\n\n// Cache for profile status to avoid repeated fetches\nlet profileStatusCache: Record<string, {\n  status: string;\n  isApproved: boolean;\n  address?: string;\n  name?: string;\n  transactionHash?: string;\n  expiryDate?: Date;\n}> = {};\n\n/**\n * Fetch profile status from the database\n * @param addressOrName Address or name of the profile to check\n * @returns Promise with profile status\n */\nexport async function checkProfileStatus(addressOrName: string): Promise<{\n  status: string;\n  isApproved: boolean;\n  message: string;\n  address?: string;\n  name?: string;\n  transactionHash?: string;\n  expiryDate?: Date;\n}> {\n  // Return from cache if available\n  if (profileStatusCache[addressOrName]) {\n    const cached = profileStatusCache[addressOrName];\n    return {\n      ...cached,\n      message: getStatusMessage(cached.status)\n    };\n  }\n\n  try {\n    // Fetch profile status from API\n    const response = await fetch(`/api/profile/check-status?identifier=${addressOrName}`);\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch profile status: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n\n    // Cache the result\n    profileStatusCache[addressOrName] = {\n      status: data.status,\n      isApproved: data.status === 'approved',\n      address: data.address,\n      name: data.name,\n      transactionHash: data.transactionHash,\n      expiryDate: data.expiryDate\n    };\n\n    return {\n      status: data.status,\n      isApproved: data.status === 'approved',\n      message: getStatusMessage(data.status),\n      address: data.address,\n      name: data.name,\n      transactionHash: data.transactionHash,\n      expiryDate: data.expiryDate\n    };\n  } catch (error) {\n    console.error(`Error fetching profile status for ${addressOrName}:`, error);\n\n    // Return default values on error\n    return {\n      status: 'error',\n      isApproved: false,\n      message: 'Error checking profile status. Please try again later.'\n    };\n  }\n}\n\n/**\n * Get a user-friendly message based on profile status\n */\nfunction getStatusMessage(status: string): string {\n  switch (status) {\n    case 'approved':\n      return 'Profile is approved and accessible.';\n    case 'new':\n      return 'This profile is new and waiting for approval. Please burn tokens and provide a transaction hash for verification.';\n    case 'in-progress':\n      return 'This profile is being processed. Please check back later.';\n    case 'pending':\n      return 'This profile is pending approval. Please check back later.';\n    case 'deleted':\n      return 'This profile has been deleted.';\n    case 'expired':\n      return 'This profile has expired. Please contact an admin to renew it.';\n    default:\n      return 'Profile status is unknown.';\n  }\n}\n\n/**\n * Clear the profile status cache\n */\nexport function clearProfileStatusCache() {\n  profileStatusCache = {};\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA,qDAAqD;AACrD,IAAI,qBAOC,CAAC;AAOC,eAAe,mBAAmB,aAAqB;IAS5D,iCAAiC;IACjC,IAAI,kBAAkB,CAAC,cAAc,EAAE;QACrC,MAAM,SAAS,kBAAkB,CAAC,cAAc;QAChD,OAAO;YACL,GAAG,MAAM;YACT,SAAS,iBAAiB,OAAO,MAAM;QACzC;IACF;IAEA,IAAI;QACF,gCAAgC;QAChC,MAAM,WAAW,MAAM,MAAM,CAAC,qCAAqC,EAAE,eAAe;QAEpF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;QAC1E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,mBAAmB;QACnB,kBAAkB,CAAC,cAAc,GAAG;YAClC,QAAQ,KAAK,MAAM;YACnB,YAAY,KAAK,MAAM,KAAK;YAC5B,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI;YACf,iBAAiB,KAAK,eAAe;YACrC,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YACL,QAAQ,KAAK,MAAM;YACnB,YAAY,KAAK,MAAM,KAAK;YAC5B,SAAS,iBAAiB,KAAK,MAAM;YACrC,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI;YACf,iBAAiB,KAAK,eAAe;YACrC,YAAY,KAAK,UAAU;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,cAAc,CAAC,CAAC,EAAE;QAErE,iCAAiC;QACjC,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,SAAS;QACX;IACF;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,MAAc;IACtC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS;IACd,qBAAqB,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/app/%5Bname%5D/ClientPage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { Loader2 } from 'lucide-react';\nimport { useAppKitAccount } from '@reown/appkit/react';\nimport { useMetadata } from '@/app/contexts/MetadataContext';\nimport { checkProfileStatus } from '@/lib/profileStatus';\n\ninterface ProfileData {\n  address: string;\n  name?: string; // Optional for backward compatibility\n  profileName?: string; // New field\n  bio?: string; // Optional for backward compatibility\n  profileBio?: string; // New field\n  chain: string;\n  // socialLinks removed - now stored in componentPositions table for socialLinks component\n  components: {\n    componentType: string;\n    order: string;\n    hidden: string;\n    backgroundColor?: string;\n    profileName?: string;\n    profileBio?: string;\n    details?: any;\n  }[];\n  compPosition?: 'left' | 'center' | 'right';\n}\n\nexport default function ClientPage({ name }: { name: string }) {\n  const pathname = usePathname();\n  const router = useRouter();\n  const { address } = useAppKitAccount();\n  const [profileData, setProfileData] = useState<ProfileData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [statusChecked, setStatusChecked] = useState(false);\n  const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = useMetadata();\n  const [bannerMetadata, setBannerMetadata] = useState<any>(null);\n  const [profilePictureMetadata, setProfilePictureMetadata] = useState<any>(null);\n  const [bannerPfpMetadata, setBannerPfpMetadata] = useState<any>(null);\n\n  // First check profile status\n  useEffect(() => {\n    async function checkStatus() {\n      try {\n        console.log('[ClientPage] Checking status for:', name);\n        const statusResult = await checkProfileStatus(name);\n        console.log('[ClientPage] Status result:', statusResult);\n\n        // Check if this is the user's own profile\n        const isOwnProfile = address && statusResult.address &&\n          address.toLowerCase() === statusResult.address.toLowerCase();\n        console.log('[ClientPage] Is own profile:', isOwnProfile);\n\n        // If profile is not approved, redirect to error page\n        // We no longer make an exception for the profile owner\n        if (!statusResult.isApproved && statusResult.status !== 'not-found') {\n          console.log('[ClientPage] Redirecting to error page for status:', statusResult.status);\n          // Use the name parameter instead of address\n          const nameParam = statusResult.name ? `&name=${statusResult.name}` : `&name=${name}`;\n          window.location.href = `/profile-error?status=${statusResult.status}${nameParam}`;\n          return;\n        }\n\n        setStatusChecked(true);\n      } catch (error) {\n        console.error('[ClientPage] Error checking profile status:', error);\n        setStatusChecked(true); // Continue anyway if status check fails\n      }\n    }\n\n    checkStatus();\n  }, [name, address]);\n\n  // Then fetch profile data once status is checked\n  useEffect(() => {\n    if (!statusChecked) return; // Wait until status check is complete\n\n    async function fetchProfileData() {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('Fetching profile for name:', name);\n        console.log('Current pathname:', pathname);\n\n        // Fetch profile data by name\n        // Always treat the parameter as a name, not an address\n        const response = await fetch(`/api/profile/${name}`);\n\n        if (!response.ok) {\n          // Handle errors with more specific messages\n          const errorResponse = await response.json();\n\n          if (response.status === 404) {\n            // For 404 errors, show a user-friendly message\n            if (errorResponse.error === 'No profiles exist in the database yet') {\n              setError('No profiles have been created yet. Be the first to create one!');\n            } else if (errorResponse.error === 'Profile components not properly initialized') {\n              setError('Profile system is not properly initialized. Please contact support.');\n            } else {\n              setError(`Profile \"${name}\" not found`);\n            }\n            setLoading(false);\n            return; // Exit early without throwing an error\n          }\n\n          // For other errors, log and throw\n          console.error('Failed to fetch profile:', errorResponse);\n          throw new Error(errorResponse.error || 'Failed to load profile');\n        }\n\n        const data = await response.json();\n        setProfileData(data);\n\n        // Fetch bannerpfp metadata\n        if (data.address) {\n          try {\n            // Fetch bannerpfp metadata first\n            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);\n            if (bannerPfpMeta) {\n              setBannerPfpMetadata(bannerPfpMeta);\n\n              // Now fetch banner and profile picture metadata which will use the bannerpfp data\n              const bannerMeta = await fetchBannerMetadata(data.address);\n              if (bannerMeta) {\n                setBannerMetadata(bannerMeta);\n              }\n\n              const profilePicMeta = await fetchProfilePictureMetadata(data.address);\n              if (profilePicMeta) {\n                setProfilePictureMetadata(profilePicMeta);\n              }\n            }\n          } catch (metadataError) {\n            console.error('Error fetching component metadata:', metadataError);\n            // Continue showing the profile even if metadata fetch fails\n          }\n        }\n      } catch (err: any) {\n        console.error('Error loading profile:', err);\n        setError(err.message || 'An error occurred while loading the profile');\n      } finally {\n        setLoading(false);\n      }\n    }\n\n    fetchProfileData();\n  }, [name, fetchBannerMetadata, fetchProfilePictureMetadata, statusChecked]);\n\n  return (\n    <main className=\"relative min-h-screen flex flex-col items-center\">\n      <div className=\"w-full max-w-4xl mx-auto px-4 sm:px-6 pt-24 pb-8 z-10 relative\">\n        {loading ? (\n          <div className=\"flex justify-center items-center min-h-[300px]\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-500\" />\n          </div>\n        ) : error ? (\n          <div className=\"bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center\">\n            <h3 className=\"text-red-400 font-medium mb-2\">Error</h3>\n            <p className=\"text-neutral-300 mb-4\">{error}</p>\n            <button\n              onClick={() => window.location.href = '/'}\n              className=\"px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors\"\n            >\n              Go Back\n            </button>\n          </div>\n        ) : profileData ? (\n          <div className=\"space-y-0 border border-neutral-700 overflow-hidden\">\n            <div className=\"p-8 text-center\">\n              <h1 className=\"text-2xl font-bold text-white mb-4\">\n                Profile: {profileData.name || name}\n              </h1>\n              <p className=\"text-neutral-400 mb-4\">\n                Address: {profileData.address}\n              </p>\n              <p className=\"text-neutral-400 mb-4\">\n                Chain: {profileData.chain}\n              </p>\n              <div className=\"text-left\">\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Components:</h3>\n                {profileData.components\n                  .filter(c => c.hidden !== 'Y')\n                  .sort((a, b) => parseInt(a.order) - parseInt(b.order))\n                  .map((component, index) => (\n                    <div key={index} className=\"bg-neutral-800 p-4 mb-2 rounded\">\n                      <p className=\"text-white\">Type: {component.componentType}</p>\n                      <p className=\"text-neutral-400\">Order: {component.order}</p>\n                      {component.profileName && (\n                        <p className=\"text-neutral-400\">Profile Name: {component.profileName}</p>\n                      )}\n                    </div>\n                  ))}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center\">\n            <h3 className=\"text-yellow-400 font-medium mb-2\">Profile Not Found</h3>\n            <p className=\"text-neutral-300 mb-4\">\n              The profile \"{name}\" doesn't exist or has been removed.\n            </p>\n            <button\n              onClick={() => window.location.href = '/'}\n              className=\"px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors\"\n            >\n              Go Back\n            </button>\n          </div>\n        )}\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AA6Be,SAAS,WAAW,EAAE,IAAI,EAAoB;;IAC3D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC/F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEhE,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,eAAe;gBACb,IAAI;oBACF,QAAQ,GAAG,CAAC,qCAAqC;oBACjD,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;oBAC9C,QAAQ,GAAG,CAAC,+BAA+B;oBAE3C,0CAA0C;oBAC1C,MAAM,eAAe,WAAW,aAAa,OAAO,IAClD,QAAQ,WAAW,OAAO,aAAa,OAAO,CAAC,WAAW;oBAC5D,QAAQ,GAAG,CAAC,gCAAgC;oBAE5C,qDAAqD;oBACrD,uDAAuD;oBACvD,IAAI,CAAC,aAAa,UAAU,IAAI,aAAa,MAAM,KAAK,aAAa;wBACnE,QAAQ,GAAG,CAAC,sDAAsD,aAAa,MAAM;wBACrF,4CAA4C;wBAC5C,MAAM,YAAY,aAAa,IAAI,GAAG,CAAC,MAAM,EAAE,aAAa,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM;wBACpF,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,sBAAsB,EAAE,aAAa,MAAM,GAAG,WAAW;wBACjF;oBACF;oBAEA,iBAAiB;gBACnB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+CAA+C;oBAC7D,iBAAiB,OAAO,wCAAwC;gBAClE;YACF;YAEA;QACF;+BAAG;QAAC;QAAM;KAAQ;IAElB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,eAAe,QAAQ,sCAAsC;YAElE,eAAe;gBACb,IAAI;oBACF,WAAW;oBACX,SAAS;oBAET,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,6BAA6B;oBAC7B,uDAAuD;oBACvD,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,MAAM;oBAEnD,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,4CAA4C;wBAC5C,MAAM,gBAAgB,MAAM,SAAS,IAAI;wBAEzC,IAAI,SAAS,MAAM,KAAK,KAAK;4BAC3B,+CAA+C;4BAC/C,IAAI,cAAc,KAAK,KAAK,yCAAyC;gCACnE,SAAS;4BACX,OAAO,IAAI,cAAc,KAAK,KAAK,+CAA+C;gCAChF,SAAS;4BACX,OAAO;gCACL,SAAS,CAAC,SAAS,EAAE,KAAK,WAAW,CAAC;4BACxC;4BACA,WAAW;4BACX,QAAQ,uCAAuC;wBACjD;wBAEA,kCAAkC;wBAClC,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,MAAM,IAAI,MAAM,cAAc,KAAK,IAAI;oBACzC;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,eAAe;oBAEf,2BAA2B;oBAC3B,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,iCAAiC;4BACjC,MAAM,gBAAgB,MAAM,uBAAuB,KAAK,OAAO;4BAC/D,IAAI,eAAe;gCACjB,qBAAqB;gCAErB,kFAAkF;gCAClF,MAAM,aAAa,MAAM,oBAAoB,KAAK,OAAO;gCACzD,IAAI,YAAY;oCACd,kBAAkB;gCACpB;gCAEA,MAAM,iBAAiB,MAAM,4BAA4B,KAAK,OAAO;gCACrE,IAAI,gBAAgB;oCAClB,0BAA0B;gCAC5B;4BACF;wBACF,EAAE,OAAO,eAAe;4BACtB,QAAQ,KAAK,CAAC,sCAAsC;wBACpD,4DAA4D;wBAC9D;oBACF;gBACF,EAAE,OAAO,KAAU;oBACjB,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,SAAS,IAAI,OAAO,IAAI;gBAC1B,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;+BAAG;QAAC;QAAM;QAAqB;QAA6B;KAAc;IAE1E,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;sBACZ,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;uBAEnB,sBACF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;kCACtC,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACtC,WAAU;kCACX;;;;;;;;;;;uBAID,4BACF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAqC;gCACvC,YAAY,IAAI,IAAI;;;;;;;sCAEhC,6LAAC;4BAAE,WAAU;;gCAAwB;gCACzB,YAAY,OAAO;;;;;;;sCAE/B,6LAAC;4BAAE,WAAU;;gCAAwB;gCAC3B,YAAY,KAAK;;;;;;;sCAE3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;gCACrD,YAAY,UAAU,CACpB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KACzB,IAAI,CAAC,CAAC,GAAG,IAAM,SAAS,EAAE,KAAK,IAAI,SAAS,EAAE,KAAK,GACnD,GAAG,CAAC,CAAC,WAAW,sBACf,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAE,WAAU;;oDAAa;oDAAO,UAAU,aAAa;;;;;;;0DACxD,6LAAC;gDAAE,WAAU;;oDAAmB;oDAAQ,UAAU,KAAK;;;;;;;4CACtD,UAAU,WAAW,kBACpB,6LAAC;gDAAE,WAAU;;oDAAmB;oDAAe,UAAU,WAAW;;;;;;;;uCAJ9D;;;;;;;;;;;;;;;;;;;;;qCAYpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;;4BAAwB;4BACrB;4BAAK;;;;;;;kCAErB,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACtC,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAQb;GA1LwB;;QACL,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACJ,sLAAA,CAAA,mBAAgB;QAKiD,sIAAA,CAAA,cAAW;;;KAR1E", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,oBAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,WAAY,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AAAR,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,GAAA,CAAA,CAAA,CAAG,WAAY,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA,CAAA;AAC/D,CAAA,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,GAAG,CAAA,CACR,IAAK,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA,CAAA;YAC5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA;QACT,CAAA;IACF,CAAA;AACF,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) => {\n    return createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    );\n  },\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACX,CACE,CAAA,CACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,CAAA,CACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,CAAA,CACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,CAAA,CACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,GAAG,CAAA,CAAA,CAAA,CAAA,EAAA,EAEL,GACG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,yKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA;QACE,CAAA,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oLAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA,CAAA;QAC/D,GAAG,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,CAAA,CACA,CAAA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA;WACvD,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;YAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;SAAA;KACpD;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,CAAA,CAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CACJ,CAAA;IAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAc,eAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA;IAEtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AACT,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file://C%3A/WebPages/Web3Socials/profiles-view/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}