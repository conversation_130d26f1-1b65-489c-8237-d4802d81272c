'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';
import { ExternalLinkIcon, Loader2, AlertTriangle } from 'lucide-react';
import Image from 'next/image';
import { fetchUserStatus } from '@/lib/userStatus';

interface UserStatus {
  status: string;
  isApproved: boolean;
  expiryDate?: string;
  transactionHash?: string;
  message?: string;
}

interface WalletValidatorProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
}

export default function WalletValidator({
  children,
  fallbackContent
}: WalletValidatorProps) {
  const { isConnected, address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const pathname = usePathname();
  const [isEnsuringDefaults, setIsEnsuringDefaults] = useState(false);
  const [defaultsEnsured, setDefaultsEnsured] = useState(false);
  const [userStatus, setUserStatus] = useState<UserStatus | null>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Check if we're on a page where status checking should be bypassed
  const shouldBypassStatusCheck =
    pathname?.startsWith('/create') ||
    pathname?.startsWith('/edit') ||
    pathname?.startsWith('/view') ||
    pathname?.startsWith('/layout') ||
    pathname?.startsWith('/discover');

  // Handle wallet initialization state
  useEffect(() => {
    // Give the wallet some time to initialize
    const timer = setTimeout(() => {
      setIsInitializing(false);
    }, 1000); // Wait 1 second for wallet to initialize

    return () => clearTimeout(timer);
  }, []);

  // Fetch user status when address changes
  useEffect(() => {
    const getUserStatus = async () => {
      if (!isConnected || !address) {
        setUserStatus(null);
        return;
      }

      try {
        setIsLoadingStatus(true);
        const status = await fetchUserStatus(address);
        setUserStatus(status);
      } catch (error: any) {
        console.error('Error fetching user status:', error);

        // Check for database unavailable message
        if (error.message && error.message.includes('Database is currently unavailable')) {
          setUserStatus({
            status: 'database_unavailable',
            isApproved: false,
            message: 'Database is currently unavailable. Please try again later.'
          });
        } else {
          setUserStatus({
            status: 'error',
            isApproved: false,
            message: error.message || 'Failed to fetch user status'
          });
        }
      } finally {
        setIsLoadingStatus(false);
      }
    };

    getUserStatus();
  }, [isConnected, address]);



  // Ensure default profile and component positions when user connects wallet
  useEffect(() => {
    const ensureDefaults = async () => {
      if (!isConnected || !address || !chainId || defaultsEnsured || isEnsuringDefaults) {
        return;
      }

      try {
        setIsEnsuringDefaults(true);
        console.log(`Ensuring defaults for address: ${address} on chain: ${chainId}`);

        const response = await fetch(`/api/ensure-defaults/${address}?chain=${chainId.toString()}`);

        if (!response.ok) {
          console.error('Failed to ensure defaults:', await response.text());
          return;
        }

        const data = await response.json();
        console.log('Defaults ensured:', data);

        setDefaultsEnsured(true);
      } catch (error) {
        console.error('Error ensuring defaults:', error);
      } finally {
        setIsEnsuringDefaults(false);
      }
    };

    ensureDefaults();
  }, [isConnected, address, chainId, defaultsEnsured, isEnsuringDefaults]);

  const isUserApproved = () => {
    // If we're on a page where status checking should be bypassed, always return true
    if (shouldBypassStatusCheck) {
      console.log("Bypassing status check for this page");
      return true;
    }

    if (!isConnected || !address) {
      return false;
    }

    // If user status is still loading, wait
    if (isLoadingStatus) {
      console.log("Waiting for user status to load...");
      return false;
    }

    // If no user status, user is not approved
    if (!userStatus) {
      console.log("No user status found");
      return false;
    }

    // Check if user is approved based on status
    const isApproved = userStatus.status === 'approved';
    console.log(`User status: ${userStatus.status}, isApproved: ${isApproved}`);
    return isApproved;
  };

  const hasApprovedStatus = isUserApproved();

  // Empty fallback content
  const emptyFallbackContent = (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="flex flex-col items-center text-center mb-8">
          <div className="relative w-24 h-24 rounded-full overflow-hidden mb-4">
            <Image
              src="/pfp.jpg"
              alt="Web3Tools Profile"
              width={96}
              height={96}
              className="object-cover"
            />
          </div>
          <h1 className="text-2xl font-bold mt-4">Wallet Connection Required</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 max-w-2xl">
            Please connect your wallet and ensure you're on a supported network to continue using Web3Socials.
          </p>
          <div className="mt-4 text-xs text-gray-500 dark:text-gray-500">
            <p>Connected: {String(isConnected)}</p>
            <p>Address: {address ? 'Yes' : 'No'}</p>
            <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {isInitializing ? (
        // Show loading state during wallet initialization
        <div className="flex flex-col items-center justify-center min-h-[300px] p-6 bg-black/30 backdrop-blur-md rounded-xl border border-neutral-800">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
          <p className="text-neutral-300">Initializing...</p>
        </div>
      ) : isEnsuringDefaults ? (
        <div className="flex flex-col items-center justify-center min-h-[300px] p-6 bg-black/30 backdrop-blur-md rounded-xl border border-neutral-800">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
          <p className="text-neutral-300">Setting up your profile...</p>
        </div>
      ) : !isConnected || !address || !chainId ? (
        // Show wallet connection message only after initialization is complete
        fallbackContent || emptyFallbackContent
      ) : !hasApprovedStatus && !shouldBypassStatusCheck ? (
        // Show profile status message when connected but not approved
        <div className="container max-w-4xl mx-auto py-12 px-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
            <div className="flex flex-col items-center text-center mb-8">
              <div className="relative w-24 h-24 rounded-full overflow-hidden mb-4 flex items-center justify-center">
                <AlertTriangle className="h-16 w-16 text-yellow-500" />
              </div>
              <h1 className="text-2xl font-bold mt-4">Profile Status: {userStatus?.status || "new"}</h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 max-w-2xl">
                {isLoadingStatus ? "Checking your profile status..." : (
                  userStatus?.status === 'new' ? "Your profile is new and needs to be approved by an admin." :
                  userStatus?.status === 'in-progress' ? "Your profile is being processed. Please check back later." :
                  userStatus?.status === 'pending' ? "Your profile is pending approval. Please check back later." :
                  userStatus?.status === 'deleted' ? "Your profile has been deleted. Please contact an admin for assistance." :
                  userStatus?.status === 'expired' ? "Your profile has expired. Please contact an admin to renew it." :
                  userStatus?.status === 'database_unavailable' ? (userStatus.message || 'Database is currently unavailable. Please try again later.') :
                  "Your profile status is unknown. Please contact an admin for assistance."
                )}
              </p>
              {userStatus?.transactionHash && (
                <p className="mt-4">
                  Transaction: <a
                    href={`https://cronoscan.com/tx/${userStatus.transactionHash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline inline-flex items-center"
                  >
                    {userStatus.transactionHash.substring(0, 6)}...{userStatus.transactionHash.substring(userStatus.transactionHash.length - 4)}
                    <ExternalLinkIcon className="h-4 w-4 ml-1" />
                  </a>
                </p>
              )}
              <a
                href="https://x.com/Web3Tools_fun"
                target="_blank"
                rel="noopener noreferrer"
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <ExternalLinkIcon className="h-4 w-4 mr-2" />
                Contact us on X
              </a>
            </div>
          </div>
        </div>
      ) : (
        // Show children when connected and approved
        children
      )}
    </>
  );
}
