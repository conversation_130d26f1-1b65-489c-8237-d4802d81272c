"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/derive-valtio";
exports.ids = ["vendor-chunks/derive-valtio"];
exports.modules = {

/***/ "(ssr)/./node_modules/derive-valtio/dist/index.modern.js":
/*!*********************************************************!*\
  !*** ./node_modules/derive-valtio/dist/index.modern.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   derive: () => (/* binding */ f),\n/* harmony export */   underive: () => (/* binding */ u),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/./node_modules/valtio/esm/vanilla.mjs\");\nconst o=new WeakMap,r=new WeakMap,s=(e,t)=>{const n=o.get(e);n&&(n[0].forEach(t=>{const{d:n}=t;e!==n&&s(n)}),++n[2],t&&n[3].add(t))},l=e=>{const t=o.get(e);t&&(--t[2],t[2]||(t[3].forEach(e=>e()),t[3].clear()),t[0].forEach(t=>{const{d:n}=t;e!==n&&l(n)}))},c=e=>{const{s:n,d:c}=e;let a=r.get(c);a||(a=[new Set],r.set(e.d,a)),a[0].add(e);let d=o.get(n);if(!d){const e=new Set,r=(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(n,t=>{e.forEach(e=>{const{d:o,c:r,n:c,i:a}=e;n===o&&t.every(e=>1===e[1].length&&a.includes(e[1][0]))||e.p||(s(n,r),c?l(n):e.p=Promise.resolve().then(()=>{delete e.p,l(n)}))})},!0);d=[e,r,0,new Set],o.set(n,d)}d[0].add(e)},a=e=>{const{s:t,d:n}=e,s=r.get(n);null==s||s[0].delete(e),0===(null==s?void 0:s[0].size)&&r.delete(n);const l=o.get(t);if(l){const[n,r]=l;n.delete(e),n.size||(r(),o.delete(t))}},d=e=>{const t=r.get(e);return t?Array.from(t[0]):[]},i={add:c,remove:a,list:d};function f(t,r){const s=(null==r?void 0:r.proxy)||(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({}),l=!(null==r||!r.sync),d=Object.keys(t);return d.forEach(e=>{if(Object.getOwnPropertyDescriptor(s,e))throw new Error(\"object property already defined\");const r=t[e];let i=null;const f=()=>{if(i){if(Array.from(i).map(([e])=>((e,t)=>{const n=o.get(e);return!(null==n||!n[2]||(n[3].add(t),0))})(e,f)).some(e=>e))return;if(Array.from(i).every(([e,t])=>(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.getVersion)(e)===t.v))return}const t=new Map,u=r(e=>(t.set(e,{v:(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.getVersion)(e)}),e)),p=()=>{var n;t.forEach((t,n)=>{var o;const r=null==(o=i)||null==(o=o.get(n))?void 0:o.s;if(r)t.s=r;else{const o={s:n,d:s,k:e,c:f,n:l,i:d};c(o),t.s=o}}),null==(n=i)||n.forEach((e,n)=>{!t.has(n)&&e.s&&a(e.s)}),i=t};u instanceof Promise?u.finally(p):p(),s[e]=u};f()}),s}function u(e,t){const n=null!=t&&t.delete?new Set:null;d(e).forEach(e=>{const{k:o}=e;null!=t&&t.keys&&!t.keys.includes(o)||(a(e),n&&n.add(o))}),n&&n.forEach(t=>{delete e[t]})}\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/derive-valtio/dist/index.modern.js\n");

/***/ })

};
;