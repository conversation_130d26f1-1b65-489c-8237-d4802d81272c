"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_app-store_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appStoreSvg: () => (/* binding */ appStoreSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst appStoreSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `\n<svg width=\"36\" height=\"36\">\n  <path\n    d=\"M28.724 0H7.271A7.269 7.269 0 0 0 0 7.272v21.46A7.268 7.268 0 0 0 7.271 36H28.73A7.272 7.272 0 0 0 36 28.728V7.272A7.275 7.275 0 0 0 28.724 0Z\"\n    fill=\"url(#a)\"\n  />\n  <path\n    d=\"m17.845 8.271.729-1.26a1.64 1.64 0 1 1 2.843 1.638l-7.023 12.159h5.08c1.646 0 2.569 1.935 1.853 3.276H6.434a1.632 1.632 0 0 1-1.638-1.638c0-.909.73-1.638 1.638-1.638h4.176l5.345-9.265-1.67-2.898a1.642 1.642 0 0 1 2.844-1.638l.716 1.264Zm-6.317 17.5-1.575 2.732a1.64 1.64 0 1 1-2.844-1.638l1.17-2.025c1.323-.41 2.398-.095 3.249.931Zm13.56-4.954h4.262c.909 0 1.638.729 1.638 1.638 0 .909-.73 1.638-1.638 1.638h-2.367l1.597 2.772c.45.788.185 1.782-.602 2.241a1.642 1.642 0 0 1-2.241-.603c-2.69-4.666-4.711-8.159-6.052-10.485-1.372-2.367-.391-4.743.576-5.549 1.075 1.846 2.682 4.631 4.828 8.348Z\"\n    fill=\"#fff\"\n  />\n  <defs>\n    <linearGradient id=\"a\" x1=\"18\" y1=\"0\" x2=\"18\" y2=\"36\" gradientUnits=\"userSpaceOnUse\">\n      <stop stop-color=\"#18BFFB\" />\n      <stop offset=\"1\" stop-color=\"#2072F3\" />\n    </linearGradient>\n  </defs>\n</svg>`;\n//# sourceMappingURL=app-store.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js\n"));

/***/ })

}]);