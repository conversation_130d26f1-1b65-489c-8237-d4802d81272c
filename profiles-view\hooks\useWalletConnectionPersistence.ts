'use client';

import { useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';

/**
 * Simplified hook to log wallet connection status for debugging
 * AppKit handles connection persistence automatically
 */
export function useWalletConnectionPersistence() {
  const { isConnected, address } = useAppKitAccount();
  const pathname = usePathname();

  // Log connection status for debugging only
  useEffect(() => {
    console.log('[Wallet Connection] Path changed to:', pathname);
    console.log('[Wallet Connection] isConnected:', isConnected);
    console.log('[Wallet Connection] address:', address);
  }, [pathname, isConnected, address]);

  return { isConnected, address };
}
