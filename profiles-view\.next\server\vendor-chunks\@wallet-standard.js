"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wallet-standard";
exports.ids = ["vendor-chunks/@wallet-standard"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wallet-standard/app/lib/esm/wallets.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wallet-standard/app/lib/esm/wallets.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEPRECATED_getWallets: () => (/* binding */ DEPRECATED_getWallets),\n/* harmony export */   getWallets: () => (/* binding */ getWallets)\n/* harmony export */ });\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _AppReadyEvent_detail;\nlet wallets = undefined;\nconst registeredWalletsSet = new Set();\nfunction addRegisteredWallet(wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.add(wallet);\n}\nfunction removeRegisteredWallet(wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.delete(wallet);\n}\nconst listeners = {};\n/**\n * Get an API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * When called for the first time --\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowAppReadyEvent} to notify each Wallet that the app is ready\n * to register it.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to listen for a notification\n * from each Wallet that the Wallet is ready to be registered by the app.\n *\n * This combination of event dispatch and listener guarantees that each Wallet will be registered synchronously as soon\n * as the app is ready whether the app loads before or after each Wallet.\n *\n * @return API for getting, listening for, and registering Wallets.\n *\n * @group App\n */\nfunction getWallets() {\n    if (wallets)\n        return wallets;\n    wallets = Object.freeze({ register, get, on });\n    if (typeof window === 'undefined')\n        return wallets;\n    const api = Object.freeze({ register });\n    try {\n        window.addEventListener('wallet-standard:register-wallet', ({ detail: callback }) => callback(api));\n    }\n    catch (error) {\n        console.error('wallet-standard:register-wallet event listener could not be added\\n', error);\n    }\n    try {\n        window.dispatchEvent(new AppReadyEvent(api));\n    }\n    catch (error) {\n        console.error('wallet-standard:app-ready event could not be dispatched\\n', error);\n    }\n    return wallets;\n}\nfunction register(...wallets) {\n    // Filter out wallets that have already been registered.\n    // This prevents the same wallet from being registered twice, but it also prevents wallets from being\n    // unregistered by reusing a reference to the wallet to obtain the unregister function for it.\n    wallets = wallets.filter((wallet) => !registeredWalletsSet.has(wallet));\n    // If there are no new wallets to register, just return a no-op unregister function.\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    if (!wallets.length)\n        return () => { };\n    wallets.forEach((wallet) => addRegisteredWallet(wallet));\n    listeners['register']?.forEach((listener) => guard(() => listener(...wallets)));\n    // Return a function that unregisters the registered wallets.\n    return function unregister() {\n        wallets.forEach((wallet) => removeRegisteredWallet(wallet));\n        listeners['unregister']?.forEach((listener) => guard(() => listener(...wallets)));\n    };\n}\nlet cachedWalletsArray;\nfunction get() {\n    if (!cachedWalletsArray) {\n        cachedWalletsArray = [...registeredWalletsSet];\n    }\n    return cachedWalletsArray;\n}\nfunction on(event, listener) {\n    listeners[event]?.push(listener) || (listeners[event] = [listener]);\n    // Return a function that removes the event listener.\n    return function off() {\n        listeners[event] = listeners[event]?.filter((existingListener) => listener !== existingListener);\n    };\n}\nfunction guard(callback) {\n    try {\n        callback();\n    }\n    catch (error) {\n        console.error(error);\n    }\n}\nclass AppReadyEvent extends Event {\n    get detail() {\n        return __classPrivateFieldGet(this, _AppReadyEvent_detail, \"f\");\n    }\n    get type() {\n        return 'wallet-standard:app-ready';\n    }\n    constructor(api) {\n        super('wallet-standard:app-ready', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        _AppReadyEvent_detail.set(this, void 0);\n        __classPrivateFieldSet(this, _AppReadyEvent_detail, api, \"f\");\n    }\n    /** @deprecated */\n    preventDefault() {\n        throw new Error('preventDefault cannot be called');\n    }\n    /** @deprecated */\n    stopImmediatePropagation() {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n    /** @deprecated */\n    stopPropagation() {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n_AppReadyEvent_detail = new WeakMap();\n/**\n * @deprecated Use {@link getWallets} instead.\n *\n * @group Deprecated\n */\nfunction DEPRECATED_getWallets() {\n    if (wallets)\n        return wallets;\n    wallets = getWallets();\n    if (typeof window === 'undefined')\n        return wallets;\n    const callbacks = window.navigator.wallets || [];\n    if (!Array.isArray(callbacks)) {\n        console.error('window.navigator.wallets is not an array');\n        return wallets;\n    }\n    const { register } = wallets;\n    const push = (...callbacks) => callbacks.forEach((callback) => guard(() => callback({ register })));\n    try {\n        Object.defineProperty(window.navigator, 'wallets', {\n            value: Object.freeze({ push }),\n        });\n    }\n    catch (error) {\n        console.error('window.navigator.wallets could not be set');\n        return wallets;\n    }\n    push(...callbacks);\n    return wallets;\n}\n//# sourceMappingURL=wallets.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/app/lib/esm/wallets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/features/lib/esm/connect.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wallet-standard/features/lib/esm/connect.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connect: () => (/* binding */ Connect),\n/* harmony export */   StandardConnect: () => (/* binding */ StandardConnect)\n/* harmony export */ });\n/** Name of the feature. */\nconst StandardConnect = 'standard:connect';\n/**\n * @deprecated Use {@link StandardConnect} instead.\n *\n * @group Deprecated\n */\nconst Connect = StandardConnect;\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGxldC1zdGFuZGFyZC9mZWF0dXJlcy9saWIvZXNtL2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQSxvQkFBb0IsdUJBQXVCO0FBQzNDO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhbGxldC1zdGFuZGFyZFxcZmVhdHVyZXNcXGxpYlxcZXNtXFxjb25uZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKiBOYW1lIG9mIHRoZSBmZWF0dXJlLiAqL1xuZXhwb3J0IGNvbnN0IFN0YW5kYXJkQ29ubmVjdCA9ICdzdGFuZGFyZDpjb25uZWN0Jztcbi8qKlxuICogQGRlcHJlY2F0ZWQgVXNlIHtAbGluayBTdGFuZGFyZENvbm5lY3R9IGluc3RlYWQuXG4gKlxuICogQGdyb3VwIERlcHJlY2F0ZWRcbiAqL1xuZXhwb3J0IGNvbnN0IENvbm5lY3QgPSBTdGFuZGFyZENvbm5lY3Q7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25uZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/features/lib/esm/connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/features/lib/esm/disconnect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wallet-standard/features/lib/esm/disconnect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Disconnect: () => (/* binding */ Disconnect),\n/* harmony export */   StandardDisconnect: () => (/* binding */ StandardDisconnect)\n/* harmony export */ });\n/** Name of the feature. */\nconst StandardDisconnect = 'standard:disconnect';\n/**\n * @deprecated Use {@link StandardDisconnect} instead.\n *\n * @group Deprecated\n */\nconst Disconnect = StandardDisconnect;\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGxldC1zdGFuZGFyZC9mZWF0dXJlcy9saWIvZXNtL2Rpc2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQSxvQkFBb0IsMEJBQTBCO0FBQzlDO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxXZWJQYWdlc1xcV2ViM1NvY2lhbHNcXHByb2ZpbGVzLXZpZXdcXG5vZGVfbW9kdWxlc1xcQHdhbGxldC1zdGFuZGFyZFxcZmVhdHVyZXNcXGxpYlxcZXNtXFxkaXNjb25uZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKiBOYW1lIG9mIHRoZSBmZWF0dXJlLiAqL1xuZXhwb3J0IGNvbnN0IFN0YW5kYXJkRGlzY29ubmVjdCA9ICdzdGFuZGFyZDpkaXNjb25uZWN0Jztcbi8qKlxuICogQGRlcHJlY2F0ZWQgVXNlIHtAbGluayBTdGFuZGFyZERpc2Nvbm5lY3R9IGluc3RlYWQuXG4gKlxuICogQGdyb3VwIERlcHJlY2F0ZWRcbiAqL1xuZXhwb3J0IGNvbnN0IERpc2Nvbm5lY3QgPSBTdGFuZGFyZERpc2Nvbm5lY3Q7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kaXNjb25uZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/features/lib/esm/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wallet-standard/features/lib/esm/events.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wallet-standard/features/lib/esm/events.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Events: () => (/* binding */ Events),\n/* harmony export */   StandardEvents: () => (/* binding */ StandardEvents)\n/* harmony export */ });\n/** Name of the feature. */\nconst StandardEvents = 'standard:events';\n/**\n * @deprecated Use {@link StandardEvents} instead.\n *\n * @group Deprecated\n */\nconst Events = StandardEvents;\n//# sourceMappingURL=events.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGxldC1zdGFuZGFyZC9mZWF0dXJlcy9saWIvZXNtL2V2ZW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiQzpcXFdlYlBhZ2VzXFxXZWIzU29jaWFsc1xccHJvZmlsZXMtdmlld1xcbm9kZV9tb2R1bGVzXFxAd2FsbGV0LXN0YW5kYXJkXFxmZWF0dXJlc1xcbGliXFxlc21cXGV2ZW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogTmFtZSBvZiB0aGUgZmVhdHVyZS4gKi9cbmV4cG9ydCBjb25zdCBTdGFuZGFyZEV2ZW50cyA9ICdzdGFuZGFyZDpldmVudHMnO1xuLyoqXG4gKiBAZGVwcmVjYXRlZCBVc2Uge0BsaW5rIFN0YW5kYXJkRXZlbnRzfSBpbnN0ZWFkLlxuICpcbiAqIEBncm91cCBEZXByZWNhdGVkXG4gKi9cbmV4cG9ydCBjb25zdCBFdmVudHMgPSBTdGFuZGFyZEV2ZW50cztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV2ZW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wallet-standard/features/lib/esm/events.js\n");

/***/ })

};
;