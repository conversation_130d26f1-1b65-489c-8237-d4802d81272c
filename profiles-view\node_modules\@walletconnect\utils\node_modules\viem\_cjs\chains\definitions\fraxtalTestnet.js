"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fraxtalTestnet = void 0;
const chainConfig_js_1 = require("../../op-stack/chainConfig.js");
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const sourceId = 17000;
exports.fraxtalTestnet = (0, defineChain_js_1.defineChain)({
    ...chainConfig_js_1.chainConfig,
    id: 2522,
    name: 'Fraxtal Testnet',
    nativeCurrency: { name: 'Frax Ether', symbol: 'frxETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.testnet.frax.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'fraxscan testnet',
            url: 'https://holesky.fraxscan.com',
            apiUrl: 'https://api-holesky.fraxscan.com/api',
        },
    },
    contracts: {
        ...chainConfig_js_1.chainConfig.contracts,
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 318416,
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
                blockCreated: 318416,
            },
        },
    },
    sourceId,
});
//# sourceMappingURL=fraxtalTestnet.js.map